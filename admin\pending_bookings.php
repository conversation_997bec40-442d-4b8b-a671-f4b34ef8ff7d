<?php require('inc/db_config.php'); require ('inc/essentials.php'); adminLogin(); ?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ADMIN-PENDING BOOKINGS</title>
    <?php require("inc/links.php")?>
</head>
<body class="bg-light">
    <?php require('inc/header.php'); ?>
    
    <div class="container-fluid" id="main-content">
        <div class="row">
            <div class="col-lg-10 ms-auto p-4 overflow-hidden">
                <h3 class="mb-4">PENDING BOOKINGS</h3>
                
                <!-- FEATURES  -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body">
                        
                        <div class="text-end mb-4">
                            <input type="text" oninput="get_bookings(this.value)" class="form-control shadow-none w-25 ms-auto" placeholder="Type to search">
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-hover border" style="min-width:1200px;">
                                <thead>
                                    <tr class="bg-dark text-light">
                                        <th scope="col">S/n</th>
                                        <th scope="col">User Details</th>
                                        <th scope="col">Room Details</th>
                                        <th scope="col">Payment Details</th>
                                        <th scope="col">Action</th>
                                    </tr>
                                </thead>
                                <tbody id="table-data">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
            </div>
        </div>
    </div>
    
    <!-- Payment Proof Modal -->
    <div class="modal fade" id="paymentProofModal" tabindex="-1" aria-labelledby="paymentProofModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="paymentProofModalLabel">Payment Proof</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <img id="paymentProofImg" src="" class="img-fluid w-100" alt="Payment Proof">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
    
    <?php require ('inc/script.php'); ?>
    <script src="scripts/pending_bookings.js"></script>
    
    <!-- Add this script to handle payment proof viewing -->
    <script>
        // Refresh the page when the modal is completely hidden
        document.getElementById('paymentProofModal').addEventListener('hidden.bs.modal', function () {
            location.reload();
        });

    // Define the function in the global scope
    window.view_payment_proof = function(image_path) {
        let imgElement = document.getElementById('paymentProofImg');
        if (imgElement) {
            imgElement.src = image_path;
            let modal = new bootstrap.Modal(document.getElementById('paymentProofModal'));
            modal.show();
        } else {
            alert('Error loading payment proof viewer!');
        }
    };
    
    // Also add event delegation as a backup method
    document.addEventListener('click', function(event) {
        const button = event.target.closest('button');
        if (button && button.innerText.includes('View Payment Proof')) {
            // Extract the onclick attribute value
            const onclickAttr = button.getAttribute('onclick');
            if (onclickAttr && onclickAttr.includes('view_payment_proof')) {
                // Parse the image path from the onclick attribute
                const matches = onclickAttr.match(/view_payment_proof\("([^"]+)"\)/);
                if (matches && matches[1]) {
                    const imagePath = matches[1];
                    // Call our defined function
                    window.view_payment_proof(imagePath);
                    
                    // Prevent the original onclick from firing
                    event.preventDefault();
                    event.stopPropagation();
                }
            }
        }
    });
    </script>
</body>
</html>