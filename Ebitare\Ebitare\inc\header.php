<nav id="nav-bar" class="navbar navbar-expand-lg navbar-light bg-white px-lg-3 py-lg-2 shadow-sm sticky-top">
  <div class="container-fluid">
    <a class="navbar-brand me-5 fw-bold fs-3 h-font" href="index.php"><?php $settings_r?></a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
      <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarSupportedContent">
      
    
    <?php 
      if(isset($_SESSION['login']) && $_SESSION['login']==true)
      {

        $path = USERS_IMG_PATH;
        echo<<<data
        <div class="btn-group">
          <button type="button" class="btn btn-outline-dark shadow-none dropdown-toggle " data-bs-toggle="dropdown" data-bs-display="static" aria-expanded="false">
            <img src="$path$_SESSION[uPic]" style="width:25px; height=25px;" class="me-1 rounded-circle">
            $_SESSION[uName]
          </button>
          <ul class="dropdown-menu dropdown-menu-lg-end">
            <li><a class="dropdown-item" href="profile.php">Profile</a></li>
            <li><a class="dropdown-item" href="bookings.php">Bookings</a></li>
            <li><a class="dropdown-item" href="logout.php">Logout</a></li>
          </ul>
        </div>
        data;
      }
      else{
        echo<<<data
         <button type="button" class="btn btn-outline-dark shadow-none me-lg-3 me-2" data-bs-toggle="modal" data-bs-target="#loginModal">
        Login
        </button>
       

        data;
      }
    ?>
        </form>
    </div>
  </div>
</nav>

<!-- Login Form -->

<div class="modal fade" id="loginModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
  <div class="modal-dialog">
     <form id="login-form">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title d-flex align-items-center">
              <i class="bi bi-person-circle fs-3 me-2"></i>User Login</h5>
            <button type="reset" class="btn-close shadow-none" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          
          <div class="modal-body">
            <div class="mb-3">
              <label class="form-label">Email address/Phone Number</label>
              <input type="text" class="form-control shadow-none" name="email_mob" required>
            </div>
            <div class="mb-3">
              <label class="form-label">Password</label>
              <input type="password" class="form-control shadow-none" name="pass" required>
            </div>
            <div class="d-flex align-items-center justify-content-between mb-2">
              <button type="submit" class="btn btn-dark shadow-none"> 
                  LOGIN
              </button>
              <button type="button" class="btn text-secondary text-decoration-none shadow-none p-0 " data-bs-toggle="modal" data-bs-target="#forgotModal"  data-bs-dismiss="modal">
              Forgot Password?
               </button>
            </div>
          </div>      
        </div>
     </form>
  </div>
</div>

<!-- Forgot password Form -->

<div class="modal fade" id="forgotModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
  <div class="modal-dialog">
     <form id="forgot-form">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title d-flex align-items-center">
              <i class="bi bi-person-circle fs-3 me-2"></i>Forgot Password</h5>
          </div>
          <span class="badge rounded-pill bg-light text-dark mb-3 text-wrap lh-base">
            Note: A link will be sent to your email to reset your password.
          </span>          
          <div class="modal-body">
            <div class="mb-3">
              <label class="form-label">Email</label>
              <input type="text" class="form-control shadow-none" name="email" required>
            </div>
            <div class="mb-2 text-end">
            <button type="button" class="btn shadow-none p-0 me-2"   data-bs-toggle="modal" data-bs-target="#loginModal"  data-bs-dismiss="modal">
             CANCEL
               </button>
              <button type="submit" class="btn btn-dark shadow-none"> 
                  SEND LINK
              </button>
            </div>
          </div>      
        </div>
     </form>
  </div>
</div>

<!-- Registration form -->



<div class="modal fade" id="registerModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
  <div class="modal-dialog">
    <form id="register-form">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title d-flex align-items-center">
            <i class="bi bi-person-lines-fill fs-3 me-2"></i>User Registration
          </h5>
          <button type="reset" class="btn-close shadow-none" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>

        <div class="modal-body">
          <span class="badge rounded-pill bg-light text-dark mb-3 text-wrap lh-base">
          Note: After registration, a verification link will be sent to your email. Please click it to verify your Account.
          </span>
          <div class="container-fluid">
            <div class="row">
              <div class="col-md-6 ps-0 mb-3">
                <label class="form-label" >Name</label>
                <input type="text" class="form-control shadow-none"name="name" required>
              </div>
              <div class="col-md-6 p-0 mb-3">
                <label class="form-label">Email address</label>
                <input type="email" class="form-control shadow-none"  name="email" required>
              </div>
              <div class="col-md-6 ps-0 mb-3">
                <label class="form-label">Phone Number</label>
                <input type="number" class="form-control shadow-none"  name="phone" required>
              </div>
              <div class="col-md-6 p-0 mb-3">
                <label class="form-label">Profile Picture</label>
                <input type="file" class="form-control shadow-none"  name="profile" accept=".jpg, .jpeg, .png,.webp" required>
              </div>
              <div class="col-md-12 p-0 mb-3">
                <label class="form-label" >Address</label>
                <textarea class="form-control shadow-none"  name="address" rows="1" required></textarea>
              </div>
              <div class="col-md-6 ps-0 mb-3">
                <label class="form-label" >Password</label>
                <input type="password" class="form-control shadow-none" name="pass" required>
              </div>
              <div class="col-md-6 p-0 mb-3">
                <label class="form-label">Confirm Password</label>
                <input type="password" class="form-control shadow-none" name="cpass" required>
              </div>
              <div class="text-center my-1">
                <button type="submit" class="btn btn-dark shadow-none">REGISTER</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>