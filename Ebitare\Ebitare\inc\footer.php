

<html>
  <head>
    <style>
      .custom-alert{
  position: fixed;
  top:80px;
  right:25px;
  z-index: 9999 !important;
}
      </style>
      <style>
.availability-form{
    margin-top:-50px;
    z-index: 2;
    position:relative;
}
 @media screen and (max-width:575px){
    .availability-form{
        margin-top:25px;
        padding:0 35px;

    }
 }   
    </style>
  </head>
  <!--uncomment this if you want to allow users book without logging in Modal HTML structure (inside header.php) -->
<!-- <div class="modal fade" id="loginModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
  <div class="modal-dialog">
    <form id="login-form">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title d-flex align-items-center">
            <i class="bi bi-person-circle fs-3 me-2"></i>User Login</h5>
          <button type="reset" class="btn-close shadow-none" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="mb-3">
            <label class="form-label">Email address/Phone Number</label>
            <input type="text" class="form-control shadow-none" name="email_mob" required>
          </div>
          <div class="mb-3">
            <label class="form-label">Password</label>
            <input type="password" class="form-control shadow-none" name="pass" required>
          </div>
          <div class="d-flex align-items-center justify-content-between mb-2">
            <button type="submit" class="btn btn-dark shadow-none"> 
                LOGIN
            </button>
            <button type="button" class="btn text-secondary text-decoration-none shadow-none p-0 " data-bs-toggle="modal" data-bs-target="#forgotModal"  data-bs-dismiss="modal">
            Forgot Password?
            </button>
          </div>
        </div>      
      </div>
    </form>
  </div>
</div> -->




<script>

function alert(type, msg, position='body') {
    let bs_class = (type === "success") ? "alert-success" : "alert-danger";

    let element = document.createElement('div');
    element.innerHTML = `
        <div class="alert ${bs_class} alert-dismissible fade show" role="alert">
            <strong class="me-3">${msg}</strong>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;

    if(position=='body'){
      document.body.append(element);
      element.classList.add('custom-alert');
    }
    else{

    document.getElementById(position).appendChild(element);
    }
    // Auto-remove the alert after 5 seconds
    setTimeout(() => {
        element.remove();
    }, 4000);
}

function remAlert(){
  document.getElementByClassName('alert')[0].remove();
}

function setActive()
{
  let navbar = document.getElementById('nav-bar');
  let a_tags  = navbar.getElementsByTagName('a');

  for(i=0; i<a_tags.length; i++){
    let file = a_tags[i].href.split('/').pop();
    let file_name = file.split('.')[0];

    if(document.location.href.indexOf(file_name) >= 0){
      a_tags[i].classList.add('active');
    }
  }
}

let register_form = document.getElementById('register-form');

register_form.addEventListener('submit', (e) => {
  e.preventDefault();

  let data = new FormData()

  data.append('name',register_form.elements['name'].value);
  data.append('email',register_form.elements['email'].value);
  data.append('address',register_form.elements['address'].value);
  data.append('phone',register_form.elements['phone'].value);  
  data.append('pass',register_form.elements['pass'].value);
  data.append('cpass',register_form.elements['cpass'].value);
  data.append('profile',register_form.elements['profile'].files[0]);
  data.append('register','');

  var myModal = document.getElementById('registerModal');
  var modal = bootstrap.Modal.getInstance(myModal) || new bootstrap.Modal(myModal);
  modal.hide();

  // Ensure backdrop is removed
  document.querySelectorAll('.modal-backdrop').forEach(el => el.remove());
        document.body.classList.remove('modal-open');

  let xhr = new XMLHttpRequest();
  xhr.open("POST", "ajax/login_register_crud.php?t=" + new Date().getTime(), true);


  xhr.onload = function() {
    // Log the response to the console for debugging

    // Trim whitespace from the response text (if any)
    var response = this.responseText.trim();

    if (response == 'pass_mismatch') {
        alert('error', "Passwords do not match!");
    } 
    else if (response == 'email_already') {
        alert('error', "Email is already registered!");
    }
    else if (response == 'phone_already') {
        alert('error', "Phone number is already registered!");
    }
    else if (response == 'inv_img') {
        alert('error', "Only JPG,WEBP and PNG images are allowed!");
    }
    else if (response == 'upd_failed') {
        alert('error', "Image Upload failed!");
    }
    else if (response == 'mail_failed') {
        alert('error', "Cannot send confirmation Email! Server down!");
    }
    else if (response == 'ins_failed') {
        alert('error', "Registration failed! Server down!");
    } 
    else {
        alert('success', "Registration Successful. Check your inbox for the confirmation link!");
        register_form.reset();
    }
};
     

  xhr.send(data);
})


let login_form = document.getElementById('login-form');
login_form.addEventListener('submit', (e) => {
  e.preventDefault();

  let data = new FormData()

  data.append('email_mob',login_form.elements['email_mob'].value);
  data.append('pass',login_form.elements['pass'].value);
  data.append('login','');

  var myModal = document.getElementById('loginModal');
  var modal = bootstrap.Modal.getInstance(myModal) || new bootstrap.Modal(myModal);
  modal.hide();

  // Ensure backdrop is removed
  document.querySelectorAll('.modal-backdrop').forEach(el => el.remove());
        document.body.classList.remove('modal-open');

  let xhr = new XMLHttpRequest();
  xhr.open("POST", "ajax/login_register_crud.php?t=" + new Date().getTime(), true);


  xhr.onload = function() {
    // Log the response to the console for debugging

    // Trim whitespace from the response text (if any)
    var response = this.responseText.trim();

    if (response == 'inv_email_mob') {
        alert('error', "Invalid Email or Mobile Number!");
    } 
    else if (response == 'not verified') {
        alert('error', "Email is not verified!");
    }
    else if (response == 'inactive') {
        alert('error', "Account Suspended! Please Contact Admin");
    }
    else if (response == 'invalid_pass') {
        alert('error', "Incorrect Password!");
    } 
    else {
    let fileurl = window.location.href.split('/').pop().split('?').shift(); // Gets the current page name
    if (fileurl == 'room_details.php') {
        // If the current page is 'room_details.php', reload the page (with query params if present)
        window.location.href = window.location.href;
    } else {
        // For any other page, reload the page without the query string (pathname only)
        window.location.href = window.location.pathname + window.location.search;
    }
}

  }

  xhr.send(data);
});

let forgot_form = document.getElementById('forgot-form');

forgot_form.addEventListener('submit', (e) => {
  e.preventDefault();

  let data = new FormData()

  data.append('email',forgot_form.elements['email'].value);
  data.append('forgot_pass','');

  var myModal = document.getElementById('forgotModal');
  var modal = bootstrap.Modal.getInstance(myModal) || new bootstrap.Modal(myModal);
  modal.hide();

  // Ensure backdrop is removed
  document.querySelectorAll('.modal-backdrop').forEach(el => el.remove());
        document.body.classList.remove('modal-open');

  let xhr = new XMLHttpRequest();
  xhr.open("POST", "ajax/login_register_crud.php?t=" + new Date().getTime(), true);

  // xhr.onprogress(){

  // }
  xhr.onload = function() {
    // Log the response to the console for debugging

    // Trim whitespace from the response text (if any)
    var response = this.responseText.trim();

    if (response == 'inv_email') {
        alert('error', "User Email not found!");
    } 
    else if (response == 'not verified') {
        alert('error', "Email is not verified! Contact Admin");
    }
    else if (response == 'inactive') {
        alert('error', "Account Suspended! Please Contact Admin");
    }
    else if (response == 'mail_failed') {
        alert('error', "Cannot send email. Server Down!");
    } 
    else if (response == 'upd_failed') {
        alert('error', "Password reset failed. Server Down!");
    } 
    else {
      alert('success', "Reset link sent to Email!");
      forgot_form.reset();
    }
};
     

  xhr.send(data);
})

// THIS FUNCTION ALLOWS ONLY THOSE  LOGGED IN TO BOOK ROOMS

function checkLoginToBook(status,room_id)
{
  if(status){
    window.location.href='confirm_booking.php?id='+room_id; 
    
  }
  else{
    alert('error', 'Please login to book room');
   }
}

// THIS FUNCTION ALLOWS THOSE  LOGGED IN OR NOT TO BOOK ROOMS ALSO SEE CONFIRM BOOKING FOR ISSET=LOGIN


// function checkLoginToBook(status, room_id) {
//   if (status) {
//     // If the user is logged in, go to booking confirmation
//     window.location.href = 'confirm_booking.php?id=' + room_id;
//   } else {
//     // If the user is not logged in, show a confirmation dialog
//     const userChoice = confirm('You are not logged in. Would you like to log in to book this room, or continue without logging in?');
    
//     if (userChoice) {
//       // Show the login modal using Bootstrap's JavaScript API
//       var myModal = new bootstrap.Modal(document.getElementById('loginModal'));
//       myModal.show();
//     } else {
//       // User chooses to continue without logging in
//       window.location.href = 'confirm_booking.php?id=' + room_id;
//     }
//   }
// }




setActive();

</script>
<?php
  if(isset($_GET['account_recovery']))
  {
    $data = filteration($_GET);

    $t_date = date("Y-m-d");

    $query = select("SELECT * FROM `user_cred` WHERE `email`=? AND `token`=? AND `t_expire`=? LIMIT 1", 
                    [$data['email'], $data['token'], $t_date], 'sss');

    if(mysqli_num_rows($query) == 1)
    {
      echo <<<showModal
      <script>
        var myModal = document.getElementById('recoveryModal');
        myModal.querySelector("input[name='email']").value = '$data[email]';
        myModal.querySelector("input[name='token']").value = '$data[token]';

        var modal = bootstrap.Modal.getOrCreateInstance(myModal); 
        modal.show();
      </script>
    showModal;
    }
    else {
       alert("error","Invalid or Expired Link!");
  }
  
  }
?>


  <script>
    var swiper = new Swiper(".swiper-container", {
      spaceBetween: 30,
      effect: "fade",
      loop:true,
      autoplay:{    
        delay:3000,
        disableOnInteraction:false,
      }

    });
  </script>
  <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>

<script>
    var swiper = new Swiper(".swiper-testimonials", {
      effect: "coverflow",
      grabCursor: true,
      centeredSlides: true,
      slidesPerView: "auto",
      slidesPerView:"3",
      loop: true,
      coverflowEffect: {
        rotate: 50,
        stretch: 0,
        depth: 100,
        modifier: 1,
        slideShadows: false,
      },
      pagination: {
        el: ".swiper-pagination",
      },
      breakpoints: {
        320 : {
          slidesPerView:1,
        },
      
         640 : {
          slidesPerView:1,
        },

      768 : {
        slidesPerView:2,
        },
      1024 : {
        slidesPerView:3,
        },
      }
    });
  </script>

  <!-- function for recovering account -->
  <script>

let recovery_form = document.getElementById('recovery-form');

recovery_form.addEventListener('submit', (e) => {
  e.preventDefault();

  let data = new FormData();

  data.append('email', recovery_form.elements['email'].value);
  data.append('token', recovery_form.elements['token'].value);
  data.append('pass', recovery_form.elements['pass'].value);
  data.append('recover_user', '');

  // Hide the modal after form submission
  var myModal = document.getElementById('recoveryModal');
  var modal = bootstrap.Modal.getInstance(myModal) || new bootstrap.Modal(myModal);
  modal.hide();

  // Remove the backdrop
  document.querySelectorAll('.modal-backdrop').forEach(el => el.remove());
  document.body.classList.remove('modal-open');

  let xhr = new XMLHttpRequest();
  xhr.open("POST", "ajax/login_register_crud.php?t=" + new Date().getTime(), true);

  xhr.onload = function() {
    // Trim whitespace from the response text (if any)
    var response = this.responseText.trim();

    if (response == 'failed') {
      alert("error", "Account reset failed. Server Down!");
    } else {
      alert("success", "Account Reset Successful!");
      recovery_form.reset();
    }
  };

  
  xhr.send(data);
});


  </script>
  <!-- Password Reset Modal-->
<div class="modal fade" id="recoveryModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
  <div class="modal-dialog">
     <form id="recovery-form">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title d-flex align-items-center">
              <i class="bi bi-shield-lock fs-3 me-2"></i>Set up new Password</h5>
          </div>         
          <div class="modal-body">
            <div class="mb-3">
              <label class="form-label">New Password</label>
              <input type="password" class="form-control shadow-none" name="pass" required>
              <input type = "hidden" name ="email">
              <input type = "hidden" name ="token">
            </div>
            <div class="mb-2 text-end">
            <button type="button" class="btn shadow-none me-2" data-bs-dismiss="modal">
             CANCEL
               </button>
              <button type="submit" class="btn btn-dark shadow-none"> 
                  SUBMIT
              </button>
            </div>
          </div>      
        </div>
     </form>
  </div>
</div>
