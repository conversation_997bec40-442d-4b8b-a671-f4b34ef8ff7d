<?php
require('../admin/inc/db_config.php');
require('../admin/inc/essentials.php');

session_start();
date_default_timezone_set("Africa/Lagos");

// Check if user is logged in and room data is available
if(!isset($_SESSION['uId']) || !isset($_SESSION['room'])) {
    header("Location: ../confirm_booking.php");
    exit;
}

// Generate a unique reference ID for the transaction
$reference = "EBITARE_BT_" . uniqid();
$_SESSION['payment_reference'] = $reference;

// Bank account details
$bank_accounts = [
    [
        'bank_name' => 'First Bank',
        'account_name' => 'Ebitare Hotels Ltd',
        'account_number' => '**********'
    ],
    [
        'bank_name' => 'GTBank',
        'account_name' => 'Ebitare Hotels Ltd',
        'account_number' => '**********'
    ]
];

?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="Ebitare">
    <title>EBITARE - Bank Transfer Payment</title>
    
    
    
    <!-- Favicons-->
    <link rel="shortcut icon" href="../img/favicon.ico" type="image/x-icon">
    <link rel="apple-touch-icon" type="image/x-icon" href="../img/apple-touch-icon-57x57-precomposed.png">
    <link rel="apple-touch-icon" type="image/x-icon" sizes="72x72" href="../img/apple-touch-icon-72x72-precomposed.png">
    <link rel="apple-touch-icon" type="image/x-icon" sizes="114x114" href="../img/apple-touch-icon-114x114-precomposed.png">
    <link rel="apple-touch-icon" type="image/x-icon" sizes="144x144" href="../img/apple-touch-icon-144x144-precomposed.png">

    <!-- GOOGLE WEB FONT-->
    <link rel="preconnect" href="https://fonts.googleapis.com/">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Caveat:wght@400;500&amp;family=Montserrat:wght@300;400;500;600;700&amp;display=swap" rel="stylesheet">

    <!-- BASE CSS -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    <link href="../css/style.css" rel="stylesheet">
    <link href="../css/vendors.min.css" rel="stylesheet">

    <!-- YOUR CUSTOM CSS -->
    <link href="../css/custom.css" rel="stylesheet">
    
    <style>
        .payment-details {
            background: #fff;
            border-radius: 5px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 0 15px rgba(0,0,0,0.05);
        }
        
        .reference-box {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #78cfcf;
        }
        
        .account-details {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #78cfcf;
        }
        
        .bank-account {
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px dashed #e1e1e1;
        }
        
        .bank-account:last-child {
            border-bottom: none;
        }
        
        .payment-form input[type="file"] {
            padding: 10px 0;
        }
        
        .payment-form small {
            color: #6c757d;
        }
    </style>
</head>

<body>


<main>

    <!-- /Background Img Parallax -->

    <div class="bg_white" id="first_section">
        <div class="container margin_120_95">
            <div class="row justify-content-between">
                <div class="col-lg-6">
                    <div class="title">
                        <small>Payment Instructions</small>
                        <h2>Bank Transfer Details</h2>
                    </div>
                    
                    <div class="reference-box">
                        <h4>Your Reference ID</h4>
                        <p><strong><?php echo $reference; ?></strong></p>
                        <p>Please include this reference ID in your bank transfer description.</p>
                    </div>
                    
                    <div class="account-details">
                        <h4>Bank Account Details</h4>
                        <?php foreach($bank_accounts as $account): ?>
                            <div class="bank-account">
                                <p><strong>Bank Name:</strong> <?php echo $account['bank_name']; ?></p>
                                <p><strong>Account Name:</strong> <?php echo $account['account_name']; ?></p>
                                <p><strong>Account Number:</strong> <?php echo $account['account_number']; ?></p>
                            </div>
                        <?php endforeach; ?>
                        <p><strong>Amount to Pay:</strong> ₦<?php echo $_SESSION['room']['payment'] ?? 'N/A'; ?></p>
                    </div>
                    
                    <div class="payment-details">
                        <h4>Booking Summary</h4>
                        <p><strong>Room:</strong> <?php echo $_SESSION['room']['name'] ?? 'N/A'; ?></p>
                        <p><strong>Price Per Night:</strong> ₦<?php echo $_SESSION['room']['price'] ?? 'N/A'; ?></p>
                        <p><strong>Check-in Date:</strong> <?php echo $_SESSION['room']['checkin'] ?? 'N/A'; ?></p>
                        <p><strong>Check-out Date:</strong> <?php echo $_SESSION['room']['checkout'] ?? 'N/A'; ?></p>
                        <p><strong>Total Amount:</strong> ₦<?php echo $_SESSION['room']['payment'] ?? 'N/A'; ?></p>
                    </div>
                </div>
                <div class="col-lg-5">
                    <form action="submit_transfer.php" method="POST" enctype="multipart/form-data" class="box_style_1">
                        <div class="title mb-4">
                            <h3>Payment Confirmation</h3>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label class="form-label">Full Name</label>
                                <input type="text" name="sender_name" id="sender_name"
                                    class="form-control shadow-none"
                                    placeholder="Enter your full name"
                                    value="<?php echo htmlspecialchars($_SESSION['user']['name'] ?? ''); ?>"
                                    required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Email</label>
                                <input type="email" name="sender_email" id="sender_email"
                                    class="form-control shadow-none"
                                    placeholder="Enter your email"
                                    value="<?php echo htmlspecialchars($_SESSION['user']['email'] ?? ''); ?>"
                                    required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Phone Number</label>
                                <input type="tel" name="sender_phone" id="sender_phone"
                                    class="form-control shadow-none"
                                    placeholder="Enter your phone number"
                                    value="<?php echo htmlspecialchars($_SESSION['user']['phone'] ?? ''); ?>"
                                    required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Your Account Number</label>
                                <input type="text" name="sender_account_number" id="sender_account_number"
                                    class="form-control shadow-none"
                                    placeholder="Enter your account number"
                                    required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Your Bank Name</label>
                                <input type="text" name="bank_name" id="bank_name"
                                    class="form-control shadow-none"
                                    placeholder="Enter your bank name"
                                    required>
                            </div>
                            
                            <div class="col-md-12 mb-3">
                                <label class="form-label">Payment Screenshot</label>
                                <input type="file" name="screenshot" id="screenshot" 
                                    class="form-control shadow-none"
                                    accept="image/*" required>
                                <small>Upload a screenshot of your payment confirmation</small>
                            </div>
                            
                            <!-- Hidden fields for essential booking data -->
                            <input type="hidden" name="reference" value="<?php echo $reference; ?>">
                            <input type="hidden" name="amount" value="<?php echo $_SESSION['room']['payment'] ?? 0; ?>">
                            <input type="hidden" name="room_id" value="<?php echo $_SESSION['room']['id'] ?? 0; ?>">
                            <input type="hidden" name="user_id" value="<?php echo $_SESSION['uId'] ?? 0; ?>">
                            <input type="hidden" name="checkin" value="<?php echo $_SESSION['room']['checkin'] ?? ''; ?>">
                            <input type="hidden" name="checkout" value="<?php echo $_SESSION['room']['checkout'] ?? ''; ?>">
                            <input type="hidden" name="room_name" value="<?php echo $_SESSION['room']['name'] ?? ''; ?>">
                            
                            <div class="col-md-12 mb-3">
                                <button type="submit" class="btn_1 w-100">Submit Payment Details</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <!-- /row -->
        </div>
        <!-- /container -->
    </div>
    <!-- /bg_white -->
</main>

<footer class="bg-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">© Ebitare Hotel - <?php echo date('Y'); ?></p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="terms.php">Terms and Conditions</a>
                </div>
            </div>
        </div>
    </footer>
<div class="progress-wrap">
    <svg class="progress-circle svg-content" width="100%" height="100%" viewBox="-1 -1 102 102">
        <path d="M50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98"/>
    </svg>
</div>
<!-- /back to top -->

<!-- COMMON SCRIPTS -->
<script src="../js/common_scripts.js"></script>
<script src="../js/common_functions.js"></script>
<script src="../phpmailer/validate.js"></script>

</body>
</html>