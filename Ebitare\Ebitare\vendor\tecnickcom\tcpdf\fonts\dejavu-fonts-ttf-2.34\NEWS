Changes from 2.33 to 2.34
* Sans, SansMono, Serif: unlinked references of U+2596 for bug 50848
  (by <PERSON>)
* <PERSON><PERSON>, <PERSON>s<PERSON><PERSON>, Serif: added U+A7AA (by <PERSON>)
* <PERSON><PERSON>, <PERSON>s<PERSON><PERSON>, Serif: added U+2A6A, U+2A6B, U+2E1F based on U+223B
  (by <PERSON>)
* Sans, Serif: removed superfluous ligature definitions for ffl und ffi
  (bug 55363) (by <PERSON> 薛至峰)
* Sans, Serif: swapped glyphs for U+25D2 and U+25D3 (bug 55197)
  (by <PERSON>g <PERSON> 薛至峰)
* Sans, Serif: added U+A740, U+A741 (by <PERSON>)
* Sans: added U+20BA Turkish Lira sign (by <PERSON>)
* Sans: replaced Georgian Asomtavruli U+10A0-U+10C5 and Mk<PERSON><PERSON>li U+10D0-U+10FC
   with new version (by <PERSON><PERSON><PERSON>)
* Sans: added Georgian Nuskhuri U+2D00-U+U+2D25 (by <PERSON><PERSON><PERSON>)
* Sans: added Private Use Area glyphs for Georgian U+F400-U+F441
  (by <PERSON><PERSON><PERSON>)
* Sans: tweaked U+0250, U+0254 (by <PERSON>ye)
* Sans: adjusted hinting of U+032C-U+032D, avoiding problem on some platforms
  (by <PERSON> Jacquerye)
* Sans: added U+A7A0-U+A7A9, pre-1921 Latvian letters with oblique stroke
  (by Denis Jacquerye)
* Sans: added anchors to U+2C6D (by <PERSON> Jacquerye)
* Sans: added cedilla anchor to some Latin characters (by Denis Jacquerye)
* Sans: added ogonek anchor to A, E, O, U, Y (by Denis Jacquerye)
* Sans: adjusted ogonek reference in U+0172, U+01EA, U+01EB
  (by Denis Jacquerye)
* Sans: added anchors to U+0104, U+0105 (by Denis Jacquerye)
* Sans: added U+1F600, U+1F611, U+1F615, U+1F617, U+1F619, U+1F61B, U+1F61F, 
  U+1F626-U+1F627, U+1F62E-U+1F62F, U+1F634 (by Gee Fung Sit 薛至峰)
* Sans: replaced U+27A1 with mirror image of U+2B05 for consistency
  (by Gee Fung Sit 薛至峰)
* Sans: copied hints from U+14A3, U+14A7 to U+2142-U+2143
  (by Gee Fung Sit 薛至峰)
* Sans: added Lisu block (by Gee Fung Sit 薛至峰)
* Sans: typographical improvements to U+0166-U+0167, U+02A6, U+02AA
  (by Gee Fung Sit 薛至峰)
* Sans: slightly change hinting of "2" to fix bug 37395 (by Ben Laenen)
* Sans: fixed U+1444 which had wrong top dot that shouldn't be there
  (by Denis Jacquerye)
* Sans: added anchors for diacritics to U+01B7, U+01B8, U+01B9, U+0292
  (by Denis Jacquerye)
* Sans: added U+01B7, U+01B8 to context for case diacritics above
* SansMono: fixed U+0574 (by Ruben Hakobian)
* SansMono: added U+2016, U+27C2 (by Yoshiki Ohshima)
* SansMono: added U+02CE, U+02CF (by Denis Jacquerye)
* SansMono: added U+2148, U+27E6-U+27E7, U+2B05-U+2B0D, U+1D55A
  (by Gee Fung Sit 薛至峰)
* Serif: added U+02BA, U+02C2-U+02C5, U+02CA-U+02CB, U+02D7, U+02F3, U+02F7,
  U+046C-U+046D, U+0476-U+0477, U+1D7C-U+1D7F, U+20B8, U+2132, U+214E, U+2C7B
  to Serif (by Gee Fung Sit 薛至峰)
* Serif: typographic improvements to U+0194, U+01B1, U+0263, U+028A, U+02A6,
  U+02A8, U+02AA, U+02E0, U+03DC, U+1D3B, U+1D7B (by Gee Fung Sit 薛至峰)
* Serif: added small cap versions of q, x (in italic styles), delta, theta, xi,
  sigma, phi, omega, not wired in yet (by Gee Fung Sit 薛至峰)
* Serif: added anchors to U+0234-U+0236 (by Gee Fung Sit 薛至峰)
* Serif: added U+02EC, U+02EF, U+02F0, U+0360 (by Denis Jacquerye)

Changes from 2.32 to 2.33

* added Old Italic block to Sans (by MaEr) 
* added U+051E, U+051F to Sans (by MaEr) 
* added U+01BA, U+0372-U+0373, U+0376-U+0377, U+03CF, U+1D00-U+1D01,
  U+1D03-U+1D07, U+1D0A-U+1D13, U+1D15, U+1D18-U+1D1C, U+1D20-U+1D2B,
  U+1D2F, U+1D3D, U+1D5C-U+1D61, U+1D66-U+1D6B, U+1DB8, U+1E9C-U+1E9D,
  U+1EFA-U+1EFB, U+2C60-U+2C61, U+2C63, U+A726-U+A73C, U+A73E-U+A73F,
  U+A746-U+A747, U+A74A-U+A74B, U+A74E+U+A74F, U+A768-U+A769, U+A77B-U+A77C,
  U+A780-U+A787, U+A790-U+A791, U+A7FA-U+A7FF to Serif (by Gee Fung Sit 薛至峰) 
* added alternate forms to U+014A and U+01B7 in Serif (by Gee Fung Sit 薛至峰) 
* typographical improvements to U+0166-U+0167, U+0197, U+01B5-U+01B6, U+01BB,
  U+0222-U+0223, U+023D, U+0250-U+0252, U+026E, U+0274, U+028F, U+029F,
  U+02A3-U+02A5, U+02AB, U+03FE-U+03FF, U+1D02, U+1D14, U+1D1D-U+1D1F, U+1D3B,
  U+1D43-U+1D46, U+1D59, U+1D9B, U+2C71, U+2C73 in Serif (by Gee Fung Sit 薛至峰) 
* fixed bugs #31762 and #34700 plus other small fixes (wrong direction,
  duplicate points, etc.) for Sans and Serif (by Gee Fung Sit 薛至峰) 
* added U+204B to Mono (by Gee Fung Sit 薛至峰) 
* added U+26E2 to Sans (by Gee Fung Sit 薛至峰) 
* added Playing Cards block (U+1F0A0-U+1F0DF) to Sans (by Gee Fung Sit 薛至峰) 
* emoticons in Sans: replace U+2639-U+263B with better versions, add
  U+1F601-U+1F610, U+1F612-U+1F614, U+1F616, U+1F618, U+1F61A, U+1F61C-U+1F61E,
  U+1F620-U+1F624, U+1F625, U+1F628-U+1F62B, U+1F62D, U+1F630-U+1F633,
  U+1F635-U+1F640 (by Ben Laenen and Denis Jacquerye) 
* added U+A78E, U+A790-U+A791 to Sans and Mono (by Denis Jacquerye) 
* added U+A7FA to Sans (by Denis Jacquerye) 
* subscripts: added U+2095-U+209C to Sans, Serif and Mono, adjusted
  U+1D49-U+1D4A in Sans and Mono (by Denis Jacquerye) 
* added U+0243 to Mono (by Denis Jacquerye) 
* adjusted U+0307 to match dot of i, replaced dotaccent U+02D9 with U+0307 in
most dependencies in Sans (by Denis Jacquerye) 
* adjusted anchors of f and added them to long s in Sans (by Denis Jacquerye) 
* added anchors to precomposed dependencies of D and d (by Denis Jacquerye) 
* added debug glyphs U+F002 and U+F003 which will show current point size (by
  Ben Laenen) 
* use correct version for Serbian italic be (by Eugeniy Meshcheryakov) 
* added pictograms U+1F42D-U+1F42E, U+1F431, U+1F435 (by Denis Jacquerye) 
* improved Hebrew in Sans (by Lior Halphon) 
* improved Armenian in Sans, and added Armenian in Serif and Mono (by Rouben
  Hakobian (Tarumian), Aleksey Chalabyan and Norayr Chilingarian) 
* remove "locl" feature for Romanian for S/T/s/t with cedilla/comma accent (by
  Ben Laenen) 
* replace wrong "dflt" script tag in Mono with "DFLT" (by Ben Laenen)

Changes from 2.31 to 2.32

* added to Sans: Latin small letter p with stroke (U+1D7D), Latin capital
  letter p with stroke through descender (U+A750), Latin small letter p with
  stroke through descender (U+A751), Latin capital letter thorn with stroke
  (U+A764), Latin small letter thorn with stroke (U+A765), Latin capital letter
  thorn with stroke through descender (U+A766), Latin small letter thorn with
  stroke through descender (U+A767), Latin capital letter q with stroke through
  descender (U+A756), Latin small letter q with stroke through descender
  (U+A757), Latin capital letter p with flourish (U+A752), Latin small letter p
  with flourish (U+A753) (by Ben Laenen)
* add new Indian rupee symbol (U+20B9) to Sans, Serif and Mono (although
  standardization in Unicode not complete yet, UTC did assign this code point)
  (by Ben Laenen)
* Sans: adjusted U+0E3F, U+20AB, U+20AD-U+20AE, U+20B1, U+20B5, U+20B8 to have
  them take up the same width as digits (by Gee Fung Sit 薛至峰)
* added U+23E8 to Sans (by Thomas Henlich)
* fixed numerous bugs (#22579, #28189, #28977, N'Ko in Windows, fixed U+FB4F,
  anchors for U+0332-U+0333, made extensions in Misc. Technical connect, and
  other small fixes) (by Gee Fung Sit 薛至峰)
* added looptail g as stylistic variant to Serif (by Gee Fung Sit 薛至峰)
* added the remaining precomposed characters in Latin Extended Additional in
  Serif (by Gee Fung Sit 薛至峰)
* added Georgian Mkhedruli (U+10D0-U+10FC) to Sans ExtraLight (by Besarion
  Gugushvili)
* fix spacing in hinting of U+042E (Ю) in Mono (by Ben Laenen)
* replaced U+2650 and minor changes to U+2640-U+2642, U+2699, U+26A2-U+26A5,
  U+26B2-U+26B5, U+26B8 in Sans (by Gee Fung Sit 薛至峰)
* added U+1E9C-U+1E9D, U+1EFA-U+1EFB, U+2028-U+2029, U+20B8, U+2150-U+2152,
  U+2189, U+26C0-U+26C3, U+A722-U+A725, U+1F030-U+1F093 to Sans (by Gee Fung
  Sit 薛至峰)
* added U+1E9C-U+1E9E, U+1EFA-U+1EFB, U+2028-U+2029, U+20B8, U+2181-U+2182,
  U+2185 U+A722-U+A725, to Sans ExtraLight (by Gee Fung Sit 薛至峰)
* added U+20B8, U+22A2-U+22A5, U+A722-U+A725 to Mono (by Gee Fung Sit 薛至峰)
* added U+02CD, U+01BF, U+01F7, U+0222-U+0223, U+0243-U+0244, U+0246-U+024F,
  U+2150-U+2152, U+2189, U+239B-U+23AD and U+A73D to Serif (by Gee Fung Sit
  薛至峰)

Changes from 2.30 to 2.31

* fixed bug where Serif Condensed Italic wouldn't get proper subfamily tags (by
  Ben Laenen)
* added math operators U+2234-U+2237 to Mono (by Ben Laenen)
* removed buggy instructions of U+032D (by Eugeniy Meshcheryakov)
* added U+2C70, U+2C7E, U+2C7F to Sans and Sans Mono (by Denis Jacquerye)
* added U+2C7D to Sans Mono (by Denis Jacquerye)
* added U+2C6D, U+2C70-2C73, U+2C7E-2C7F to Serif (by Denis Jacquerye)
* added extremas to alpha U+03B1 in Serif-Italic (by Denis Jacquerye)
* added U+4A4, U+4A5 to Mono (by Andrey V. Panov)
* added Arabic letters U+0657, U+0670, U+0688-U+0690, U+0693-U+0694,
  U+0696-U+0697, U+0699-U+06A0, U+06A2-U+06A3, U+06A5, U+06A7-U+06A8,
  U+06AA-U+06AE, U+06B0-U+06B4, U+06B6-U+06B9, U+06BB-U+06BE and their
  contextual forms to Sans (by MihailJP)
* added U+A78D LATIN CAPITAL LETTER TURNED H for coming Unicode 6.0 (by Denis
  Jacquerye) 

Changes from 2.29 to 2.30

* added U+0462-U+0463 to Mono (by Denis Jacquerye) 
* corrected U+1E53 in Serif (by Gee Fung Sit) 
* added U+1E4C-U+1E4D to Mono and Serif (by Gee Fung Sit) 
* added U+1E78-U+1E79 to Mono (by Gee Fung Sit) 
* fixed missing diacritics in Latin Extended Additional in Sans ExtraLight
  (moved stacked diacritics out of PUA in the process) (by Gee Fung Sit) 
* fixed anchors on U+1E78 in Serif (by Gee Fung Sit) 
* added U+1DC4-U+1DC9 to Serif (by Denis Jacquerye) 
* renamed above-mark to above-mark in Serif-Italic (by Denis Jacquerye) 
* added U+1DC4-U+1DC9 to context class for dotless substitution (by Denis
  Jacquerye) 
* changed Doubleacute to Doublegrave in Sans ExtraLight (by Gee Fung Sit) 
* removed redundant reference in U+01FB in Sans Oblique (by Gee Fung Sit) 
* added U+A726-U+A727 to Mono (Denis Jacquerye) 
* changed U+04BE and U+04BF according to recommedations of Sasha Ankwab in Sans
  (by Andrey V. Panov) 
* remove "Symbol Charset" from set of codepages in Sans (by Eugeniy
  Meshcheryakov)

Changes from 2.28 to 2.29

* modified U+10FB in Sans to be a mirror image of U+2056, since U+10FB is not
  Georgian-specific (by Roozbeh Pournader)
* added U+2B1F, U+2B24, U+2B53, U+2B54 in Sans (by Roozbeh Pournader)
* fixed TUR opentype language tag to TRK in Serif (bug 19825) (by Ben Laenen)
* early implementation of Abkhaz letter U+0524-U+0525 in Sans
  (by Michael Everson and abysta)
* flipped U+1D538 in Sans (by Gee Fung Sit)
* added U+26B3-U+26B8, U+1D7D8-U+1D7E1 in Sans (by Gee Fung Sit)
* corrected U+1D7A9 in Sans Bold Oblique (by Gee Fung Sit)
* Fixed U+0649 to be dual-joining in Sans Mono (by Roozbeh Pournader)
* Remove unnecessary 'isol' feature from Sans Mono (by Roozbeh Pournader)
* Remove 'cmap' mappings for U+066E, U+066F, U+067C, U+067D, U+0681, U+0682,
  U+0685, U+0692, U+06A1, U+06B5, U+06BA, U+06C6, U+06CE, and U+06D5
  in Sans Mono (bug 20323) (by Roozbeh Pournader)
* add half brackets (U+2E22 - U+2E25, by Steve Tinney) 

Changes from 2.27 to 2.28

* added U+A789, U+A78A in Sans and Sans Mono (by Denis Jacquerye)
* modified U+02D6, U+02D7, U+02EE in Sans and Sans Mono (by Denis Jacquerye)
* added U+1E9E (German capital ß) to Sans and Serif (by Denis Jacquerye)
* adjusted width of U+01B7-U+01B9 in Serif Italic (by Denis Jacquerye)
* modified U+021C, U+021D in Sans (by Denis Jacquerye)
* added U+021C, U+021D in Mono (by Denis Jacquerye)
* added U+F428 (Georgian Nuskhuri "f") in private use area (by Besarion
  Gugushvili)
* updated Georgian mkhedruli (U+10D0-U+10FA) with new version (by Besarion
  Gugushvili)
* updated Georgian asomtavruli (U+10A0-U+10C5) with new version (by Besarion
  Gugushvili)
* added Georgian nuskhuri (U+2D00-U+2D25) (by Besarion Gugushvili)
* added Georgian mtavruli in private use area (U+F400-U+F426) (by Besarion
  Gugushvili)
* added mark anchors above to Cyrillic U+0430-U+0438, U+043A-U+044F,
  U+0454-U+0455 in Mono (by Ben Laenen)
* modified/moved up U+0318-U+0319, U+031C-U+031F, U+0329-U+032A, U+032C-U+032D,
  U+0339-U+033B, U+0348 and U+0353 in Sans to prevent cut-off (by Gee Fung Sit)
* added U+035A to Sans (by Gee Fung Sit)
* updated fontconfig files (by Nicolas Mailhot)
* added U+2032-2037 to Mono (by Denis Jacquerye)
* added Ogham to Sans ExtraLight (by Gee Fung Sit)
* added U+2C6F, U+2C79, U+2C7C-2C7D to Mono (by Gee Fung Sit)
* added U+210F to Serif and Sans ExtraLight (by Gee Fung Sit)
* changed U+210F to a more common glyph in Sans and Mono (by Gee Fung Sit)

Changes from 2.26 to 2.27

* added some of Michael Everson's new Cyrillic glyphs to Sans (by Wesley
  Transue)
* removed blank glyph at U+05EF from Sans Bold Oblique (by Gee Fung Sit)
* small adjustments to existing tone bars in Sans and Mono (by Gee Fung Sit)
* added U+0372-U+0373, U+0376-U+0377, U+03CF, U+A668-U+A66E, U+A708-U+A711,
  U+A71B-U+A71F to Sans (by Gee Fung Sit)
* copied U+02E5-U+02E9 over from Sans to fix inconsistencies in Serif (by Gee
  Fung Sit)
* added U+021C-U+021D, U+0370-U+0371, U+037B-U+037D, U+0470-U+0471,
  U+0510-U+0515, U+051A-U+051D, U+1E9F, U+2C64, U+2C6E-U+2C6F, U+2C79,
  U+2C7C-U+2C7D, U+A644-U+A647, U+A650-U+A651, U+A654-U+A657, U+A708-U+A716,
  U+A71B-U+A71F to Serif (by Gee Fung Sit)
* added U+A708-U+A716, U+A71B-U+A71F to Mono (by Gee Fung Sit)
* added anchors to U+017F (ſ) and fixed U+1E9B (ẛ) in Serif (by Gee Fung Sit)
* made U+0325 smaller in Sans Bold and Serif to match Sans Book (by Gee Fung
  Sit)
* fixes to U+02F3 (moved up), U+228F-U+2294 (more square-like) and
  U+22CE-U+22CF (stroke width) in Sans (by Gee Fung Sit)
* replaced U+2202 ∂ (Sans and Mono) and U+221D ∝, U+221E ∞ (Sans) with glyphs
  from Arev (with small changes) (by Gee Fung Sit)
* added U+22B0-U+22B1, U+22C7, U+22D0-U+22D5 from Arev to Sans to complete the
  block (by Gee Fung Sit)
* added U+0514-U+0515 to Sans ExtraLight (by Gee Fung Sit)
* skewed U+A78C in all Oblique/Italic fonts (by Gee Fung Sit)
* moved U+2215 to U+2044 in Sans and Serif and replaced U+2215 with reference
  to U+002F in all fonts (by Gee Fung Sit)
* added U+2C6E to Mono (by Denis Jacquerye)
* added U+A782 and U+A783 in Sans (by Wesley Transue)
* added U+0244, U+024C-024D, U+2C64 in Sans Mono (by Denis Jacquerye)
* modified U+01AE in Sans Mono (by Denis Jacquerye)
* added U+2C7A to all fonts (by Gee Fung Sit)
* italicized/small changes to U+2C76 in Serif (Bold) Italic (by Gee Fung Sit)
* improved outlines of U+2C68, U+2C6A, U+2C6C in Serif (Bold) Italic (by Gee
  Fung Sit)
* rounded U+2C77 at the bottom in Serif (by Gee Fung Sit)
* added joining behavior for tone letters (U+02E5-U+02E9) in Sans (bug #15669)
  (by Gee Fung Sit)
* fixed outline of y.alt in Sans Regular (by Denis Jacquerye) 
* changed references of U+1D5A8, U+1D5C5, U+1D5DC, U+1D5F9, U+1D610, U+1D62D,
  U+1D644 and U+1D661 to stylistic alternates to have a better distinction (by
  Gee Fung Sit)
* hinted I.alt in Sans Regular (by Gee Fung Sit)
* added U+0487, completing Cyrillic block (by Gee Fung Sit)
* extended the bar of U+0463 to the right and moved the anchor (by Gee Fung
  Sit)
* added anchors to glyphs in Cyrillic block (by Gee Fung Sit)
* added (preliminary) hints to tone letter forms (U+02E5.5, U+02E9.1, stem) in
  Sans Book (by Gee Fung Sit)

Changes from 2.25 to 2.26

- added glyphs for Cyrillic-B to Sans (by Wesley Transue) 
- added U+0370-U+0371 to Sans (by Wesley Transue) 
- added U+019C, U+01A2-U+01A3, U+01A6, U+01E4-U+01E5, U+024C-U+024D, U+0285,
  U+0290, U+02A0, U+0370-U+0371, U+03F1, U+03FC to Sans ExtraLight (by Wesley
  Transue) 
- added U+20A0-U+20A5, U+20A7-U+20B3, U+2105, U+210D, U+210F, U+2115, U+2117,
  U+2119-U+211A, U+211D, U+2124, U+212E, U+2200-U+2204 to Mono (by Heikki
  Lindroos) 
- added U+01BA and U+01BF to Mono (by Heikki Lindroos) 
- merged OpenType "aalt" feature in Latin in Sans (by Denis Jacquerye) 
- added alternative shape for y in Sans (by Denis Jacquerye) 
- added saltillo (U+A78B-U+A78C) to all faces (by James Cloos) 
- changed U+047C-U+047D to references instead of outlines in Sans (by Wesley
  Transue) 
- added Latin letter tresillo U+A72A-U+A72B to Sans (by Wesley Transue) 
- added U+A734-U+A737 to Sans (by Wesley Transue) 
- added U+2053 to Serif and fixed it bug:9425 in Sans (by Gee Fung Sit) 
- removed problematic hints for U+0423 bug:10025 (by Gee Fung Sit) 
- added U+27C5-U+27C6 bug:10255 to all faces (by Gee Fung Sit) 
- fixed width of U+2016 in Sans Oblique (by Gee Fung Sit) 
- added U+2016, U+2032-U+2038, U+2042, U+2045-U+2046, U+204B-U+204F,
  U+2051-U+2052, U+2057 to Serif (by Gee Fung Sit) 
- made U+2140 bigger to match other n-ary operators (by Gee Fung Sit) 
- added U+0606-U+0607, U+0609-U+060A to Sans (by Gee Fung Sit) 
- added U+221B-U+221C to Mono (by Gee Fung Sit) 
- small adjustments to U+221B-U+221C in Sans and Serif (by Gee Fung Sit) 
- update U+04B4-U+04B5 in Serif (by Andrey V. Panov) 
- increased max-storage value from maxp table to 153 (by Andrey V. Panov) 
- added U+0472-U+0473, U+0510-U+0511, U+051A-U+051D, U+0606-U+0607,
  U+0609-U+060A, U+1E26-U+1E27, U+1E54-U+1E55, U+1E7C-U+1E7D, U+1E8C-U+1E8D,
  U+1E90-U+1E91, U+1E97-U+1E99, U+1E9F, U+1EAC-U+1EAD, U+1EB6-U+1EB7,
  U+1EC6-U+1EC7, U+1ED8-U+1EDD, U+1EE0-U+1EE3, U+1EE8-U+1EEB, U+1EEE-U+1EF1 to
  Mono (by Gee Fung Sit) 
- added locl rules for S/T cedilla for Romanian and Moldavian so they get
  rendered as S/T with comma accent (see Redhat bug #455981) (by Ben Laenen) 
- removed ligature rule from Armenian U+0587 bug:16113 (by Gee Fung Sit)

Changes from 2.24 to 2.25

- moved/added U+2E18 (by Gee Fung Sit)
- added empty glyph for U+2064 in Sans and Serif (by Gee Fung Sit)
- added U+22CE-U+22CF to Sans (by Gee Fung Sit)
- Sans Oblique and Bold Oblique, Serif: reverted digits hinting instructions back to before revision 1590, which fixed mistaken debian bug #471024. This fixes Debian bug #411308. The original bug was in freetype not in the fonts (by Denis Jacquerye)
- added U+A726-U+A729, U+A730-U+A733, U+A738-U+A73F, U+A746-U+A74B, U+A74E-U+A74F, U+A780-U+A781, U+A7FB-U+A7FF to Sans (by Gee Fung Sit)
- added Macedonian italic glyph shape for U+0453 in Serif (by Ben Laenen)
- changed descenders in U+0446, U+0449, U+0497, U+04A3, U+04AD (by Andrey V. Panov)
- updated main SFD files to SplineFontDB 3.0 (Denis Jacquerye and Gee Fung Sit)
- moved U+0561 2 up since it wasn't aligned with the baseline well (by Ben Laenen)
- added U+2E2E to Sans (by Gee Fung Sit)
- replaced U+2699 with simpler version in Sans (by Gee Fung Sit)
- added a lot of hinting instructions to Latin Extended B, Greek and Coptic glyphs Sans Book (by Wesley Transue)
- differentiated U+2219 from U+22C5 and adjusted affected references in Sans and Mono (by Gee Fung Sit)
- made Hebrew narrower in Sans Bold and Sans Bold Oblique (by Denis Jacquerye)
- added Kurdish and Chuvash letters from Unicode 5.1 Cyrillic Extended block (by Wesley Transue)
- added U+1E9F, U+A644-U+A647, U+A64C-U+A64D, U+A650-U+A651, U+A654-U+A655, U+A712U+A716 to Sans (by Gee Fung Sit)
- added several glyphs to Sans ExtraLight (by Gee Fung Sit)
- added hinting instructions to U+046A-U+046B, U+0508-U+0509, U+050B, U+0512-U+0513 in Sans Book (by Wesley Transue)
- corrected width of U+027E in Sans Book (by Gee Fung Sit)
- added U+2C79, U+2C7B-U+2C7D to Sans (by Gee Fung Sit)
- added a bunch of glyphs+small corrections to Sans Light (by Gee Fung Sit)
- added U+0496, U+0497, U+04B0, U+04B1 (by Andrey V. Panov)
- updated U+0493, U+049B, U+04B3, U+04B7, U+04F7 (by Andrey V. Panov)
- further improvements in extended Cyrillic (by Andrey V. Panov) 

Changes from 2.23 to 2.24

- instructions for U+05C0 ׀, U+05C3 ׃, U+05F3 ׳, and U+05F4 ״ in DejaVu 
   Sans. (by Wesley Transue)
- instructions for U+2116 in Sans (by Andrey V. Panov)
- Unicode 5.1 update: moved U+F208 to U+2C6D, U+F25F to U+2C71, added 
  U+2C6E-U+2C6F, U+2C72-U+2C73, updated outline of U+2C71 in Sans. (by 
  Denis Jacquerye)
- updated and instructed U+0401 in Sans (by Andrey V. Panov)
- fixed the bug in Sans faces where U+02EC ˬ faced the wrong direction. 
  Also, added a few more glyph instructions. (by Wesley Transue)
- removed OS2Sub and OS2Strike that weren't intentional in Sans 
  ExtraLight. (by Denis Jacquerye)
- updated instructions for U+401, U+44F in Serif Book. (by Andrey V. 
  Panov)
- instructions for U+02C4 ˄, U+02C5 ˅, U+03D8 Ϙ, U+03D9 ϙ, U+0494 Ҕ, and 
  U+0495 ҕ in Sans Book. (by Wesley Transue)
- instructions for U+01A6 Ʀ, U+0238 ȸ, U+0239 ȹ, U+02EC ˬ, and U+05C6 ׆ 
  in Sans Book. (by Wesley Transue)
- DejaVuSans.sfd DejaVuSerif.sfd: updated instructions for U+447 and 
  U+451 using code generated with xgridfit (by Andrey V. Panov)
- instructions for a few glyphs in the Latin Extended-B Block, Greek 
  Block, Cyrillic Block, and N'Ko block. (by Wesley Transue)
- updated sfdnormalize.pl, and SFD files to new SFD format with empty 
  lines. (by Denis Jacquerye) 

Changes from 2.22 to 2.23

- fixed bug which made Condensed fonts appear instead of normal width ones
- added U+20DB, U+20DC, and U+20E1 to Sans (by Roozbeh Pournader)
- added hinting instructions to U+01A7, U+01AA-U+01AC, U+01AE-U+01AF,
  U+01BC-U+01BD, U+01BF, U+01F7, U+0277, U+027F, U+0285-U+0286, U+0297, U+02AF,
  U+02B4-U+02B5, U+02BD, U+030D, U+0311, U+0329, U+04A0-U+04A1 in Sans Book (by
  Wesley Transue)
- modified hinting instructions of U+04A2 in Sans Book (by Wesley Transue)
- added hinting instructions to U+237D, U+2423 in Mono Book and Mono Bold (by
  Wesley Transue)
- added mathematical alphanumeric symbols to all styles (by Max Berger)
- added Unicode 5.1 U+2E18 as U+2E18.u51 (not yet usable) to Sans (by Roozbeh
  Pournader) 
- dereferenced all glyphs with mixed references and outlines (by Denis
  Jacquerye)
- removed non-zero width from U+0344 in Sans (by Denis Jacquerye)

Changes from 2.21 to 2.22

- directory structure has changed, we now use the Makefile
- modified Armenian U+0565 in Sans (by Սահակ Պետրոսյան)
- added double struck letters and numbers U+2102, U+210D, U+2115,
  U+2119-U+211A, U+211D, U+2124, U+213C-U+2140, U+2145-U+2149, U+1D538-U+1D539,
  U+1D53B-U+1D53E, U+1D540-U+1D544, U+1D546, U+1D54A-U+1D550, U+1D552-U+1D56B,
  U+1D7D8-U+1D7E1 to Serif (by Stephen Hartke)
- added letterlike symbols U+2103, U+2109, U+2127, U+214B, U+2141-U+2144 to
  Serif (by Ben Laenen)
- fixed outline direction of U+2143 in Sans Bold/Bold Oblique (by Ben Laenen)
- added arrow set in Serif: arrows: U+2194-U+21FF; dingbats: U+27A1;
  supplemental arrows A: U+27F0-U+27FF; supplemental arrows B: U+2900-U+2975,
  U+297A; miscellaneous symbols and arrows: U+2B00-U+2B11 (by Ben Laenen)
- added U+0180, U+01DE, U+01E0-01E1, U+022A, U+022C, U+0230, U+1E08-U+1E09,
  U+1E10-U+1E11, U+1EB0-U+1EB1 to Mono (by Denis Jacquerye)
- adjusted U+01D5, U+01D7, U+01D9, U+1DB in Mono (by Denis Jacquerye)
- added Ogham in Sans (by Wesley Transue)
- added Yijing Hexagram Symbols in Sans (by Wesley Transue)
- hinting instructions added to Cyrillic U+0460, U+04A6-U+04A7, U+04AC-U+04AD,
  U+04C7-U+04C8, U+04F6-U+04F7, U+04FA-U+04FB, U+050C-U+050D in Sans Book (by
  Wesley Transue)
- adjusted Cyrillic letters U+042A, U+044A, U+044C, U+0459-U+045B, U+0462,
  U+048C-U+048D in Serif (by Andrey V. Panov)
- hinting instructions added to Lao U+0EB7 in Sans (by Wesley Transue)
- added Roman numerals and Claudian letter U+2160-U+2184 in Serif (by Ben
  Laenen)
- added U+FFF9-U+FFFD to Sans, Serif and Mono (by Lars Næsbye Christensen)
- added mathematical symbols to Serif: U+2200, U+2203-U+2204, U+2213-U+2214,
  U+2217-U+2218, U+2223-U+2226, U+2250-U+2255, U+2295-U+22AF, U+22C5 (by Ben
  Laenen)
- modified bullet symbol U+2219 in Serif (by Ben Laenen)

Changes from 2.20 to 2.21

- added U+FE20-U+FE23 (half diacritics) to Sans (by Denis Jacquerye)
- added anchor "half" to position right half of double marks, U+FE21 or U+FE23
  to Sans (by Denis Jacquerye)
- shifted U+0360 up to avoid collision with some outlines in Sans (by Denis
  Jacquerye)
- added anchor above-mark anchor to U+035D, U+035E, U+0360, U+0361 in Sans (by
  Denis Jacquerye)
- added instructions for ff, ffi, ffl ligatures in Serif Bold (by Eugeniy
  Meshcheryakov)
- added instructions to some N'Ko glyphs (by Wesley Transue)
- added instructions to some Lao glyphs (by Wesley Transue)
- cleaning up 'liga' Standard Ligature in Latin, in Sans and Sans Mono (by
  Denis Jacquerye)
- added U+046A, U+046B (big yus) in Serif (by Andrey V. Panov)
- added box drawing symbols to Sans and Serif (by Lars Næsbye Christensen)
- added Makefile to improve font and packages generating (by Nicolas Mailhot)

Changes from 2.19 to 2.20

- removed TeX and TeXData tags from all sfd files (by Eugeniy  Meshcheryakov)
- removed all 'frac' lookups (by Eugeniy  Meshcheryakov)
- fixed duplicate glyph names (by Eugeniy  Meshcheryakov)
- removed standard ligatures with U+00B7 in Mono (by Eugeniy  Meshcheryakov)
- use reference to U+002D in U+00AD in Sans Oblique, and adjust instructions
  (by Eugeniy  Meshcheryakov)
- updated Cyrillic in Sans Extra Light (by Andrey V. Panov)
- added instructions to N'Ko U+07C1-U+07C6, U+07CA, U+07CE-U+07CF, U+07D1,
  U+07D3-U+07D4, U+07D8, U+07DB and U+07EB in Sans (by Wesley Transue)
- added instructions to Lao U+0E8A, U+0E95, U+0E97, U+EA5, U+0EB4 and U+0EB5
  (by Wesley Transue)
- adjusted instructions for Hebrew glyphs (by Denis Jacquerye)
- added instructions for U+0265 in Sans Bold (by Denis Jacquerye)
- fix U+1D68 in Sans: it had the shape of delta, where it should be a rho (by
  Ben Laenen)
- remove U+1D5C glyph in Sans Oblique (it was empty) (by Ben Laenen)
- fix instructions of U+01AD in Sans Bold  (by Ben Laenen)
- fix instructions of U+042D in Serif (by Ben Laenen)
- remove buggy instructions of U+2328 in Serif (by Ben Laenen)
- corrected width of U+2C75-U+2C76 in Sans Bold and Serif Bold (by Gee Fung Sit)
- added U+2C75-U+2C77 to Mono (by Gee Fung Sit)

Changes from 2.18 to 2.19

- fixed misplaced symbols (U+2325,2326) in Sans Oblique (by John Karp) 
- added Mark to Base anchors: 'cedilla' for combining cedilla and
  'above-legacy' for stacking above precomposed glyphs (just a,e,i,o,u with
  macron for now) in Sans (by Denis Jacquerye).
- added contextual substitution for Case and Dotless forms in all Sans variants
  (by Denis Jacquerye).
- renamed 'ccmp' lookups for RTL and Basic (LGC, etc.) (by Denis Jacquerye)
- added anchor 'cedilla' for vowels in Sans. (by Denis Jacquerye)
- extended contextual dotless and case substitutions to handle both below and
  above diacritics (by Denis Jacquerye)
- renamed Dotless and Case Form GSUB lookups in Sans with meaningful names (by
  Denis Jacquerye)

Changes from 2.17 to 2.18

- Re-encoded the source files for Full Unicode (by Ben Laenen)
- Re-enabled the "ff", "fl", "fi", "ffl", "ffi" ligatures by default in Serif
  (by Ben Laenen)
- Disabled the "fi", "ffi" ligatures for languages with dotless i in Serif (by
  Ben Laenen)
- added Tifinagh to Sans Book and Bold, U+2D30-U+2D65, U+2D6F, partially hinted
  in Sans Book. (by Denis Jacquerye)
- added Tai Xuan Jing Symbols (U+1D300-1D356) to Sans (by Remy Oudompheng)
- added double-struck letters (U+1D538-U+1D56B minus reserved code points) to
  Sans (by Gee Fung Sit)
- added U+22EE-U+22F1 to Sans (by Gee Fung Sit)
- added U+2C67-U+2C6C, U+2C75-U+2C77 to Serif (by Gee Fung Sit)
- italicized various glyphs in Latin Extended-B, IPA Extensions, Spacing
  Modifier Letters, Phonetic Extension (Supplement) and Super- and Subscripts
  in Serif Oblique fonts (by Gee Fung Sit)
- modified outlines, bearings of Hebrew U+05D6, U+05D8, U+05DB, U+05DE, U+05E0,
  U+05E1, U+05E2, U+05EA in Sans Book and Oblique, adjusted hinting in Book
  based on Yotam Benshalom's comments. (by Denis Jacquerye)
- added Braille Patterns (U+2800-U+28FF) to Serif fonts (by Gee Fung Sit)
- added N'Ko to Sans Book and Bold: U+07C0-U+07E7, U+07EB-U+07F5, U+07F8-U+07FA
  (by Eugeniy  Meshcheryakov)
- added U+0ED8 (Lao digit 8) to Sans (by Remy Oudompheng)
- added Lao diacritics U+0EB0-0EB9, U+0EBB-0EBC, U+0EC8-0ECD to Mono (by Remy
  Oudompheng)
- renamed Serif [Bold] Oblique, make it Italic (by Eugeniy  Meshcheryakov)
- added U+29FA-U+29FB to Sans and Sans Mono (by Gee Fung Sit)
- swapped glyphs for Eng U+014A from Sami Eng to African Eng, the latter being
  more common (by Denis Jacquerye)
- swapped ae U+00E6 and ae.alt in Serif Italics fonts, thus fixing #8213 (by
  Denis Jacquerye)
- minor improvements to Misc. Symbols in Sans (by Gee Fung Sit)
- minor improvements and additions to Sans ExtraLight (by Gee Fung Sit)
- improved instructions for various Cyrillic letters (by Eugeniy  Meshcheryakov)
- fixed hinting of theta and chi in Sans Book (by Ben Laenen)
- added Georgian Mkhedruli to Sans, Serif and Mono, ASumtavruli to Sans and
  Serif (by Besarion Gugushvili)

Changes from 2.16 to 2.17

- Sans fonts: fix position for certain combinations of Arabic fatha, kasra,
  shadda, damma, kasratan, dammatan, fathatan and hamza (by Ben Laenen)
- added 'ae.alt' to Serif Oblique fonts, with design matching shape of italic
  'a' instead of slanted 'a', see bug #8213 (by Denis Jacquerye)
- added super- and subscripts to Serif and Mono: U+1D2C-U+1D2E, U+1D30-U+1D3C,
  U+1D3E-U+1D42, U+1D62-U+1D65, U+1D78, U+2071, U+207A-U+207E, U+208A-U+208E,
  U+2090-U+2094 (by Gee Fung Sit)

Changes from 2.15 to 2.16

- fixed hinting instructions for digits in DejaVu Sans Oblique, Bold Oblique,
  and Serif Book to not change glyph width (by Eugeniy  Meshcheryakov)
- added instructions for U+0404, U+0411, U+0413, U+0414, U+0417-U+041B, U+041F,
  U+0423, U+0424, U+0426-U+0429, U+042C, U+042E, U+042F, U+0490 in Serif Bold
  (by Eugeniy  Meshcheryakov)
- added U+0220 and Eng.alt to Serif fonts (by Denis Jacquerye)
- added U+232C, U+2394, U+23E3 to Sans fonts (by John Karp)
- added U+230C-U+230F, U+231C-U+231F to Sans fonts, fixing bug:9547
  (by John Karp)
- adjusted dot below, dot above, dieresis above, dieresis below in Sans fonts
  (by Denis Jacquerye)
- added U+2300, U+2301, U+2303, U+2304, U+2305, U+2307, U+2326, U+2327, U+232B,
  arrow.base to Sans fonts (by John Karp)
- adjusted dot and dieresis below and above in Serif fonts (by Denis Jacquerye)
- added U+1E1C-U+1E1D to Serif fonts (by Denis Jacquerye)
- added U+22BE, U+22BF (by Wesley Transue)
- added U+2324; modified U+2325: more standard proportions, and matches U+2324 
  and U+2387; added U+2387 : flipped U+2325 with standard arrowhead 
  (by John Karp)
- added Lao digits U+0ED0-0ED7, U+0ED9 (by Remy Oudompheng)
- added to Mono in Arabic block : U+060C, U+0615, U+061B, U+061F, 
  U+0621-U+063A, U+0640-0655, U+065A, U+0660-066F, U+0674, U+0679-0687, U+0691, 
  U+0692, U+0698, U+06A1, U+06A4, U+06A9, U+06AF, U+06B5, U+06BA, U+06BE, 
  U+06C6, U+06CC, U+06CE, U+06D5, U+06F0-06F9 (by Remy Oudompheng)
- added to Mono in Arabic Presentations Forms-A : U+FB52-FB81, U+FB8A-FB95, 
  U+FB9E, U+FB9F, U+FBAA-FBAD, U+FBE8, U+FBE9, U+FBFC-FBFF (by Remy Oudompheng)
- added to Mono in Arabic Presentations Forms-B : U+FE70-FE74, U+FE76-FEFC, 
  U+FEFF (by Remy Oudompheng)
- added U+05BA, U+05BE, U+05F3, U+05F4, U+FB1E, U+FB21-U+FB28, U+FB4F to Sans 
  (by Eugeniy  Meshcheryakov)
- added U+2102 to Mono (by Eugeniy  Meshcheryakov)
- added U+2983-U+2984 to Sans (by Gee Fung Sit)
- added U+2A2F to Sans, Serif and Mono (by Gee Fung Sit)
- added U+2373-2375, U+237A to Sans (by John Karp)
- converted kern pairs to kern classes with Tavmjong Bah's scripts 
  (by Denis Jacquerye)
- set ScriptLang of kerning classes to just latn because of Pango bug
  (by Denis Jacquerye)
- added DNK to ScriptLang latn otherwise it is excluded, and SRB and MDK to
  cyrl (by Denis Jacquerye)
- removed flag 0x80 in generate.pe, otherwise it generates kerning tables some
  systems don't like; thus loosing Apple tables (by Denis Jacquerye)
- removed ligature for precomposed legacy characters of Sans Oblique fonts
  (by Denis Jacquerye)
- added bearings to en dash U+2013, em dash U+2014 and figure dash U+2012
  by making dashes shorter, preserving character width (by Denis Jacquerye)
- reduced U+031C, U+0325 (ring below), U+0339 to be entirely visible; 
  added instructions in Sans Book; changed U+1e00-U+1e01 to use new ring below
  (by Denis Jacquerye)
- adjusted circumflex below on U+1E12-U+1E13, U+1E18-U+1E19, U+1E3C-U+1E3D,
  U+1E4A-U+1E4B, U+1E70-U+1E71, U+1E76-U+1E77 in Sans fonts (by Denis Jacquerye)
- Added U+0ED4, U+0ED5 to DejaVu Sans (by Remy Oudompheng)
- Lao-specific anchors (by Remy Oudompheng)
- added alternate I to match the small capital in Sans (by Gee Fung Sit)

Changes from 2.14 to 2.15

- improved hinting in Sans Oblique to deal with some spacing and inconsistency
  issues (by Ben Laenen)
- added anchors to Mono Book, and added GPOS rules for combining diacritics to
  show up as zero width glyphs (by Ben Laenen)
- removed U+F21C (PUA), it was copy of U+2C64 from Latin Extended C (by Eugeniy
  Meshcheryakov)
- added U+27E6-U+27E7 to Sans (by Gee Fung Sit)
- added U+1407, U+1409, U+140C-U+141B, U+141D-U+1425, U+1427-U+142E,
  U+1435-U+1438, U+143A-U+1449, U+1452, U+1454, U+1457-U+1465, U+1467-U+146A,
  U+1471, U+1474-U+1482, U+1484-U+1488, U+148F, U+1492, U+14A0, U+14A2, U+14A9,
  U+14AC-U+14BA, U+14BC, U+14BD, U+14C6, U+14C9-U+14CF, U+14D1, U+14D2, U+14D9,
  U+14DC-U+14E9, U+14EC, U+14F3, U+14F6-U+1504, U+1506, U+1507, U+1510-U+1525,
  U+152C, U+152F-U+153D, U+1540, U+1541, U+154E, U+154F, U+1552, U+155B, U+155C,
  U+1568, U+1569, U+1574-U+157B, U+157D, U+15A7-U+15AE, U+1646, U+1647 (by
  Eugeniy Meshcheryakov)
- fixed several contours to not intersect, use horizontal or vertical tangents,
  use integer coordinates, etc in Sans Book (by Denis Jacquerye)
- added U+0496-U+0497 in Serif (by Andrey V. Panov)

Changes from 2.13 to 2.14

- added Philippine peso glyph U+20B1 (by Clayborne Arevalo)
- made U+2012 have the same width as digits, according to Unicode 5.0, 
  page 206 (by Roozbeh Pournader)
- made all of the "above" combining characters remove the dot of "i", 
  "j", etc (Soft_Dotted characters), according to Unicode 5.0, 
  page 228 (by Roozbeh Pournader)
- made U+012F, U+03F3, U+0456, U+0458, U+1E2D, and U+1ECB (all fonts 
  except Mono), U+0249, U+2148, and U+2149 (Sans and Sans Condensed), 
  U+0268 (Sans ExtraLight, Serif and Serif Condensed), and U+029D (Serif 
  and Serif Condensed) respect the Soft_Dotted property (by Roozbeh 
  Pournader)
- added U+223E, U+223F, U+2240, U+22C2, U+22C3 to Sans (by Remy Oudompheng)
- added U+203D to Serif (by Gee Fung Sit)
- added zero-width glyphs for U+2061-U+2063 to Sans and Serif (by Gee 
  Fung Sit)
- changed isolated forms of Arabic waw (U+0648, U+0624 and U+06C6) (bug #9432) 
  (by Ben Laenen)
- added Lao consonants U+0E81, U+0E82, U+0E84, U+0E87, U+0E88, U+0E8A, 
  U+0E8D, U+0E94-0E97, U+0E99-0E9F, U+0EA1-0EA3, U+0EA5, U+0EA7, U+0EAA, 
  U+0EAB, U+0EAD-0EAF to Sans Mono (by Remy Oudompheng)
- added U+0200-U+0217, U+0226-U+0229, U+02F3, U+1E00-U+1E07, 
  U+1E0A-U+1E0B, U+1E18-U+1E1F, U+1E22-U+1E23, U+1E28-U+1E2D, 
  U+1E3A-U+1E3B, U+1E40, U+1E48-U+1E49, U+1E56, U+1E58-U+1E59, 
  U+1E5E-U+1E5F, U+1E60, U+1E68-U+1E6B, U+1E6E-U+1E6F, U+1E72-U+1E77, 
  U+1E86-U+1E8B, U+1E92-U+1E96, U+1EA0-U+1EA1, U+1EF4-U+1EF5 to Mono 
  (by Ben Laenen)
- renamed uppercase variants of diacritics (macron, breve, double grave, 
  double acute, inverted breve, dot above) to "uni03XX.case" in Mono 
  (by Ben Laenen)
- moved uppercase variants of diacritics up in Mono so they properly 
  vertically align on capitals (by Ben Laenen)
- precomposed glyphs with macron, breve, double grave, double acute, 
  inverted breve, dot above, macron below, breve below, inverted breve 
  below, dot below, cedilla, caron below, circumflex below, diaeresis 
  below, tilde below now reference to combining diacritics instead of 
  space modifiers in Mono (by Ben Laenen)
- made ring below (U+0325), and half rings below (U+031C and U+0339) 
  smaller in Mono (by Ben Laenen)
- added U+205F to all fonts (by Roozbeh Pournader)
- added U+035E-U+035F to Sans (by Roozbeh Pournader)
- added empty glyphs for U+034F, U+202A-U+202E, U+2060, U+206A-206F, 
  U+FE00-U+FE0F to non-Mono fonts (by Roozbeh Pournader)
- added U+2101, U+2107-U+2108, U+210B, U+210C, U+2110, U+2112, U+211B, 
  U+211F, U+2123, U+2125, U+2128-U+2129, U+212C-U+212D, U+212F, 
  U+2130-U+2131, U+2133, U+2136-U+213A, U+2141-U+2144, U+2B00-U+2B11, 
  U+2B20-U+2B23 to Sans (by John Karp)
- reshaped omega (U+03C9) in Mono (by Ben Laenen)
- added U+2205, U+22C6, U+2300-U+2301, U+2303-U+2306, U+230C-U+230F, 
  U+2312-U+2315, U+231C-U+231F, U+2335, U+2337-U+233E, U+2341-U+2344, 
  U+2347-U+2348, U+234B-U+234D, U+2349-U+2350, U+2352-U+2354, 
  U+2357-U+2359, U+235A-U+235C, U+235E-U+2360, U+2363-U+2365, 
  U+2368-U+2369, U+236B-U+2370, U+2373-U+237A, U+2380-U+2383, 
  U+2388-U+238B, U+2395 in Mono (by Ben Laenen)

Changes from 2.12 to 2.13

- adjusted U+0198B, U+01B3-U+01B4 in Sans, hinted U+01B4 in Sans Book 
  (by Denis Jacquerye)
- added U+27F0-U+27FF, U+2906-U+2907, U+290A-U+290B, U+2940-U+2941 to Sans 
  (by Denis Jacquerye)
- added U+01E6-U+01E9, U+01EE-U+01EF, U+01F4-U+01F5, U+01FC-U+01FF, 
  U+021E-U+021F, U+0245, U+02BD, U+02C9, U+1E9B, U+2045-U+2046, U+2213, U+22C5,
  U+22EF to Sans Mono (by Roozbeh Pournader)
- added U+04FA-U+04FD to Sans (by Michael Everson)
- removed U+2329 and U+232A because of their CJK properties, added U+27E8 
  and U+27E9 in their stead, fixing part of bug #9038 (by Roozbeh Pournader)
- corrected and improvised U+0466-U+0469, U+046E-U+0471, U+047C-U+047D, U+0482, 
  U+0484-U+0486, U+0492-U+0493, U+04B0-U+04B1, U+050C-U+050D, and U+204A 
  in Sans (by Michael Everson)
- added instructions for U+0402, U+0409, U+040A, U+040B, U+044D, U+040F, 
  U+0452, U+0459-U+045B, U+045F to Sans Book (by Eugeniy Meshcheryakov)
- made italic shape for U+431, U+432, U+437, U+43B, U+43C, U+43D, U+444, U+447, 
  U+44D, U+44F, U+459, U+45A in SerifOblique and SerifBoldOblique 
  (by Andrey V. Panov)
- modified U+024C to match glyph in Unicode chart, fixing bug #9039 
  (by Denis Jacquerye)
- made some canonically equivalent characters share the same glyph: 
  U+02B9 = U+0374, U+0343 = U+0313, and U+0387 = U+00B7 also adjusting U+02BA 
  to look like double U+02B9, fixing parts of bug #9038 (by Roozbeh Pournader)
- changed shapes for U+0478 and U+0479 in Sans to those in the Unicode charts, 
  based on a recent decision by Unicode Technical Committee to only use 
  the digraph form (by Michael Everson)
- adjusted width of NBSP U+00A0 and NNBSP U+202F, fixing bug #8401 
  (by Denis Jacquerye)
- fixed several contours to not intersect, use horizontal or vertical tangents, 
  use integer coordinates, etc (by Roozbeh Pournader and Denis Jacquerye)
- added U+1402, U+1430, U+144D, U+146C, U+148A, U+14A4, U+14C1, U+14D4, U+14EE, 
  U+1527, U+1545, U+157E, U+158E, U+15AF to Sans (by Eugeniy Meshcheryakov)
- enlarged width of U+459 and U+45A in Serif (by Andrey V. Panov)
- made traditional shape for U+452, U+45B (by Andrey V. Panov)
- added euro sign U+20AC to Sans ExtraLight, making fontconfig recognize 
  the font as supporting English (by Denis Jacquerye)

Changes from 2.11 to 2.12

- added U+0180 to Serif (by Denis Jacquerye)
- improved and/or hinted Armenian letters U+0542, U+0546, U+0562,
  U+0563, U+0564, U+0577, U+0582 in Sans (by Ben Laenen)
- added U+4FE-U+4FF, U+512-U+513, U+2114, U+214E, U+26B2 to Sans
  (by Gee Fung Sit)
- adjusted U+0496-U+0497, U+049A-U+04A1 in Sans to match U+0416,
  U+041A, U+0436 and U+043A (by Gee Fung Sit)
- Mathematical Operators in Sans: changed U+22C0-U+22C1 to match
  other n-ary operators, adjusted U+2203-U+2204, changed U+2220 in
  Sans to match the style of U+2221 (by Gee Fung Sit)
- added U+1401, U+1403-U+1406, U+140A, U+140B, U+1426, U+142F,
  U+1431-U+1434, U+1438, U+1439, U+1449, U+144A, U+144C,
  U+144E-U+1451, U+1455, U+1456, U+1466, U+146B, U+146D-U+1470,
  U+1472, U+1473, U+1483, U+1489, U+148B-U+148E, U+1490, U+1491,
  U+14A1, U+14A3, U+14A5-U+14A8, U+14AA, U+14AB, U+14BB, U+14C0,
  U+14C2-U+14C5, U+14C7, U+14C8, U+14D0, U+14D3, U+14D5-U+14D8,
  U+14DA, U+14DB, U+14EA, U+14ED, U+14EF-U+14F2, U+14F4, U+14F5,
  U+1405, U+1526, U+1528-U+152B, U+152D, U+152E, U+153E,
  U+1542-U+1544, U+1546-U+154D, U+1550, U+1553, U+1555-U+155A,
  U+1567, U+156A, U+157C, U+157F-U+1585, U+158A-U+158D,
  U+158F-U+1596, U+15A0-U+15A6, U+15DE, U+15E1, U+166E-U+1676 to
  Sans (by Eugeniy Meshcheryakov)
- re-enabled Latin ligatures fi, ffi, fl, ffl and ff in Sans
  (by Ben Laenen)
- made italic shape for U+436, U+44A, U+44B, U+44C, U+44E, U+45F,
  U+463 in SerifOblique and SerifBoldOblique (by Andrey V. Panov)
- fixed sub- and superscript metrics in Condensed Sans (bug #8848)
  (by Ben Laenen)
- added U+474, U+475 in Serif (by Andrey V. Panov)
- hinted Greek glyphs U+03B7, U+30B8, U+03B9, U+03C1, U+03C3,
  U+03C6 in Mono Book (by Ben Laenen)

Changes from 2.10 to 2.11

- added instructions for Hebrew glyphs (Sans Book, by Eugeniy
  Meshcheryakov)
- changed U+01A6 (Latin Yr) after bug #8212, in Sans, Serif and
  Sans Mono fonts (by Denis Jacquerye).
- removed instruction for U+2600-U+26A1 (by Mederic Boquien)
- added U+202F and set width of U+00A0 (nobreakingspace) to the
  same as U+0020, space (by Denis Jacquerye).
- added and improved instructions for various Cyrillic letters
  (by Eugeniy Meshcheryakov)
- Changed U+416, U+42F, U+427 (non-Bold), U+436, U+447 (non-Bold),
  U+44F, U+437 (Bold), corrected U+40F, U+414, U+424, U+426, U+429,
  U+434, U+438 (Bold), U+446, U+449, U+44D (non-Bold), U+45F in
  Sans Mono (by Andrey V. Panov)
- made small corrections to Cyrillic, most appreciable to U+409,
  U+413, U+41B, U+427 and U+433, U+434, U+43B, U+447, U+459
  (upright fonts) to Serif (by Andrey V. Panov)
- adjusted bearings of U+410, U+416, U+41A, U+42F, U+436, U+43A,
  U+443, U+44F in Serif (by Andrey V. Panov)
- enlarged width of U+44A, U+44B, U+44C, U+463 in Serif
  (by Andrey V. Panov)
- added ligature "iacute" as "afii10103" (U+456) "acutecomb" in
  Serif (by Andrey V. Panov)
- made italic shape to U+446, U+448, U+449 in Serif (by Andrey V.
  Panov)
- added "afii10831" (U+F6C7), "afii10832" (U+F6C8) in Serif (by
  Andrey V. Panov)
- new minimum version of fontforge is 20061014 (by Ben Laenen)

Changes from 2.9 to 2.10:

- added U+0242, U+024A-U+024B, U+024E-U+024F, U+037C-U+037D, U+0E3F, 
  U+1D2C-U+1D2E, U+1D30-U+1D42, U+1D5D-U+1D6A, U+1D78, U+1DB8, 
  U+2090-U+2094, U+20D0-U+20D1, U+2C60-U+2C66, U+2C6B-U+2C6C, U+2C74 and 
  U+FB29 to Sans (by Gee Fung Sit)
- added Lao glyphs : U+0E81-0E82, U+E084, U+0E87-0E88, U+0E8A, U+0E8D, 
  U+0E94-0E97, U+0E99-0E9F, U+0EA1-0EA3, U+0EA5, U+0EA7, U+0EAA-0EAB, 
  U+0EAD-0EB9, U+0EBB-0EBD, U+0EC0-0EC4, U+0EC6, U+0EC8-0ECD, U+0EDC-0EDD 
  (by Remy Oudompheng)
- fixed U+0193 not showing in Windows (bug #7897) (by Ben Laenen)
- changes to U+222B-222D in Sans Mono (by Remy Oudompheng)
- ported the three remaining currency symbols from Arev (U+20B0, 
  U+20B2-U+20B3), and replaced one (U+20AF) in Sans (by Lars Naesbye 
  Christensen)
- corrected U+20A5 in Sans (by Gee Fung Sit)
- merged Double-Struck Letters from Arev: U+2102, U+210D, U+2115, 
  U+2119-U+211A, U+2124, U+213C-U+2140 (by Gee Fung Sit)
- added U+2308-U+230B and U+2329-U+232A to Sans Mono and Serif faces, 
  fixed incorrect direction of U+2329 in Sans faces, and improved 
  U+2308-U+230B in Sans faces per Ben Laenen's suggestions (by David 
  Lawrence Ramsey)
- added U+06D5 and final form of it (needed for Kurdish) (by Ben Laenen)
- added two special glyphs U+F000 and U+F001 in Sans Book that show the 
  current ppem size (horizontal and vertical) (by Ben Laenen)
- added U+2318 and U+2325 to Sans Mono faces, based on the Sans versions 
  (by David Lawrence Ramsey)
- added U+2B14-U+2B1A to all faces except Sans ExtraLight (by David 
  Lawrence Ramsey)
- respaced all Geometric Shapes characters in Serif faces to match those 
  in Sans faces again, respaced U+23CF in Sans, Sans ExtraLight, and 
  Serif faces to match U+25A0 (or Sans in Sans ExtraLight's case) again, 
  and respaced U+2B12-U+2B13 in Sans and Serif faces to match U+25A1 
  again (by David Lawrence Ramsey)
- corrected width of Modifier Small Letters U+1D43-1D5B in Sans Oblique 
  and U+1D9B-U+1DBF in Sans Oblique and Sans Bold Oblique (by Gee Fung Sit)
- added a bunch of glyphs to Sans ExtraLight (see SVN for details) (by 
  Gee Fung Sit)
- adjusted Cyrillic descenders in Sans ExtraLight to sync with Sans (by 
  Gee Fung Sit)
- added U+0242, U+0245 to Serif (by Gee Fung Sit)
- replaced the SHPIX routines which gave them bad spacing at certain 
  sizes in FreeType for A, V, Z, v and z in Sans Bold (by Ben Laenen) 

Changes from 2.8 to 2.9:

- DejaVuSansExtraLight.sfd: changed family name from "DejaVu Sans" to
  "DejaVu Sans Light" (in case we add a Light weight variant), so legacy
  apps that understand only 4 styles are happy. (by Denis Jacquerye)
- added Name ID 16, aka preferred family name, and Name ID 17, aka
  preferred style name, so contemporary apps that understand more that 4
  styles can use big fonts families "DejaVu Sans" and "DejaVu Serif". For
  those, Extralight and Condensed are just styles not different families.
  (by Denis Jacquerye)
- added U+22B6-22BD, U+22C0-22C1, U+22D6-22D7 to Sans. (by Remy Oudompheng)
- added U+037B, U+2184, U+2C67-U+2C6A and U+2C75-U+2C77 to Sans (by Gee
  Fung Sit)
- adjusted asteriskmath (U+2217) for consistency with other mathematical
  operators in Sans (by Ben Laenen)
- hinted some Armenian capitals in Sans Book (by Ben Laenen)
- added U+0246 - U+0249 (by Ben Laenen)
- BUGFIX : swapped U+224E and U+224F, in Sans, Sans Condensed and Sans Mono
  (by Remy Oudompheng)
- adjusted U+20B5 (by Mederic Boquien)
- swapped U+21DA and U+21DB which were in wrong order (by Heikki Lindroos)
- added U+222E-2233, U+239B-23AD, U+2A00-2A02, U+2A0F-2A1C to Sans (by Remy
  Oudompheng)
- added U+239B-23AD to Mono (by Remy Oudompheng)
- added U+2024-2025 to Serif (by Mederic Boquien)
- added U+222C-222D, U+2A0C-2A0E to Serif (by Remy Oudompheng)
- added U+2190-21FF to Mono (by Heikki Lindroos)
- added Hebrew glyphs - U+05B0-U+05BD, U+05BF-U+05C3, U+05C6, U+05C7,
  U+05D0-U+05EA, U+05F0-U+05F2, U+FB1F, U+FB20, U+FB2A-U+FB36,
  U+FB38-U+FB3C, U+FB3E, U+FB40, U+FB41, U+FB43, U+FB44, U+FB46-U+FB4E (by
  Gee Fung Sit and Eugeniy Meshcheryakov)
- adjustments for Cyrillic in Sans (by Andrey V. Panov)
- made italic shape for U+0434, U+0456, U+0457 in SerifOblique and Serif
  Bold Oblique (by Andrey V. Panov)

Changes from 2.7 to 2.8:

- fixed instructions for U+0423, U+0427, U+0447, U+0448 in Serif, so they
  look good at large sizes too (by Eugeniy Meshcheryakov)
- added U+FB00 and U+FB03 to U+FB06 to Serif typefaces (by Heikki Lindroos)
- added U+26B0-U+26B1, U+2701-U+2704, U+2706-U+2709, U+270C-U+2727, U+2729
  to U+274B, U+274D, U+274F to U+2752, U+2756, U+2758-U+275E, U+2761 to
  U+2775 (by Heikki Lindroos)
- added and improved instructions for Cyrillic letters in Mono and Serif
  (Book, by Eugeniy Meshcheryakov)
- rotated U+26B0 (was too small in mono) (by Gee Fung Sit)
- adjusted U+1EDA-U+1EDD, U+1EE8-U+1EEB, capitals using capital specific
  accent and moved diacritics to match position on U+00F2 (ograve), etc.
  (by Denis Jacquerye)
- added U+20D6, U+20D7 to Sans (by Gee Fung Sit)
- made Armenian ligatures discretionary since the Firefox ligature problem
  still isn't fixed (by Ben Laenen)
- moved Armenian hyphen U+058A to a higher position (bug #7436) (by Ben
  Laenen)
- hinted Greek glyphs in Sans Bold (by Ben Laenen)
- enabled Arabic lam-alif ligatures when diacritics are used (by Ben Laenen)

Changes from 2.6 to 2.7:

- added glyphs needed for Kurdish: U+0695, U+06B5, U+06C6, U+06CE and their
  init/medi/fina forms in Sans (by Ben Laenen)
- added U+02CD, U+01F8 - U+01F9, U+1E3E - U+1E3F, U+1E30 - U+1E35, U+1EBC -
  U+1EBD, U+1EF8 - U+1EF9 (includes glyphs needed for Yoruba, Maori, Guarani
  and Twi) (by Ben Laenen)
- added U+22C8-22CC, U+29CE-29D5, U+2A7D-2AA0, U+2AAE-2ABA, U+2AF9-2AFA to
  Sans (by Remy Oudompheng)
- adjusted diacritics on Vietnamese, Pinyin and other characters:
  U+01A0-U+01A1, U+01AF-U+01B0, U+01D5-U+01DC, U+01DE-01E1, U+01FA-U+01FB
  U+022A-U+022D, U+0230-U+0231, U+1E14-U+1E17, U+1E4C-U+1E53, U+1E78-U+1E7B,
  U+1EA4-U+1EF1 in Sans (Book, Bold and Oblique) (by Denis Jacquerye)
- added basic arrows U+2190-U+2193 in Serif, which completes MES-1 compliance
  for Serif (by Ben Laenen)
- added U+01E4, U+01E5, U+01FA, U+01FB, U+02BD, U+02C9 and U+02EE to Serif
  (by Ben Laenen)
- fixed U+0209 in Serif Bold Oblique (by Ben Laenen)
- adjusted Box Drawing block characters U+2500-257F in Mono to fit character
  cell, shifting them up by 416 (Denis Jacquerye)
- redid U+0194 in Sans (by Ben Laenen)
- added U+2217-2218, U+2295-22A1 to Mono (by Remy Oudompheng)
- added U+0462 to Serif (by Andrey V. Panov)
- added U+226C, U+228C-228E, U+2293-2294, U+22F2-22FF to Sans (by Remy
  Oudompheng)
- adjusted U+2208-220D in Sans (by Remy Oudompheng)
- improved some Cyrillic glyphs in Mono (by Andrey V. Panov), rewritten
  instructions for changed glyphs (by Eugeniy Meshcheryakov)
- added U+1E0E-1E0F, U+1E8E-1E8F to Mono fonts (by Denis Jacquerye). (bug
  #7166)
- renamed 'Dotabove' to 'Dotaccent' in Mono Sans Oblique to match other fonts
  (by Denis Jacquerye).
- added U+200B-U+200F in Sans faces and Serif faces, U+200B and U+200C were
  in Sans already (by Lars Naesbye Christensen)
- added U+2601-U+262F, U+263D, U+263E, U+2648-U+265F, U+2668, U+2670-U+268B,
  U+2690-U+269C, U+26A0, U+26A1, U+2794, U+2798-U+27AF, U+27B1-U+27BE to Mono
  (by Heikki Lindroos)
- replaced the references with unshifted ones for both κ U+03BA and к U+043A
  in Mono Book (by Denis Jacquerye)
- fixing glyph for U+04ED in Mono Book, consisted only of dieresis (by Andrey
  V. Panov).

Changes from 2.5 to 2.6:

- redid U+2032 - U+2037, U+2057 based on Arev in Sans (by Gee Fung Sit)
- added U+0195, corrected U+039E, U+204B in Sans ExtraLight (by Gee Fung Sit)
- added instructions for some Cyrillic letters in Sans Bold (by Eugeniy
  Meshcheryakov)
- added vulgar fractions U+2153-U+215F for Serif, made with references (by
  Lars Naesbye Christensen)
- added U+228F-2292, U+2299-22AF, U+22B2-22B5, U+22CD, U+22D8-22ED to Sans
  (by Remy Oudompheng)
- added U+2208-220D, U+2238-223D, U+2278-2281, U+228A-228B, U+228F-2292,
  U+22CD, U+22DA-22E9 to Mono (by Remy Oudompheng)
- fixed misplaced dot in U+2250 in Mono (by Remy Oudompheng)
- added instructions for some Cyrillic letters in Mono Book and Bold(by
  Eugeniy Meshcheryakov)
- minor changes to U+2241, U+2261-2263, U+22A4, U+22A5 in Sans (by Remy
  Oudompheng)
- added hinting instructions to lowercase Armenian glyphs in Sans Book (by
  Ben Laenen)
- changed U+2208, U+220B to match U+2209 and U+220C in Sans Bold (by Remy
  Oudompheng)
- added Braille patterns U+2800-U+28FF to Sans (by Mederic Boquien)
- added instructions for some Cyrillic letters in Serif Book (by Eugeniy
  Meshcheryakov)
- renamed BoldOblique fonts to Bold Oblique in TTF Name as originally in
  Bitstream Vera fonts (by Denis Jacquerye)
- added hinting instructions to some Latin-B Extended and IPA characters in
  Sans Book (by Denis Jacquerye and Ben Laenen)
- adjusted bearings, replaced diacritics, hinted hook and horn for
  Vietnamese in Sans Book (by Denis Jacquerye)
- made FAX, TM, TEL, etc. discritionary ligatures in Sans and Serif fonts
  (by Denis Jacquerye)
- removed ligatures of precomposed characters in Sans and Serif fonts (by
  Denis Jacquerye)
- added U+F208, U+F20A, U+F215-F217, U+F21A-F21B, U+F25F in PUA (from SIL's
  PUA, probably in Unicode 5.0): U+0243, U+0244, U+0245, U+024C, U+024D,
  U+2C64, (U+2C6D), (U+2C71)
- modified some glyphs in Serif Oblique to make them more italic (by Denis
  Jacquerye)

Changes from 2.4 to 2.5:

- fixed excessive kerning bug that occurs with Pango (by Denis Jacquerye)
- added U+20AF to Sans and Serif (by Lars Naesbye Christensen)
- regenerated Condensed faces (by Ben Laenen)
- added U+035C-U+035D to Sans, fixed U+0361 (by Denis Jacquerye)
- integrated 255 characters from Arev fonts: Latin Extended-B, Spacing
  Modifiers, Combining Diacritical Marks, Cyrillic, Cyrillic supplement,
  General Punctuation, Letterlike Symbols, Arrows, Mathematical Operators,
  Miscellaneous Technical, Dingbats, Alphabetic Presentation Forms (by Denis
  Jacquerye)
- added basic Cyrillic and basic Greek to Sans ExtraLight (by Denis Jacquerye)
- added U+0498, U+049A, U+04AA, U+04AB, U+04AF to Serif (by Eugeniy
  Meshcheryakov)
- added U+0494, U+0495, U+0498, U+0499, U+04AA, U+04AB, U+04C3, U+04C4,
  U+04C7, U+04C8 to Mono (by Eugeniy Meshcheryakov)
- adjusted weight of U+0256, U+0257, U+0260, U+0272, U+0273, U+0277, U+029B,
  U+02A0 and modifed  U+028B and U+027A in Mono (by Denis Jacquerye)
- added U+2000-200A to Mono (by Denis Jacquerye)
- added vulgar fractions U+2153 - U+215F to Mono (by Gee Fung Sit)
- adapted metrics of Arabic glyphs so they stay above cut-off height in Sans
  (by Ben Laenen)
- fixed mkmk anchors for Arabic diacritics so they stack properly in Sans (by
  Ben Laenen)
- fixed weight of lowercase upsilon in Sans Bold, make small adjustment to
  lowercase omega in Sans (by Ben Laenen)
- added U+210E (by Mederic Boquien)
- unslanted U+2201, U+221B and U+221C in Sans Oblique (by Mederic Boquien)
- added several mathematical relation symbols to Sans and Mono (U+2241-224C,
  U+2250-2255, U+2260-2269, U+226E-2277, U+2282-2287) modified U+223C to match
  other tildes, and U+2282-2284 to have the same shape. (by Remy Oudompheng)
- made U+2234-U+2237 refer to U+2219 instead of U+00B7 in Sans (by Mederic
  Boquien)
- added U+2238-223B, U+226A-226B, U+2278-2281, U+2288-228B to Sans (by Remy
  Oudompheng)
- unslanted and changed reference of U+22C5 from U+00B7 to U+2219 in Sans (by
  Mederic Boquien)
- added U+224D-225F, U+226D, U+22C6 to Sans and unslanted U+2219 in Sans
  Oblique. (by Remy Oudompheng)
- added U+224D-225F, U+226D to Mono, shifted U+2266-2269 higher upwards and
  unslanted U+2219 in Oblique. (by Remy Oudompheng)
- merged Coptic glyphs from Arev 0.2 (by Lars Naesbye Christensen)
- fixed and adjusted various Cyrillic glyphs in Serif (by Andrey V. Panov)
- made fi, fl... ligatures discretionary ligatures (by Ben Laenen)

Changes from 2.3 to 2.4:

- added U+04A2, U+04A3, U+04AC - U+04AF, U+04BA, U+04BB, U+04C0 -
  U+04C2, U+04CB, U+04CD, U+04D8 - U+04DF, U+04E2 - U+04E5, U+04E8 - U+04F5,
  U+04F6 - U+04F9 to Mono (by Eugeniy Meshcheryakov)
- added U+048C, U+048D, U+0494, U+0495, U+049E - U+04A7, U+04AC -
  U+04AE, U+04B4- U+04B7, U+04BA, U+04BB, U+04C0 - U+04C4, U+04C7, U+04C8,
  U+04CB, U+04CC, U+04D8 - U+04DF, U+04E2 - U+04E5, U+04EC - U+04F9 to Serif
  (by Eugeniy Meshcheryakov)
- added U+2134 to Sans (by Gee Fung Sit)
- added U+2080 - U+2089 to all faces (by Gee Fung Sit)
- several minor corrections to Sans (by Gee Fung Sit)
- major corrections to Sans Condensed (by Gee Fung Sit)
- corrected Superscripts and Subscripts in Sans (by Gee Fung Sit)
- corrected anchors of U+0316-U+0319 (by Denis Jacquerye)
- Verajja integrated (by Stepan Roh)
- copied U+2328, U+2600, U+2639-U+263C, U+263F-U+2647, U+2660-U+2667,
  and U+2669-U+266F from Sans to Serif, and copied scaled-down versions of
  them to Sans Mono (by David Lawrence Ramsey)
- added U+20B4 to all faces (by Eugeniy Meshcheryakov)
- added more minor positional adjustments to U+2638 in all faces to
  match the other miscellaneous symbols in Verajja, and rescale it in Sans
  Mono so that it looks better (by David Lawrence Ramsey)
- added U+2242, U+2243 and U+22A4 (by Mederic Boquien)
- corrected U+2245 in Sans (by Mederic Boquien)
- added U+0221, U+0234-0236 (by Denis Jacquerye)
- added in Arabic block to Sans: U+060C, U+0615, U+061B, U+061F, U+0621
- U+063A, U+0640 - U+0655, U+0660 - U+066F, U+0679 - U+0687, U+0698, U+06A1,
  U+06A9, U+06AF, U+06BA, U+06BF, U+06CC, U+06F0 - U+06F9 (by Ben Laenen)
- added in Arabic Presentation Forms A to Sans: U+FB52 - U+FB81, U+FB8A
- U+FB95, U+FB9E - U+FB9F, U+FBE8 - U+FBE9, U+FBFC - U+FBFF (by Ben Laenen)
- added complete Arabic Presentation Forms B to Sans: U+FE70 - U+FE74,
  U+FE76 - U+FEFC, U+FEFF (by Ben Laenen)
- added complete Greek Extended block to Mono (by Ben Laenen)
- modified Greek capitals with tonos in Mono (by Ben Laenen)
- added U+01C4-01CC, U+01D5, U+01DE, U+01E0-U+01E1, U+01E6-U+01E9,
  U+01EE-U+01F5, U+01F8-U+0217, U+021E-U+021F, U+0226-U+022A, U+022C to Serif
  (by Denis Jacquerye)
- adjusted U+043B and U+044F in Serif (by Denis Jacquerye)
- added U+2000-U+200A (by Denis Jacquerye)
- added U+1E00-U+1E0B, U+1E0E-U+1E11, U+1E14-U+1E1C, U+1E1E-U+1E23,
  U+1E26-U+1E2D, U+1E30-U+1E35, U+1E3A-U+1E3B, U+1E3E-U+1E40, U+1E48-U+1E49,
  U+1E50-U+1E56, U+1E58-U+1E59, U+1E5E-U+1E60, U+1E68-U+1E6B, U+1E6E-U+1E6F,
  U+1E72-U+1E7D, U+1E86-U+1E9B, U+1EA0-U+1EA3, U+1EAC-U+1EB7, U+1EBA-U+1EBD,
  U+1EC6-U+1ECF, U+1ED8-U+1ED9, U+1EE6-U+1EE7, U+1EF4-U+1EF9 to Serif (by
  Denis Jacquerye)
- added U+048E, U+048F, U+049C-U+049F, U+04B8, U+04B9, U+04BC-U+04BF,
  U+04C3, U+04C4 to Sans (by Eugeniy Meshcheryakov)
- added DejaVu Sans Extra Light (by Denis Jacquerye)
- Adjusted underline position for (hopefully) improved legibility in
  Sans, Serif, Mono (Tim May)
- added auto-generated DejaVu LGC (by Stepan Roh) 

Changes from 2.2 to 2.3:

- fixed bug U+042B and U+044B behave badly in Sans Bold or Oblique (by
  Keenan Pepper)
- added and improved TrueType instructions and related settings (by
  Keenan Pepper)
- added U+04D0-U+04D7, U+04E6, U+04E7 to Mono (by Eugeniy Meshcheryakov)
- added U+048A - U+048D, U+0498, U+0499, U+04AA, U+04AB, U+04B0, U+04B1,
  U+04C0, U+04C9, U+04CA, U+04CE, U+04CD, U+04DA, U+04DB, U+04DE, U+04DF,
  U+04E2 - U+04E5, U+04EC - U+04F8, U+04F9 to Sans (by Eugeniy Meshcheryakov)
- added U+04E0, U+04E1 to all faces (by Eugeniy Meshcheryakov)
- added Greek Extended to Sans and Serif: U+1F00-U+1F15, U+1F18-U+1F1D,
  U+1F20-U+1F45, U+1F48-U+1F4D, U+1F50-U+1F57, U+1F59, U+1F5B, U+1F5D,
  U+1F5F-U+1F7D, U+1F80-U+1FB4, U+1FB6-U+1FC4, U+1FC6-U+1FD3, U+1FD6-U+1FDB,
  U+1FDD-U+1FEF, U+1FF2-U+1FF4, U+1FF6-U+1FFE (by Ben Laenen)
- added Greek variant letterforms, archaic letters and symbols to Mono:
  U+03D0-U+03E1, U+03F0-U+03FF (by Ben Laenen)
- added Armenian block and Armenian ligatures to Sans (U+0531 - U+0556,
  U+0559 - U+055F, U+0561 - U+0587, U+0589 - U+058A, U+FB13 - U+FB17) (by Ben
  Laenen)
- redid some Greek characters in Sans and Mono to make them look better
  and to correct some errors (by Ben Laenen)
- added U+27E0 to all faces (by David Lawrence Ramsey)
- added underscore (U+005F) consistency fixes: extended the Sans Mono
  and Sans Mono Oblique underscores to touch both horizontal edges, and
  reduced the height of the Sans Bold Oblique underscore to match the Sans
  Bold underscore (by David Lawrence Ramsey)
- added underscore (U+005F) derivatives and consistency fixes for them:
  made U+0332 a reference to underscore at Denis Jacquerye's suggestion; made
  U+0333 two references to underscore; made U+033F two references to U+203E;
  added U+2017 as two references to underscore, and made U+0333 a reference to
  it; and added U+203E as a reference to underscore, and made U+0305 a
  reference to it (by David Lawrence Ramsey)
- added U+201B, U+2220, U+2320-U+2321, U+23AE, U+23CF, all remaining
  Geometric Shapes glyphs (U+25A0-U+25C9, U+25CB-U+25D7, U+25D9-U+25E5,
  U+25E7-U+25FF), and U+2B12-U+2B13 to all faces (by David Lawrence Ramsey)
- added minor positional adjustments to U+2638 in all faces (by David
  Lawrence Ramsey)
- added U+201F to Sans Mono and Serif faces (by David Lawrence Ramsey)
- added U+01B7, U+01F6, U+0464 - U+0465, U+2160 - U+2180, U+2183,
  U+220A, U+220D, U+2329, U+232A, U+2422, U+27E8 - U+27EB, U+2680 - U+2685 to
  Sans (by Gee Fung Sit ???)
- added U+2116 to Sans and Serif (by Gee Fung Sit)
- changed florin sign U+0192 in Sans (by Gee Fung Sit)
- added anchor points to some glyphs (by Denis Jacquerye)
- adjusted height of IPA superscripts U+02B0-02B8, U+02C0-02C1,
  U+02E0-02E4, U+207F to match with height of U+00B2 (by Denis Jacquerye)
- added U+0184-U+0185, U+019C, U+019F, U+01A0-U+01A3, U+01A6, U+01AA,
  U+01AF-U+01B0, U+01B2-U+01B4, U+01B7-U+01B8, U+01BC-U+01BC, U+0224-U+0225,
  U+023A-U+0240, U+1D16-U+1D17, U+1D1D-U+1D1E, U+1D43-U+1D5B, U+1D7B,
  U+1D85,U+1D9B-1DB7, U+1DB9-U+1DBF, U+20A6 to all fonts (by Denis Jacquerye)
- added added U+0182, U+018B, U+018E, U+01A0-U+01A1, U+01B1, U+01B9,
  U+01C0-U+01C3, U+0238-U+0239, U+1D02, U+1D08-U+1D09, U+1D14, U+1D1F, U+1D77
  to Serif and Mono (by Denis Jacquerye)
- added U+0181, U+0183, U+0187-U+0188, U+018A-U+018F, U+0191, U+0193,
  U+0195-U+019B, U+019D-U+019E, U+01A4-U+01A5, U+01AC-U+01AE, U+01B5-U+01B6,
  U+01B9, U+01BB, U+01F6 to Serif (by Denis Jacquerye)
- added U+0181, U+0187-U+0188, U+018A, U+018D, U+018F, U+0191, U+0193,
  U+0195-U+019F, U+01A4-01A5, U+01AC-01AD, U+01B5-U+01B6, U+1BB, U+01F6,
  U+01D7-U+01DC, U+0238-U+0239, U+0241 to Mono (by Denis Jacquerye)
- added to Mono and Serif (by Denis Jacquerye) 

Changes from 2.1 to 2.2:

- reworked the vertical orientation of the Blocks Elements characters
  in all faces to remove their overly large descenders, in order to fix
  problems with e.g. terminal emulators (by David Lawrence Ramsey)
- copied bullet in Sans faces to Serif faces for consistency (by David
  Lawrence Ramsey)
- added U+2023, U+25D8, U+25E6, and U+29EB to all faces (by David
  Lawrence Ramsey)
- added U+1EB8, U+1EB9, U+1ECA - U+1ECD, U+1EE4, U+1EE5 (by Tim May)
- added U+01DD, U+02BE, U+02BF, U+02D3 to all, changed U+02D2 in
  non-Condensed and U+1EE5 in Serif (by Tim May)
- fixed U+01CE, replacing wrong circumflex by caron (by Denis Jacquerye)
- added anchor points to some glyphs (by Denis Jacquerye)
- added U+20B5 (by Denis Jacquerye)
- added U+0181 - U+0183, U+0187, U+0188, U+018A - U+018D, U+0191,
  U+0193, U+0195 - U+019B, U+019D, U+019E, U+01A4, U+01A7 - U+01A9, U+01AB -
  U+01AE, U+01B1, U+01B5, U+01B6, U+01BB, U+01C0 - U+01C3, U+01F1 - U+01F3,
  U+0238, U+0239, U+1D02, U+1D08, U+1D09, U+1D14, U+1D1F, U+1D77, U+2103,
  U+2126, U+2127, U+212A, U+212B, U+2132, U+214B, U+2210, U+2217, U+2218,
  U+2A0C - U+2A0E, U+FB00, U+FB03 and U+FB04 to Sans (by Gee Fung Sit)
- added U+01A9, U+01C3 and U+2126 to Mono and Serif (by Gee Fung Sit)
- adjusted bearings of U+028B in Sans (by Gee Fung Sit)
- added U+018F, U+0494-U+0497, U+04A0-U+04A7, U+04AC-U+04AF,
  U+04B4-U+04B7, U+04BA-U+04BB, U+04C1-U+04C2, U+04C5-U+04C8, U+04CB-U+04CC,
  U+04D0-U+04D9, U+04DC-U+04DD, U+04E6-U+04EB to Sans (by Eugeniy
  Meshcheryakov)
- replaced with references U+0391-U+0393, U+0395-U+0397, U+0399, U+039A,
  U+039C, U+039D, U+039F-U+03A1, U+03A4, U+03A5, U+03A7, U+03BF, U+03DC,
  U+0405, U+0406, U+0408, U+0410, U+0412, U+0415, U+0417, U+041A,
  U+041C-U+041E, U+0420-U+0422, U+0425, U+0430, U+0435, U+043E, U+0440,
  U+0441, U+0443, U+0445, U+0455-U+0458 in Serif and Mono (by Eugeniy
  Meshcheryakov)
- added U+04D0-U+04D7, U+04E6-U+04EB to Serif (by Eugeniy Meshcheryakov)
- added U+212A and U+212B to the rest of the faces (by Lars Naesbye
  Christensen)
- added U+2318 and U+2325 to Sans and Serif (by Lars Naesbye Christensen)
- added and improved TrueType instructions and related settings (by
  Keenan Pepper)
- completed basic Greek alphabet: added U+0374-U+0375, U+037A, U+037E,
  U+0384-U+038A, U+038C, U+038E-U+0390, U+03AC-U+03BF, U+03C1-U+03CE (by Ben
  Laenen)
- added U+2070 and U+2074-U+2079 (by Mederic Boquien) 

Changes from 2.0 to 2.1:

*** Be aware that names of some TTF files changed since version 2.0. ***

- added U+0323, U+1E0C, U+1E0D, U+1E24, U+1E25, U+1E36 - U+1E39, U+1E42,
  U+1E43, U+1E46, U+1E47, U+1E5A - U+1E5D, U+1E62, U+1E63, U+1E6C, U+1E6D,
  U+1E7E, U+1E7F (by Tim May)
- fixed bug where GNOME applications used Mono Bold Oblique instead of
  Mono Oblique (by Keenan Pepper)
- added and improved TrueType instructions and related settings (by
  Keenan Pepper)
- added U+1E41, U+1E57, U+1E61 (by Sander Vesik)
- added U+0189, U+0309, U+0313, U+0314, U+031A, U+031B, U+0327, U+0328,
  U+032B, U+0333, U+033C (by Denis Jacquerye)
- adjusted and fixed U+0186, U+0254, U+0291, U+0316 - U+0319, U+031C -
  U+0320, U+0323 - U+0326, U+0329 - U+032A, U+032C - U+0332, U+0339 - U+033B,
  U+033E, U+033F (by Denis Jacquerye)
- fixed U+1E12, U+1E3C, U+1E4A, U+1E70 to have normal below diacritics
  (by Denis Jacquerye)
- fixed U+1E82, U+1E84 and U+1EF2 to have uppercase above diacritics (by
  Denis Jacquerye)
- added anchor points to some glyphs (by Denis Jacquerye)
- dropped "-Roman" from font names - affects both internal TTF names and
  names of generated files (by Stepan Roh)
- attempt to fix bug Vertical spacing too big for Mono by exchanging
  LineGap and OS2TypoLinegap values (proofed by Stefan Rank)
- added Greek capitals U+0391 - U+03A1, U+03A3 - U+03A9, U+03AA, U+03AB
  in Mono (by Ben Laenen)
- added the per ten thousand sign U+2031 (by Mederic Boquien)
- added U+2207, U+221D, U+221F, U+2227 - U+222A, and U+2261 (by David
  Lawrence Ramsey)
- new logo (by Gee Fung Sit)
- added U+0180, U+018E, U+201F, U+2024, U+2025, U+203D, U+2200, U+2203,
  U+2213, U+222C, U+222D, U+2263 to Sans (by Gee Fung Sit) 

Changes from 1.15 to 2.0:

- "Italized" basic glyphs in all Serif Oblique and their Condensed faces
  (by David Jez)
- added and improved TrueType instructions and related settings (by Keenan
  Pepper)
- added anchor points to some glyphs (by Denis Jacquerye)
- many new spacing and combining accents (by Denis Jacquerye)
- smart substitutions for transforming i and j to dottless form and for
  using uppercase diacritics (by Denis Jacquerye)
- fixed remaining erroneously slanted characters in Serif Oblique faces (by
  David Lawrence Ramsey)
- copied bullet in Sans faces to Sans Oblique faces for consistency (by
  David Lawrence Ramsey)
- added U+203C and U+2047-U+2049 (by David Lawrence Ramsey)
- added Greek glyphs to Serif (by Ben Laenen, Condensed merge by David Jez)
- fixed bug LTR glyphs behaving like RTL (by Ben Laenen)
- fixed wrong glyph directions (by David Jez)
- fixed repositioned accents in Condensed faces (by David Jez)

Changes from 1.14 to 1.15:

- added and improved TrueType instructions and related settings (by Keenan
  Pepper)
- fixed U+2302, U+2319 (by David Lawrence Ramsey)
- fixed yet another monospace bug (by Stepan Roh)
- fixed potential "too big ascender/descender" bug (by Stepan Roh)
- fixed U+026E and U+028E (by Denis Jacquerye)
- added U+0186, U+0190, U+0300 - U+0304, U+0306 - U+0308, U+030A - U+030C,
  U+0321, U+0322 (by Denis Jacquerye)
- added rest of Block Elements: U+2591 - U+2593 (by David Lawrence Ramsey)
- added U+2311, U+237D and U+2638 (by David Lawrence Ramsey)
- added U+01CD - U+01D4 (by Denis Jacquerye)
- fixed accents of U+00F2 - U+00F6 by replacing them with references in Mono
  Bold (by David Jez)
- added U+0490, U+0491 (by Eugeniy Meshcheryakov)
- added hints to U+0404 and U+0454 in Sans (by Eugeniy Meshcheryakov)
- completed Greek glyphs from U+0370 to U+03CF in Serif (by Ben Laenen)
- fixed shape of U+0255 in Sans Bold and Sans Bold Oblique (by Denis
  Jacquerye)

Changes from 1.13 to 1.14:

- fixed bug where Mono faces were not recognized as fixed pitch in Windows
  by correcting Venda glyphs (by David Jez)
- added and improved TrueType instructions (by Keenan Pepper)
- added 6 Uzbekian glyphs (by Mashrab Kuvatov)
- added Greek glyphs to Sans and Serif, changed pi and omega to fit in (by
  Ben Laenen)
- added IPA and related superscript glyphs (by Denis Jacquerye)
- fixed buggy Venda glyphs (by David Lawrence Ramsey and Stepan Roh)
- added U+2302, U+2310, U+2319 (by David Lawrence Ramsey)
- fixed slanted U+00AC in Serif Oblique faces (by David Lawrence Ramsey)
- added 29 glyphs from Block Elements (by David Lawrence Ramsey)

Changes from 1.12 to 1.13:

- removed all stems (PS hints) (requested by David Jez)
- added U+01D6, U+01DF, U+022B, U+022D and U+0231 (by Sander Vesik)
- added 10 Venda glyphs (by Dwayne Bailey)
- fixed bug when fonts had no name on Microsoft Windows (by Stepan Roh)
- updated 'missing' glyph U+FFFD (by David Jez)
- set TTF flag fsType to 'Installable Embedding' (= unrestricted usage)
  (idea by C. Tiffany)

Changes from 1.11 to 1.12:

- added long s (by James Cloos)
- prettier comma accent in gcommaaccent (by David Jez)
- added Hbar, hbar, kgreenlandic, napostrophe, Eng, eng, Tbar, tbar,
  afii57929 (by David Jez)
- changed Iogonek, iogonek, IJ, ij to look better (by David Jez)
- glyph uni0237 renamed to dotlessj (requested by David Jez)
- fixed accents for dcaron, lcaron, tcaron, Uogonek, uogonek in Serif (by
  David Jez)
- added U+2500 - U+257F box drawing glyphs to Sans Mono (by David Jez)
- fixed accents in Wcircumflex, Ycircumflex and Zdotaccent (by David Jez)
- extra kerning for F (by Sander Vesik)
- added 'missing' glyph U+FFFD (by David Jez)

Changes from 1.10 to 1.11:

- kerning updates (by Sander Vesik)
- added Iogonek, iogonek, IJ, ij, Uogonek, uogonek (from SuSE standard fonts
  by Adrian Schroeter, SuSE AG)
- added Gcommaaccent, gcommaaccent, Kcommaaccent, kcommaaccent,
  Lcommaaccent, lcommaaccent, Ncommaaccent, ncommaaccent, Rcommaaccent,
  rcommaaccent (by Stepan Roh)

Changes from 1.9 to 1.10:

- added U+022E, U+022F (by Sander Vesik)
- kerning updates for DejaVu Sans (by Sander Vesik)
- fixed too wide cyrillic glyphs in DejaVu Sans Mono (by Valentin Stoykov)
- fixed ligatures bug in Mono (by Stepan Roh)

Changes from 1.8 to 1.9:

- integrated Arev Cyrillics (by Danilo Segan)
- added U+01EA, U+01EB, U+01EC, U+01ED (by Sander Vesik)

Changes from 1.7 to 1.8:

- fixed accents in Serif Oblique and Serif Bold Oblique (by Stepan Roh)

Changes from 1.6 to 1.7:

- added automatically generated Condensed typefaces (by Stepan Roh)

Changes from 1.5 to 1.6:

- monospace bug fixed (by Stepan Roh)
- incorrect Bitstream foundry assigned by fontconfig and KDE Font Installer
fixed (by Stepan Roh)
- added automatically generated Oblique version of Serif typefaces (by
Stepan Roh)
- corrected cyrillic D and d (by Danilo Segan and David Jez)
- fixed accents position in Oblique version of Serif typefaces (by Danilo
Segan and Sander Vesik)
- fixed incorrect computation of OS2Win* fields (by Stepan Roh)
- added visiblespace U+2423 (by David Jez)
- fixed 'line height' bug by fixing ascender and descender values (by David
Jez and Stepan Roh)
- fixed part of 'worse than Vera' bug (by Peter Cernak)
- smaller comma accent U+0326 (by David Jez)

Changes from 1.4 to 1.5:

- added Cyrillics (96 characters) and Dcroat to the rest of typefaces (by
Danilo Segan)
- fixed bugs in some Cyrillic characters, some of them reported by Sander
Vesik (by Danilo Segan)
- added U+0100, U+0101, U+0112, U+0113, U+012A, U+012B, U+014C, U+014D,
U+016A, U+016B, U+01E2, U+01E3, U+0232 and U+0233 (by Sander Vesik)
- added Romanian characters (by Misu Moldovan)
- added U+0108, U+0109, U+010A, U+010B, U+0114, U+0115, U+0116, U+0117,
U+011C, U+011D, U+0120, U+0121, U+0124, U+0125, U+0128, U+0129, U+012C,
U+012D, U+0134, U+0135, U+014E, U+014F, U+0150, U+0151, U+015C, U+015D,
U+0168, U+0169, U+016C, U+016D, U+0170, U+0171 and U+0237 (by James
Crippen)
- added U+02BB, U+2010, U+2011, U+2012 and U+2015 (by Stepan Roh)

Changes from 1.3 to 1.4:

- added Polish characters (Aogonek, aogonek, Eogonek, eogonek, Nacute,
nacute, Sacute, sacute, Zacute, zacute, Zdotaccent, zdotaccent) (by Stepan
Roh)

Changes from 1.2 to 1.3:

- added Cyrillics (96 characters) and Dcroat to Sans typefaces (by Danilo
Segan from his BePa fonts)

Changes from 1.1 to 1.2:

- added Ldot, ldot, Wcircumflex, wcircumflex, Ycircumflex, ycircumflex,
  Wgrave, wgrave, Wacute, wacute, Wdieresis, wdieresis, Ygrave and ygrave
  (from The Olwen Font Family 0.2 by Dafydd Harries)

Changes from 1.0 to 1.1:

- added Lacute, lacute, Lcaron, lcaron, Racute and racute (by Peter Cernak)

Changes from 0.9.4 to 1.0:

- none, just changed version and updated README

Changes from 0.9.3 to 0.9.4:

- fixed TTF generation (kerning tables were missing)

Changes from 0.9.2 to 0.9.3:

- kerning of added characters
- proper caron shape for dcaron in Mono (by Ondrej Koala Vacha)
- minor visual changes

Changes from 0.9.1 to 0.9.2:

- internal bugged version

Changes from 0.9 to 0.9.1:

- proper caron shape for dcaron and tcaron
- minor visual changes

$Id: NEWS 2535 2013-08-25 15:21:17Z moyogo $
