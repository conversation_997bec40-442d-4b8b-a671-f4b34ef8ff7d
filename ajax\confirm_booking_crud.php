<?php
// Start session before any output
session_start();
require('../admin/inc/db_config.php');
require('../admin/inc/essentials.php');
date_default_timezone_set("Africa/Lagos");

if (isset($_POST['check_availability'])) {
    // Sanitize input data
    $frm_data = filteration($_POST);

    $status = "";
    $result = "";

    // Check-in and Check-out validation  
    $today_date = new DateTime(date("Y-m-d"));
    $checkin_date = new DateTime($frm_data['check_in']);
    $checkout_date = new DateTime($frm_data['check_out']);

    // Case: Check-in and Check-out are the same date
    if ($checkin_date == $checkout_date) {
        $status = 'check_in_out_equal';
        $result = json_encode(["status" => $status]);
    }
    // Case: Check-out date is earlier than Check-in date
    else if ($checkout_date < $checkin_date) {
        $status = 'check_out_earlier';
        $result = json_encode(["status" => $status]);
    }
    // Case: Check-in date is earlier than today
    else if ($checkin_date < $today_date) {
        $status = 'check_in_earlier';
        $result = json_encode(["status" => $status]);
    }

    // If there's an error (status is set), return the error
    if ($status != '') {
        echo $result;
    }
    else {
        // Check if room session exists
        if (!isset($_SESSION['room'])) {
            $status = 'room_not_found';
            $result = json_encode(["status" => $status]);
            echo $result;
            exit;
        }

        // Query to check overlapping bookings
        $tb_query = "SELECT COUNT(*) AS `total_bookings` FROM `booking_order`
                    WHERE booking_status=? AND room_id=?
                    AND check_out > ? AND check_in < ?";

        $values = ['booked', $_SESSION['room']['id'], $frm_data['check_in'], $frm_data['check_out']];

        // Execute query with error handling
        $tb_result = select($tb_query, $values, 'siss');
        if (!$tb_result) {
            $status = 'query_error';
            $result = json_encode(["status" => $status]);
            echo $result;
            exit;
        }
        
        $tb_fetch = mysqli_fetch_assoc($tb_result);

        // Get room quantity
        $rq_result = select("SELECT `quantity` FROM `rooms` WHERE `id`=?", [$_SESSION['room']['id']], 'i');
        if (!$rq_result) {
            $status = 'query_error';
            $result = json_encode(["status" => $status]);
            echo $result;
            exit;
        }
        
        $rq_fetch = mysqli_fetch_assoc($rq_result);
        
        // Check if any rooms are available
        if (($rq_fetch['quantity'] - $tb_fetch['total_bookings']) <= 0) {
            $status = 'unavailable';
            $result = json_encode(['status' => $status]);
            echo $result;
            exit;
        }

        // Calculate price
        if (isset($_SESSION['room']['price'])) {
            // Remove commas from the price string
            $price_str = $_SESSION['room']['price'];
            $price_str = str_replace(',', '', $price_str);
            
            // Convert the price to a numeric value
            $price = floatval($price_str);
            
            // Check if price is valid
            if ($price > 0) {
                $count_days = date_diff($checkin_date, $checkout_date)->days;
                $payment = $price * $count_days;
                
                // Format the price with commas
                $formatted_price = number_format($payment);
                
                // Update session with payment and availability info
                $_SESSION['room']['payment'] = $payment;
                $_SESSION['room']['available'] = true;
                
                // Respond with success
                $result = json_encode([
                    "status" => 'available', 
                    "days" => $count_days, 
                    "payment" => $formatted_price
                ]);
                echo $result;
            } else {
                // Handle invalid price
                $status = 'invalid_price';
                $result = json_encode([
                    "status" => $status, 
                    "message" => "Room price is invalid"
                ]);
                echo $result;
            }
        } else {
            // Handle missing price
            $status = 'price_not_set';
            $result = json_encode([
                "status" => $status, 
                "message" => "Room price is not set"
            ]);
            echo $result;
        }
    }
}
?>