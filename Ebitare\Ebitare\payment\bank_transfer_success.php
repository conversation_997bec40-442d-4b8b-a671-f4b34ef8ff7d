<?php
require('../admin/inc/db_config.php');
require('../admin/inc/essentials.php');

session_start();

// Redirect if accessed directly without success flag
if (!isset($_SESSION['bank_transfer_success']) || $_SESSION['bank_transfer_success'] !== true) {
    header("Location: ../index.php");
    exit;
}

// Get reference from URL
$reference = isset($_GET['ref']) ? htmlspecialchars($_GET['ref']) : 'N/A';

// Clear the success flag to prevent refresh issues
$_SESSION['bank_transfer_success'] = false;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="Ebitare">
    <title>EBITARE - Payment Submitted</title>
    
    
    
    

    <!-- GOOGLE WEB FONT-->
    <link rel="preconnect" href="https://fonts.googleapis.com/">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Caveat:wght@400;500&amp;family=Montserrat:wght@300;400;500;600;700&amp;display=swap" rel="stylesheet">

    <!-- BASE CSS -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    <link href="../css/style.css" rel="stylesheet">
    <link href="../css/vendors.min.css" rel="stylesheet">

    <!-- YOUR CUSTOM CSS -->
    <link href="../css/custom.css" rel="stylesheet">
    
    <style>
        .success-icon {
            color: #78cfcf;
            font-size: 64px;
            margin-bottom: 20px;
            display: inline-block;
            height: 80px;
            width: 80px;
            line-height: 80px;
            border-radius: 50%;
            background: rgba(120, 207, 207, 0.1);
        }
        
        .success-card {
            padding: 40px;
            text-align: center;
            background: #fff;
            border-radius: 5px;
            box-shadow: 0 0 15px rgba(0,0,0,0.05);
        }
        
        .reference-box {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #78cfcf;
        }
        
        .reference-value {
            font-size: 1.2rem; 
            font-weight: bold;
            color: #333;
        }
    </style>
</head>

<body>

    <div class="bg_white" id="first_section">
        <div class="container margin_120_95">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="success-card">
                        <div class="success-icon">
                            <i class="bi bi-check-lg"></i>
                        </div>
                        
                        <div class="title">
                            <small>Transaction Successful</small>
                            <h2>Payment Details Submitted!</h2>
                        </div>
                        
                        <p>Thank you for choosing Ebitare Hotel. Your bank transfer details have been received and are being verified. You will receive a confirmation email once your payment is confirmed.</p>
                        
                        <div class="reference-box">
                            <h4>Your Reference ID</h4>
                            <p class="reference-value"><?php echo $reference; ?></p>
                            <p>Please save this reference ID for your records.</p>
                        </div>
                        
                        <p>If you have any questions, please contact our customer <NAME_EMAIL> or call +************ 567.</p>
                        
                        <a href="../bookings.php" class="btn_1">View Bookings</a>
                    </div>
                </div>
            </div>
            <!-- /row -->
        </div>
        <!-- /container -->
    </div>
    <!-- /bg_white -->
</main>

<footer class="bg-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">© Ebitare Hotel - <?php echo date('Y'); ?></p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="terms.php">Terms and Conditions</a>
                </div>
            </div>
        </div>
    </footer>
<!-- /footer -->

<div class="progress-wrap">
    <svg class="progress-circle svg-content" width="100%" height="100%" viewBox="-1 -1 102 102">
        <path d="M50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98"/>
    </svg>
</div>
<!-- /back to top -->

<!-- COMMON SCRIPTS -->
<script src="../js/common_scripts.js"></script>
<script src="../js/common_functions.js"></script>
<script src="../phpmailer/validate.js"></script>

</body>
</html>