/* ------ Disable arrow Nav for Firefox Mac Os (Feature not supported)----------- */
@-moz-document url-prefix() {
    .vertical-nav {
        display: none !important;
    }
}
/* ------ Disable arrow Nav for Safari Mac Os (Feature not supported) ----------- */
@supports (hanging-punctuation: first) and (font: -apple-system-body) and (-webkit-appearance: none) {
    .vertical-nav {
        display: none !important;
    }
}
/* ------ Menu of the day ----------- */
body {
  color: #ffffff;
  background-color: #292b33;
}

body::before {
  /* never visible - this is used in jQuery to check the current MQ */
  content: "mobile";
  display: none;
}

@media only screen and (min-width: 1170px) {
  body::before {
    /* never visible - this is used in jQuery to check the current MQ */
    content: "desktop";
  }
}
/* -------------------------------- 

Main Components 

-------------------------------- */
@media only screen and (min-width: 1170px) {
  .menu-section {
    height: 100vh;
  }
}
.menu-section .intro {
  position: relative;
  top: 45%;
  transform: translateY(-50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  font-weight: 700;
  font-size: 46px;
  font-size: 2.875rem;
  color: #fff;
  letter-spacing: normal;
}
@media (max-width: 767px) {
  .menu-section .intro {
    font-size: 32px;
    font-size: 2rem;
  }
}
.menu-section .intro span {
  display: block;
  font-family: "Lora", serif;
  font-size: 24px;
  font-size: 1.5rem;
  font-weight: normal;
  color: #fff;
}
@media (max-width: 767px) {
  .menu-section .intro span {
    font-size: 21px;
    font-size: 1.3125rem;
  }
}

.txt_container ul {
  list-style: none;
  padding: 0;
  margin: 0;
  text-align: center;
}
.txt_container ul li h3 {
  font-size: 21px;
  font-size: 1.3125rem;
  margin-bottom: 0;
}
.txt_container ul li p {
  font-family: "Lora", serif;
  font-size: 16px;
  font-size: 1rem;
}

.m_title {
  margin-bottom: 45px;
}
.m_title h2:before {
  content: url("../img/daymenu/divider_2.svg");
  display: block;
  margin-bottom: 15px;
}
.m_title p {
  font-size: 16px;
  font-size: 1rem;
  color: #999;
}

.phone_us {
  font-size: 24px;
  font-size: 1.5rem;
  font-weight: 600;
}

.full-container,
.half-container {
  transform: translateZ(0);
  backface-visibility: hidden;
}

.full-container {
  transform-origin: center center;
}

.menu-section:first-of-type .full-container {
  visibility: visible;
  height: 100vh;
  background-color: #24262d;
}

@media only screen and (min-width: 1170px) {
  .full-container {
    position: fixed;
    width: 100%;
    min-height: 100vh;
    top: 0;
    left: 0;
    height: 100vh;
    box-shadow: 0 0 40px rgba(0, 0, 0, 0.7);
    visibility: hidden;
  }
  .menu-section:first-of-type .full-container {
    visibility: visible;
  }
  .full-container > * {
    visibility: visible;
  }
}
.half-container {
  background: #fff url(../img/daymenu/pattern_menu.jpg) no-repeat center center;
  background-size: cover;
  color: #263b40;
  text-align: center;
}

.half-container:nth-of-type(1) {
  height: 60vh;
  background-color: #fff;
  background-position: center center;
  background-repeat: no-repeat;
}

.half-container:nth-of-type(2) {
  padding: 4em 10%;
}

.menu-section:nth-of-type(2) .half-container:first-of-type {
  background-image: url("../img/daymenu/img-1.jpg");
  background-size: cover;
}

.menu-section:nth-of-type(3) .half-container:first-of-type {
  background-image: url("../img/daymenu/img-2.jpg");
  background-size: cover;
}

.menu-section:nth-of-type(4) .half-container:first-of-type {
  background-image: url("../img/daymenu/img-3.jpg");
  background-size: cover;
}

.menu-section:nth-of-type(5) .half-container:first-of-type {
  background-image: url("../img/daymenu/img-4.jpg");
  background-size: cover;
}

.menu-section:nth-of-type(6) .half-container:first-of-type {
  background-image: url("../img/daymenu/img-5.jpg");
  background-size: cover;
}

@media only screen and (min-width: 1170px) {
  .half-container {
    height: 100vh !important;
    width: 50%;
    position: absolute;
    top: 0;
  }
  .half-container .txt_container {
    position: absolute;
    left: 50%;
    top: 50%;
    bottom: auto;
    right: auto;
    transform: translateX(-50%) translateY(-50%);
    width: 100%;
    padding: 0 20%;
    text-align: center;
  }
  .menu-section:nth-of-type(even) .half-container:first-of-type,
  .menu-section:nth-of-type(odd) .half-container:nth-of-type(2) {
    left: 0;
    transform: translateX(-100%);
  }
  .menu-section:nth-of-type(odd) .half-container:first-of-type,
  .menu-section:nth-of-type(even) .half-container:nth-of-type(2) {
    right: 0;
    transform: translateX(100%);
  }
}
.vertical-nav {
  position: fixed;
  z-index: 1;
  right: 15px;
  top: 50%;
  bottom: auto;
  transform: translateY(-50%);
  display: none;
}
.vertical-nav a {
  display: block;
  height: 40px;
  width: 40px;
  overflow: hidden;
  text-indent: 100%;
  white-space: nowrap;
  background: transparent url(../img/daymenu/icon-arrow.svg) no-repeat center center;
  transition: opacity 0.2s 0s, visibility 0.2s 0s;
}
.vertical-nav a.prev {
  transform: rotate(180deg);
  margin-bottom: 10px;
}
.vertical-nav a.inactive {
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.2s 0s, visibility 0s 0.2s;
}

ul.top-nav,
ul.vertical-nav {
  list-style: none;
  margin: 0;
  padding: 0;
}

.top-nav {
  position: fixed;
  z-index: 1;
  right: 15px;
  top: 2%;
  bottom: auto;
}
.top-nav a {
  display: block;
  height: 40px;
  width: 40px;
  font-size: 36px;
  opacity: 0.5;
  cursor: pointer;
}
.top-nav a:hover {
  color: #24262d;
  opacity: 1;
}

@media only screen and (min-width: 1170px) {
  .vertical-nav {
    display: block;
  }
}