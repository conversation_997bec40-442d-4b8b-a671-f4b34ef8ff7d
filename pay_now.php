<?php
require('admin/inc/db_config.php');
require('admin/inc/essentials.php');

date_default_timezone_set("Africa/Lagos");
session_start();

header("Pragma: no-cache");
header("Cache-Control: no-cache");
header("Expires:0");

// STEP 1: Sanitize incoming POST data
$data = filteration($_POST);  // or $data = $_POST if filteration() is not available

// Set default values for potentially missing fields
if (!isset($data['address'])) {
    $data['address'] = '';  // Default empty address
}

// If user ID is directly passed, save it
if(isset($data['user_id']) && !empty($data['user_id'])) {
    $_SESSION['uId'] = $data['user_id'];
}

// Save check-in and check-out dates to session
if(isset($data['checkin']) && isset($data['checkout'])) {
    $_SESSION['room']['checkin'] = $data['checkin'];
    $_SESSION['room']['checkout'] = $data['checkout'];
}

// If payment amount is sent, save it to session
if(isset($data['payment'])) {
    $_SESSION['room']['payment'] = $data['payment'];
}

// STEP 2: Handle user insert if not already stored
$user_check = select("SELECT * FROM `user_cred` WHERE `email` = ? OR `phone` = ? LIMIT 1", 
 [$data['email'], $data['phone']], "ss");

if (mysqli_num_rows($user_check) == 0) {
    // Insert new user
    $profile = "";
    
    // Create hashed password - assuming you're using PHP's password_hash
    $default_password = "ebitare";
    // Use the same hashing method your system uses
    // Common options: password_hash or md5 or sha1
    
    // Option 1: If using password_hash (recommended)
    $password = password_hash($default_password, PASSWORD_DEFAULT);
    
    // Option 2: If using MD5 (less secure but common in older systems)
    // $password = md5($default_password);
    
    // Option 3: If using SHA1 (less secure but common in older systems)
    // $password = sha1($default_password);
    
    // Option 4: If no hashing is being used (not recommended for security)
    // $password = $default_password;
    
    $token = bin2hex(random_bytes(16));
    $datetime = date("Y-m-d H:i:s");

    $query = "INSERT INTO `user_cred`(`name`, `email`, `address`, `phone`, `profile`, `password`, `is_verified`, `token`, `t_expire`, `status`, `datentime`) 
              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    $values = [
        $data['name'],
        $data['email'],
        $data['address'],
        $data['phone'],
        $profile,
        $password,
        1,
        $token,
        NULL,
        1,
        $datetime
    ];

    $insert_result = insert($query, $values, "sssssssssis");

    if ($insert_result) {
        $_SESSION['uId'] = $con->insert_id;
    } else {
        die("User insert failed: " . mysqli_error($con));
    }
} else {
    $existing_user = mysqli_fetch_assoc($user_check);
    $_SESSION['uId'] = $existing_user['id'];
}

// Debugging: Store user data in session for display
$_SESSION['user'] = [
    'name' => $data['name'],
    'email' => $data['email'],
    'phone' => $data['phone'],
    'address' => $data['address']
];

// For payment processing, ensure we have payment amount
if (!isset($_SESSION['room']['payment']) || $_SESSION['room']['payment'] === null) {
    echo "<script>alert('Payment information is missing. Please go back and select your dates.');</script>";
    echo "<script>window.location.href='confirm_booking.php?id=".$_SESSION['room']['id']."';</script>";
    exit;
}

// Handle form submission
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['pay_now'])) {
    $price = floatval($_SESSION['room']['payment']);
    
    // Sanitize user input
    $email = htmlspecialchars($_POST["email"]);
    $name = htmlspecialchars($_POST["name"] ?? '');
    $phone = htmlspecialchars($_POST["phone"] ?? '');
    $checkin = htmlspecialchars($_POST["checkin"] ?? $_SESSION['room']['checkin'] ?? '');
    $checkout = htmlspecialchars($_POST["checkout"] ?? $_SESSION['room']['checkout'] ?? '');
    
    $gateway = isset($_POST["gateway"]) ? htmlspecialchars($_POST["gateway"]) : '';

    $paystackSecretKey = PAYSTACK_KEY; 

    if ($gateway === "paystack") {
        $endpoint = ENDPOINT_URL;
        $headers = [
            "Authorization: Bearer $paystackSecretKey", // Replace with your actual Paystack key
            'Content-Type: application/json'
        ];

        $reference = uniqid("EBITARE_");
        $_SESSION['payment_reference'] = $reference;

        $data = [
            'amount' => $price * 100000,  // in kobo
            'currency' => 'NGN',
            'reference' => $reference,
            'email' => $email,
            'callback_url' => CALLBACK_URL,
            'metadata' => [
                'custom_fields' => [
                    [
                        'display_name' => 'Full Name',
                        'variable_name' => 'full_name',
                        'value' => $name
                    ],
                    [
                        'display_name' => 'Phone Number',
                        'variable_name' => 'phone_number',
                        'value' => $phone
                    ],
                    [
                        'display_name' => 'Check in',
                        'variable_name' => 'check_in',
                        'value' => $checkin
                    ],
                    [
                        'display_name' => 'Check out',
                        'variable_name' => 'check_out',
                        'value' => $checkout
                    ],
                    [
                        'display_name' => 'Room Name',
                        'variable_name' => 'room_name',
                        'value' => $_SESSION['room']['name'] ?? ''
                    ],
                    [
                        'display_name' => 'Room ID',
                        'variable_name' => 'room_id',
                        'value' => $_SESSION['room']['id'] ?? ''
                    ],
                    [
                        'display_name' => 'User ID',
                        'variable_name' => 'user_id',
                        'value' => $_SESSION['uId'] ?? ''
                    ]
                ]
            ]
        ];
        
        $ch = curl_init();

        curl_setopt_array($ch, [
            CURLOPT_URL => $endpoint,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => $headers
        ]);

        $responseData = curl_exec($ch);

        if ($responseData === false) {
            echo "cURL Error: " . curl_error($ch);
        } else {
            $decodedResponse = json_decode($responseData, true);
            if ($decodedResponse['status'] == 'success') {
                header("Location: " . $decodedResponse['data']['authorization_url']);
                exit();
            } else {
                echo "Error initializing payment: " . $decodedResponse['message'];
            }
        }

        curl_close($ch);
    } elseif ($gateway === "bank_transfer") {
        header("Location: payment/bank_transfer.php");
        exit;
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="Ebitare">
    <title>EBITARE - Payment</title>
    
  
    
    <!-- Favicons -->
    <link rel="shortcut icon" href="img/favicon.ico" type="image/x-icon">
    
    <!-- GOOGLE WEB FONT -->
    <link rel="preconnect" href="https://fonts.googleapis.com/">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Caveat:wght@400;500&amp;family=Montserrat:wght@300;400;500;600;700&amp;display=swap" rel="stylesheet">

    <!-- BASE CSS -->
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <link href="css/vendors.min.css" rel="stylesheet">

    <!-- YOUR CUSTOM CSS -->
    <link href="css/custom.css" rel="stylesheet">
    
    <style>
        .payment-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .payment-heading {
            border-bottom: 2px solid #f8f9fa;
            padding-bottom: 15px;
            margin-bottom: 25px;
        }
        
        .booking-summary {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 25px;
            border-left: 4px solid #dca57d;
        }
        
        .booking-summary h5 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .summary-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        
        .summary-item.total {
            font-weight: bold;
            border-top: 1px solid #ddd;
            padding-top: 10px;
            margin-top: 10px;
        }
        
        .payment-options {
            margin-bottom: 25px;
        }
        
        .payment-options label {
            display: block;
            margin-bottom: 10px;
            cursor: pointer;
        }
        
        .btn-payment {
            background-color: #dca57d;
            border: none;
            color: white;
            padding: 12px 20px;
            width: 100%;
            font-weight: 600;
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        
        .btn-payment:hover {
            background-color: #c79469;
        }
        
        .secure-badge {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: #6c757d;
            margin-top: 15px;
        }
        
        .secure-badge i {
            margin-right: 5px;
            color: #28a745;
        }
        
        /* Remove overlay effects */
        .layer {
            display: none !important;
        }
        
        #preloader {
            display: none !important;
        }
        
        .opacity-mask {
            opacity: 1 !important;
        }
        
        /* Simple header for payment page */
        .payment-header {
            background-color: #343a40;
            color: white;
            padding: 15px 0;
            margin-bottom: 30px;
        }
        
        .payment-header h2 {
            margin: 0;
            font-size: 24px;
        }
    </style>
</head>

<body>
    <!-- Simple header instead of including complex header -->
    <div class="payment-header">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h2>Ebitare Hotel</h2>
                </div>
                <div class="col-md-6 text-end">
                    <a href="index.php" class="text-white">Back to Home</a>
                </div>
            </div>
        </div>
    </div>

    <main>
        <div class="container margin_60_60">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="payment-container">
                        <div class="payment-heading">
                            <h3>Complete Your Payment</h3>
                            <p>Secure checkout for your stay at Ebitare Hotel</p>
                        </div>
                        
                        <div class="booking-summary">
                            <h5>Booking Summary</h5>
                            <div class="summary-item">
                                <span>Room:</span>
                                <span><?php echo $_SESSION['room']['name'] ?? 'N/A'; ?></span>
                            </div>
                            <div class="summary-item">
                                <span>Price Per Night:</span>
                                <span>₦<?php echo $_SESSION['room']['price'] ?? 'N/A'; ?></span>
                            </div>
                            <div class="summary-item">
                                <span>Check-in Date:</span>
                                <span><?php echo $_SESSION['room']['checkin'] ?? 'N/A'; ?></span>
                            </div>
                            <div class="summary-item">
                                <span>Check-out Date:</span>
                                <span><?php echo $_SESSION['room']['checkout'] ?? 'N/A'; ?></span>
                            </div>
                            <div class="summary-item total">
                                <span>Total Amount:</span>
                                <span>₦<?php echo $_SESSION['room']['payment'] ?? 'N/A'; ?></span>
                            </div>
                        </div>
                        
                        <form action="" method="POST">
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label class="form-label">Full Name</label>
                                    <input type="text"
                                        name="name"
                                        class="form-control shadow-none"
                                        value="<?php echo htmlspecialchars($_POST['name'] ?? ($_SESSION['user']['name'] ?? '')); ?>"
                                        readonly required>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Email</label>
                                    <input type="email"
                                        name="email"
                                        class="form-control shadow-none"
                                        value="<?php echo htmlspecialchars($_POST['email'] ?? ($_SESSION['user']['email'] ?? '')); ?>"
                                        readonly required>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Phone Number</label>
                                    <input type="tel"
                                        name="phone"
                                        class="form-control shadow-none"
                                        value="<?php echo htmlspecialchars($_POST['phone'] ?? ($_SESSION['user']['phone'] ?? '')); ?>"
                                        readonly required>
                                </div>
                                
                                <!-- Hidden fields for check-in and check-out dates -->
                                <input type="hidden" name="checkin" value="<?php echo htmlspecialchars($_SESSION['room']['checkin'] ?? ''); ?>">
                                <input type="hidden" name="checkout" value="<?php echo htmlspecialchars($_SESSION['room']['checkout'] ?? ''); ?>">
                                
                                <div class="col-md-12 mb-4">
                                    <label class="form-label">Payment Method</label>
                                    
                                    <div class="payment-options">
                                        <label for="paystack" class="d-flex align-items-center border rounded p-3 mb-2">
                                            <input type="radio" name="gateway" value="paystack" id="paystack" checked>
                                            <span class="ms-2">Paystack (Credit/Debit Card)</span>
                                        </label>
                                        
                                        <label for="bank_transfer" class="d-flex align-items-center border rounded p-3">
                                            <input type="radio" name="gateway" value="bank_transfer" id="bank_transfer">
                                            <span class="ms-2">Bank Transfer</span>
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="col-md-12">
                                    <button type="submit" name="pay_now" class="btn-payment">Complete Payment</button>
                                    
                                    <div class="secure-badge">
                                        <i class="bi bi-shield-lock"></i>
                                        <span>Secure Payment Processing</span>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Simple footer -->
    <footer class="bg-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">© Ebitare Hotel - <?php echo date('Y'); ?></p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="terms.php">Terms and Conditions</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- COMMON SCRIPTS - Just include essential ones -->
    <script src="js/common_scripts.js"></script>
    
    <script>
    // Ensure no overlays or preloaders are active
    document.addEventListener('DOMContentLoaded', function() {
        // Remove any potential overlay elements
        const preloader = document.getElementById('preloader');
        if (preloader) {
            preloader.style.display = 'none';
        }
        
        const layers = document.querySelectorAll('.layer');
        layers.forEach(function(layer) {
            layer.style.display = 'none';
        });
    });
    </script>
</body>
</html>