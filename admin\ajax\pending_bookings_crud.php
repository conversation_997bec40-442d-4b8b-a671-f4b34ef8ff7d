<?php
require('../inc/db_config.php');
require('../inc/essentials.php');
adminLogin();

if (isset($_POST['get_bookings'])) {
    $frm_data = filteration($_POST);
    
    // First, check if the columns exist in booking_order table
    $check_columns_query = "SHOW COLUMNS FROM `booking_order` LIKE 'sender_name'";
    $res_check = mysqli_query($con, $check_columns_query);
    $has_sender_details = (mysqli_num_rows($res_check) > 0);

    $query = "SELECT bo.*, bd.* FROM `booking_order` bo
            INNER JOIN `booking_details` bd ON bo.booking_id = bd.booking_id
            WHERE 
                (bo.order_id LIKE ? OR bd.customer_phone LIKE ? OR bd.customer_name LIKE ? OR bd.customer_email LIKE ?)
              AND bo.booking_status = ?
            ORDER BY bo.booking_id ASC";
    
    $values = [
        "%{$frm_data['search']}%",
        "%{$frm_data['search']}%",
        "%{$frm_data['search']}%",
        "%{$frm_data['search']}%",
        "pending"
    ];
    
    $res = select($query, $values, 'sssss');
    $i = 1;
    $table_data = "";
    
    if (mysqli_num_rows($res) == 0) {
        echo "<tr><td colspan='5'><br>No Pending Bookings Found</br></td></tr>";
        exit();
    }
    
    while ($data = mysqli_fetch_assoc($res)) {
        $date = date("d-m-Y", strtotime($data['datentime']));
        $checkin = date("d-m-Y", strtotime($data['check_in']));
        $checkout = date("d-m-Y", strtotime($data['check_out']));
        
        // Payment details section
        $payment_details = "";
        
        // Only add sender details if the columns exist
        if ($has_sender_details) {
            $payment_details .= "
                <b>Sender: </b> " . ($data['sender_name'] ?? 'N/A') . "
                <br>
                <b>Account: </b> " . ($data['sender_account_number'] ?? 'N/A') . "
                <br>
                <b>Bank: </b> " . ($data['bank_name'] ?? 'N/A') . "
            ";
            
            // Payment proof image - ensure the path is constructed properly
            if (!empty($data['payment_proof'])) {
                // Use the correct path to the payment proof image
                $img_path = SITE_URL . $data['payment_proof'];
                $img_path_escaped = addslashes($img_path);
                $payment_details .= "
                    <br>
                    <button type='button' onclick='view_payment_proof(\"$img_path_escaped\")' class='btn btn-info btn-sm fw-bold shadow-none mt-2'>
                        <i class='bi bi-image'></i> View Payment Proof
                    </button>
                ";
            }
        }
        
        $table_data .= "
            <tr>
                <td>$i</td>
                <td>
                    <span class='badge bg-primary'>
                        Order ID: {$data['order_id']}
                    </span>
                    <br>
                    <b>Name: </b> {$data['customer_name']}
                    <br>
                    <b>Email: </b> {$data['customer_email']}
                    <br>
                    <b>Phone No: </b> {$data['customer_phone']}
                </td>
                <td>
                    <b>Room: </b> {$data['room_name']}
                    <br>
                    <b>Check-in: </b> $checkin
                    <br>
                    <b>Check-out: </b> $checkout
                    <br>
                    <b>Date: </b> $date
                    <br>
                </td>
                <td>
                    <b>₦</b>{$data['trans_amt']}
                    <br>
                    $payment_details
                </td>
                <td>
                    <button type='button' onclick='confirm_booking({$data['booking_id']})' class='btn btn-success btn-sm-bold fw-bold shadow-none'>
                        <i class='bi bi-check-circle'></i> Confirm Booking
                    </button>
                </td>
            </tr>
        ";
        
        $i++;
    }
    
    echo $table_data;
}

if (isset($_POST['confirm_booking'])) {
    $frm_data = filteration($_POST);
    
    $query = "UPDATE `booking_order` SET `booking_status`=? WHERE `booking_id`=?";
    $values = ['booked', $frm_data['booking_id']];
    $res = update($query, $values, 'si');
    
    echo $res;
}
?>