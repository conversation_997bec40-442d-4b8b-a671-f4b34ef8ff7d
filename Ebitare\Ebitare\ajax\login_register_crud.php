<?php
require('../admin/inc/db_config.php');
require ('../admin/inc/essentials.php');
require __DIR__ . '/../phpmailer/vendor/autoload.php';
date_default_timezone_set("Africa/Lagos");

// Import PHPMailer classes into the global namespace
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use P<PERSON>Mailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

// Send email function with return value
function send_mail($email, $token, $type){

    if ($type == "email_confirmation"){
        $page = 'email_confirm.php';
        $subject = "Account Verification Link";
        $content = "verify your Email";
        $click = " Click here to verify your email";
    }
    else{
        $page = 'index.php';
        $subject = "Account Reset Link";
        $content = "reset your Account";
        $click   = "Click here to reset your Account";

    }

    $mail = new PHPMailer(true);

    try {
        // Server settings// you can store all in the essential file using define
        // $mail->SMTPDebug = SMTP::DEBUG_SERVER;                      // Enable verbose debug output
        $mail->isSMTP();                                            // Send using SMTP
        $mail->Host       = 'smtp.gmail.com';                        // Set the SMTP server to send through
        $mail->SMTPAuth   = true;                                    // Enable SMTP authentication
        $mail->Username   = '<EMAIL>';               // SMTP username
        $mail->Password   = 'dfdysteqbyuznadx';                      // SMTP password
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;             // Enable implicit TLS encryption
        $mail->Port       = 465;                                     // TCP port to connect to; use 587 if you have set `SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS`

        // Recipients
        $mail->setFrom('<EMAIL>', 'EBITARE HOTEL');
        $mail->addAddress($email,);                             // Add a recipient

        // Content
        $mail->isHTML(true);                                         // Set email format to HTML
        $mail->Subject = $subject;
        
        $mail->Body = "
                Click the link to $content: <br>
                <a href='" . htmlspecialchars(SITE_URL) . htmlspecialchars($page) . "?" . htmlspecialchars($type) . "&email=" . urlencode($email) . "&token=" . urlencode($token) . "'>
                    " . htmlspecialchars($click) . "
                </a>
            ";
                
    
        $mail->AltBody = "
         Click the link to $content: <br>
                <a href='" . htmlspecialchars(SITE_URL) . htmlspecialchars($page) . "?" . htmlspecialchars($type) . "&email=" . urlencode($email) . "&token=" . urlencode($token) . "'>
                    " . htmlspecialchars($click) . "
                </a>
            ";

        // Send the email
        $mail->send();
        return true;  // Indicate success
    } catch (Exception $e) {
        return false; // Indicate failure
    }
}

if(isset($_POST['register']))
{

    $data = filteration($_POST);

    // Check if user exists
    $u_exist = select("SELECT * FROM `user_cred` WHERE `email`= ? OR `phone` = ? LIMIT 1", 
    [$data['email'], $data['phone']], "ss");

    if(mysqli_num_rows($u_exist) != 0){
        $u_exist_fetch = mysqli_fetch_assoc($u_exist);
        echo ($u_exist_fetch['email'] == $data['email']) ? 'email_already' : 'phone_already';
        exit;
    }

    // Match password and confirm Field
    if($data['pass'] != $data['cpass']){
        echo 'pass_mismatch';
        exit;
    }    

    // Upload user image to server
    $img = uploadUserImage($_FILES['profile']);

    if($img == 'inv_img'){
        echo 'inv_img';
        exit;
    }
    else if($img == 'upd_failed'){
        echo 'upd_failed';
        exit;
    }

    // Send confirmation link to user's email
    $token = bin2hex(random_bytes(16));

    if(!send_mail($data['email'], $token,"email_confirmation")){
        echo 'mail_failed';
        exit;
    }

    // Encrypt the password
    $enc_pass = password_hash($data['pass'], PASSWORD_BCRYPT);

    // Insert user data into database
    $query = "INSERT INTO `user_cred`(`name`, `email`, `address`, `phone`, `profile`, `password`, `token`) 
              VALUES (?,?,?,?,?,?,?)";
    $values = [$data['name'], $data['email'], $data['address'], $data['phone'], $img, $enc_pass, $token];

    if(insert($query, $values, 'sssssss')){
        echo 1;  // Success
    } else {
        echo 'ins_failed';  // Insert failed
    }
}

if(isset($_POST['login']))
{

    $data = filteration($_POST);

    // Check if user exists
    $u_exist = select("SELECT * FROM `user_cred` WHERE `email`= ? OR `phone` = ? LIMIT 1", 
    [$data['email_mob'], $data['email_mob']], "ss");

    if(mysqli_num_rows($u_exist) == 0){
        echo 'inv_email_mob';
    }
    else{
        $u_fetch = mysqli_fetch_assoc($u_exist);
        if($u_fetch['is_verified'] == 0){
            echo 'not verified';
        }
        else if($u_fetch['status'] ==0 ){
            echo 'inactive';
        }
        else{
            if(!password_verify($data['pass'], $u_fetch['password'])){
                echo 'invalid_pass';
            }
            else{
                session_start();
                $_SESSION['login'] =true;
                $_SESSION['uId'] =$u_fetch['id'];
                $_SESSION['uName'] =$u_fetch['name'];
                $_SESSION['uPic'] =$u_fetch['profile'];
                $_SESSION['uPhone'] =$u_fetch['phone'];
                echo 1;

            }
        }
    }
        
}

if(isset($_POST['forgot_pass']))
{

    $data = filteration($_POST);

    // Check if user exists
    $u_exist = select("SELECT * FROM `user_cred` WHERE `email`= ? LIMIT 1", 
    [$data['email']], "s");

    if(mysqli_num_rows($u_exist) == 0){
        echo 'inv_email';
    }
    else{
        $u_fetch = mysqli_fetch_assoc($u_exist);
        if($u_fetch['is_verified'] == 0){
            echo 'not verified';
        }
        else if($u_fetch['status'] ==0 ){
            echo 'inactive';
        }
        else{
            //send reset link to email

            $token=bin2hex(random_bytes(16));

            if(!send_mail($data['email'],$token,"account_recovery")){
                echo 'mail_failed';
            }
            else{               
                $date = date("Y-m-d");
                $query = mysqli_query($con, "UPDATE `user_cred` SET  `token`='$token', `t_expire` ='$date'
                WHERE `id`='$u_fetch[id]' ");

                if($query){
                    echo 1;
                }
                else{
                    echo 'upd_failed';
                }
            }
        }
    }
        
}


if (isset($_POST['recover_user'])) {
    $data = filteration($_POST); // Assuming filteration is a custom sanitizing function

    // Hash the password
    $enc_pass = password_hash($data['pass'], PASSWORD_BCRYPT);

    // SQL query to update the password and invalidate the token
    $query = "UPDATE `user_cred` SET `password`=?, `token`=?, `t_expire`=? 
              WHERE `email`=? AND `token`=?";

    // Assuming you want to set the token and expiry to null after password reset
    $values = [$enc_pass, null, null, $data['email'], $data['token']];

    // Call the update function, assuming it returns true if successful
    if (update($query, $values, 'sssss')) {
        echo 1;  // Success
    } else {
        echo 'failed';  // Failure
    }
}




?>
