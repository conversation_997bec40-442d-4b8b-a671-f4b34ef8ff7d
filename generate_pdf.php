<?php
ob_start(); 
require('admin/inc/db_config.php');
require('admin/inc/essentials.php');

session_start();


if (isset($_GET['id'])) {
    $booking_id = intval($_GET['id']); // Sanitize input
    $action = isset($_GET['action']) ? $_GET['action'] : 'view'; // Default to view
    
    // Query to fetch booking details
    $query = "SELECT bo.*, bd.* FROM `booking_order` bo
              INNER JOIN `booking_details` bd ON bo.booking_id = bd.booking_id
              WHERE bo.booking_id = ?";
    
    $values = [$booking_id];
    $res = select($query, $values, 'i');
    
    if (mysqli_num_rows($res) == 0) {
        echo "No booking found with ID: $booking_id";
        exit;
    }
    
    $data = mysqli_fetch_assoc($res);
    
    // Format dates
    $date = date("h:i A | d-m-Y", strtotime($data['datentime']));
    $checkin = date("d-m-Y", strtotime($data['check_in']));
    $checkout = date("d-m-Y", strtotime($data['check_out']));
    
    // Determine status color
    $status_class = match($data['booking_status']) {
        'booked' => 'success',
        'cancelled' => 'danger',
        default => 'warning'
    };
    
    // Determine refund status text and class
    $refund_status = '';
    $refund_class = '';
    
    if ($data['booking_status'] == 'cancelled') {
        if ($data['refund'] == 1) {
            $refund_status = 'Refunded';
            $refund_class = 'success';
        } else {
            $refund_status = 'Not Refunded';
            $refund_class = 'warning';
        }
    }
    
    if ($action == 'download') {
        // Generate and download PDF
        
        // Check if TCPDF library is available
        if (!file_exists('vendor/tecnickcom/tcpdf/tcpdf.php')) {
            echo "<div style='text-align:center; padding:20px;'>
                    <h3>PDF Generation Error</h3>
                    <p>TCPDF library not found. Please install it first.</p>
                    <a href='generate_pdf.php?id=$booking_id'>Go back to preview</a>
                  </div>";
            exit;
        }
        
        require_once('vendor/tecnickcom/tcpdf/tcpdf.php');
        
        // Set content type for PDF download
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="booking_receipt_'.$data['order_id'].'.pdf"');
        
        // Create new PDF document
        $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
        
        // Set document information
        $pdf->SetCreator('Hotel Booking System');
        $pdf->SetAuthor('Admin');
        $pdf->SetTitle('Booking Receipt - ' . $data['order_id']);
        
        // Set margins
        $pdf->SetMargins(15, 15, 15);
        
        // Add a page
        $pdf->AddPage();
        
        // Set font
        $pdf->SetFont('dejavusans', '', 10);

        
        // Content - Same as the HTML content below but formatted for PDF
        $html = '
        <h1 style="text-align:center;">Booking Receipt</h1>
        <hr>
        <table border="1" cellpadding="5" cellspacing="0" width="100%">
            <tr>
                <td width="50%"><b>Order ID:</b> '.$data['order_id'].'</td>
                <td width="50%"><b>Booking Date:</b> '.$date.'</td>
            </tr>
            <tr>
                <td><b>Customer Name:</b> '.$data['customer_name'].'</td>
                <td><b>Email:</b> '.$data['customer_email'].'</td>
            </tr>
            <tr>
                <td><b>Phone:</b> '.$data['customer_phone'].'</td>
                <td><b>Room:</b> '.$data['room_name'].'</td>
            </tr>
            <tr>
                <td><b>Check-In:</b> '.$checkin.'</td>
                <td><b>Check-Out:</b> '.$checkout.'</td>
            </tr>
            <tr>
                <td><b>Amount:</b> ₦'.$data['trans_amt'].'</td>
                <td><b>Status:</b> '.$data['booking_status'].'</td>
            </tr>';
            
        // Add refund status row for cancelled bookings
        if ($data['booking_status'] == 'cancelled') {
            $html .= '
            <tr>
                <td colspan="2"><b>Refund Status:</b> '.$refund_status.'</td>
            </tr>';
        }
        
        $html .= '
        </table>
        <p style="text-align:center; margin-top:30px;">Thank you for booking with us!</p>
        ';
        
        // Output HTML content
        $pdf->writeHTML($html, true, false, true, false, '');
        
        // Close and output PDF document
        ob_end_clean();
        $pdf->Output('booking_receipt_'.$data['order_id'].'.pdf', 'D');
        exit;
    } else {
        // View as HTML
        ?>
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Booking Receipt - <?php echo $data['order_id']; ?></title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet">
            <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
            <style>
                body {
                    background-color: #f8f9fa;
                }
                .receipt-container {
                    max-width: 800px;
                    margin: 30px auto;
                    background: white;
                    padding: 20px;
                    box-shadow: 0 0 15px rgba(0,0,0,0.1);
                }
                .actions-bar {
                    text-align: right;
                    margin-bottom: 20px;
                }
                @media print {
                    .no-print {
                        display: none;
                    }
                    .receipt-container {
                        box-shadow: none;
                        margin: 0;
                        padding: 15px;
                    }
                }
            </style>
        </head>
        <body>
            <div class="receipt-container">
                <div class="actions-bar no-print">
                    <button onclick="window.print()" class="btn btn-sm btn-outline-secondary">
                        <i class="bi bi-printer"></i> Print
                    </button>
                    <a href="generate_pdf.php?id=<?php echo $booking_id; ?>&action=download" class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-file-earmark-arrow-down"></i> Download PDF
                    </a>
                    <a href="bookings.php" class="btn btn-sm btn-outline-dark">
                        <i class="bi bi-arrow-left"></i> Back
                    </a>
                </div>
                
                <div class="text-center mb-4">
                    <h2>Booking Receipt</h2>
                    <hr>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <tr>
                            <td><strong>Order ID:</strong> <?php echo $data['order_id']; ?></td>
                            <td><strong>Booking Time and Date:</strong> <?php echo $date; ?></td>
                        </tr>
                        <tr>
                            <td><strong>Customer Name:</strong> <?php echo $data['customer_name']; ?></td>
                            <td><strong>Email:</strong> <?php echo $data['customer_email']; ?></td>
                        </tr>
                        <tr>
                            <td><strong>Phone:</strong> <?php echo $data['customer_phone']; ?></td>
                            <td><strong>Room:</strong> <?php echo $data['room_name']; ?></td>
                        </tr>
                        <tr>
                            <td><strong>Check-In:</strong> <?php echo $checkin; ?></td>
                            <td><strong>Check-Out:</strong> <?php echo $checkout; ?></td>
                        </tr>
                        <tr>
                            <td><strong>Amount:</strong> ₦<?php echo $data['trans_amt']; ?></td>
                            <td>
                                <strong>Status:</strong> 
                                <span class="badge bg-<?php echo $status_class; ?>">
                                    <?php echo $data['booking_status']; ?>
                                </span>
                            </td>
                        </tr>
                        <?php if(isset($data['room_no']) && !empty($data['room_no'])): ?>
                        <tr>
                            <td colspan="2"><strong>Room Number:</strong> <?php echo $data['room_no']; ?></td>
                        </tr>
                        <?php endif; ?>
                        
                        <?php if($data['booking_status'] == 'cancelled'): ?>
                        <tr>
                            <td colspan="2">
                                <strong>Refund Status:</strong> 
                                <span class="badge bg-<?php echo $refund_class; ?>">
                                    <?php echo $refund_status; ?>
                                </span>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </table>
                </div>
                
                <div class="text-center mt-4">
                    <p>Thank you for booking with us!</p>
                </div>
            </div>
            
            <script>
                // Add any JavaScript functionality here if needed
            </script>
        </body>
        </html>
        <?php
    }
} else {
    header('location: index.php');
    exit;
}
?>