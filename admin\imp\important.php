<!-- //send grid recovery code -->


V96FJMUGAGAAB68E5SWD8ZMP

<!-- //send grid recovery code -->
 

<!-- //function to set active nav bar -->

<script>
function setActive()
  {
    let navbar = document.getElementById('nav-bar');
    let a_tags  = navbar.getElementsByTagName('a');

    for(i=0; i<a_tags.length; i++){
      let file = a_tags[i].href.split('/').pop();
      let file_name = file.split('.')[0];

      if(document.location.href.indexOf(file_name) >= 0){
        a_tags[i].classList.add('active');
      }
    }
  }
  setActive();
  </script>


  <!-- // function to connct the backend contact settings to the frontend display  ALSO REFER TO CONTACTS.PHP-->
  <?php 
$contact_q ="SELECT * FROM `contact_details` WHERE `sr_no`=?";
$values = [1];
$contact_r = mysqli_fetch_assoc(select($contact_q, $values, 'i'));
?>


<!-- function to connect the backend about seetings to the front end  ALSO REFER TO ABOUT.PHP-->
<?php 
    $about_r = selectAll('team_details');
    $path = ABOUT_IMG_PATH;
    
    while($row = mysqli_fetch_assoc($about_r)) {
        echo <<<data
        <div class="swiper-slide bg-white text-center overflow-hidden rounded">
            <img src="{$path}{$row['picture']}" class="w-100" alt="Team Member">
            <h5 class="mt-2">{$row['name']}</h5> 
        </div>
        data;
    }
    ?>

    <!-- function to display a carousel/image from the backend after setting th seettings.php,settings_crud.php,
     settings.js and setting the database to accept the imgages also add the folder and ppath to essentials
      i,e refer to carousel.php  ALSO REFER TO INDEX.PHP OR ANY PAGE WITH CAROUSEL-->

<div class="container-fliud px-lg-4 mt-4">
    <!-- Swiper -->
  <div class="swiper swiper-container">
    <div class="swiper-wrapper">
      <?php 
        // Make sure the selectAll() function is properly returning a result
        $res = selectAll('carousel');
        
        // Check if the query was successful and returned a valid result set
        if ($res) {
            while ($row = mysqli_fetch_assoc($res)) {
                $path = CAROUSEL_IMG_PATH;
                echo <<<data
                <div class="swiper-slide">
                    <img src="{$path}{$row['image']}" class="w-100 d-block" />
                </div>
        data;
            }
        } else {
            // Handle query failure (optional)
            echo "Error fetching data from the database.";
        }
      ?>      
    </div>    
  </div>
</div>

<!-- function to send data from the frontend to the backend using a FORM but create a 
 datbase with the name attribute e.g name="email and same shoule be the same name on the database" -->

<?php
  if(isset($_POST['send']))
  {
    $frm_data = filteration($_POST);

    $q="INSERT INTO `user_queries`(`name`, `email`, `subject`, `message`) VALUES (?,?,?,?)";
    $values = [$frm_data['name'],$frm_data['email'],$frm_data['subject'],$frm_data['message']];

    $res = insert($q,$values,'ssss');
    if($res==1){
      alert('success', 'Mail sent!');
    }
    else{
      alert('error', 'Server Down! Try Again Later');
    }
  }
?>
<?php require('inc/footer.php');?>

<!-- function to mark, mark all as read, delete and delete all" -->


<?php
require('inc/db_config.php');
require ('inc/essentials.php');
adminLogin();

if (isset($_GET['seen'])) {
    $frm_data = filteration($_GET);

    if ($frm_data['seen'] == 'all') {
        $q = "UPDATE `user_queries` SET `seen`=?";
        $values = [1];
        if (update($q, $values, 'i')) {
            alert('success', 'Marked all as read!');
        } else {
            alert('error', 'Operation Failed!');
        }
    } else {
        $q = "UPDATE `user_queries` SET `seen`=? WHERE `sr_no`=?";
        $values = [1, $frm_data['seen']];
        if (update($q, $values, 'ii')) {
            alert('success', 'Marked as read!');
        } else {
            alert('error', 'Operation Failed!');
        }
    }
}
if (isset($_GET['del'])) {
    $frm_data = filteration($_GET);

    if ($frm_data['del'] == 'all') {
            $q = "DELETE FROM `user_queries`";
            if (mysqli_query($con,$q)) {
                alert('success', 'All Feedback Deleted!');
        } else {
            alert('error', 'Operation Failed!');
        }    } else {
        $q = "DELETE FROM `user_queries` WHERE `sr_no`=?";
        $values = [$frm_data['del']];
        if (delete($q, $values, 'i')) {
            alert('success', 'Feedback Deleted!');
        } else {
            alert('error', 'Operation Failed!');
        }
    }
}
?>

<!-- function to show the details from the frond end stored on the database on the backend admin panel 
 it works ewith the function above to see and delete details can be good for feedacks and complains
 dont forget to create a database THIS WILL DISPLAY IT AS A TABLE FROM THE DATABASE" ALSO REFER TO USER_QUERIES.PHP -->

 <div class="table-responsive-md" style="height:450px; overflow-y">
<table class="table table-hover border">
    <thead class="sticky-top">
        <tr class="bg-dark text-light">
        <th scope="col">S/n</th>
        <th scope="col">Name</th>
        <th scope="col">Email</th>
        <th scope="col" width="17%">Subject</th>
        <th scope="col" width="25%">Message</th>
        <th scope="col">Date</th>
        <th scope="col">Action</th>
        </tr>
    </thead>
    <tbody>
        <?php
            $q = "SELECT * FROM `user_queries` ORDER BY  `sr_no`  DESC";
            $data = mysqli_query($con,$q);
            $i=1;

            while($row = mysqli_fetch_assoc($data))
            {
                $seen = '';
                if ($row['seen'] != 1) {
                    $seen = "<a href='?seen=$row[sr_no]' class='btn btn-sm rounded-pill btn-primary'> Mark as read </a><br>";
                }

                $seen .= "<a href='?del=$row[sr_no]' class='btn btn-sm rounded-pill btn-danger mt-2'> Delete</a>";

                echo<<<query
                    <tr>
                        <td>$i</td>
                        <td>$row[name]</td>
                        <td>$row[email]</td>
                        <td>$row[subject]</td>
                        <td>$row[message]</td>
                        <td>$row[date]</td>
                        <td>$seen</td>
                    </tr>
                query;
                $i++;
            }
        ?>
    </tbody>
    </table>
</div>