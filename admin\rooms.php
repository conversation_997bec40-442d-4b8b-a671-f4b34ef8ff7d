<?php
require('inc/db_config.php');
require ('inc/essentials.php');
adminLogin();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ADMIN-ROOMS</title>
    <?php require("inc/links.php")?>
</head>
<body class="bg-light">
    <?php require('inc/header.php'); ?>

    <div class="container-fluid" id="main-content">
        <div class="row">
            <div class="col-lg-10 ms-auto p-4 overflow-hidden">
                <h3 class="mb-4">ROOMS</h3>

                         <!-- FEATURES  -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body">
                                                           
                    <div class="text-end mb-4">
                        <button type="button" class="btn btn-dark shadow-none btn-sm" data-bs-toggle="modal" data-bs-target="#add-room">
                            <i class="bi bi-plus-square"></i> Add
                        </button>
                    </div>

                        <div class="table-responsive-lg
                        " style="height:contain; overflow-y">
                        <table class="table table-hover border text-center">
                            <thead>
                                <tr class="bg-dark text-light">
                                <th scope="col">S/n</th>
                                <th scope="col">Name</th>
                                <th scope="col">Area</th>
                                <th scope="col">Guest</th>
                                <th scope="col">Price</th>
                                <th scope="col">Quantity</th>
                                <th scope="col">Status</th>
                                <th scope="col">Action</th>
                                </tr>
                            </thead>
                            <tbody id="room-data">
                            </tbody>
                            </table>
                        </div>    
                    </div>
                </div>         
            </div>        
        </div>
    </div>

      <!-- ADD ROOM MODAL -->
    <div class="modal fade" id="add-room" data-bs-backdrop="static" data-bs-keyboard="true" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <form id="add_room_form" autocomplete="off">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Add Room</h5>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">Name</label>
                                    <input type="text"name="name" class="form-control shadow-none" required>
                            </div>                        
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">Area</label>
                                <input type="number"name="area" min="1" class="form-control shadow-none" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">Price</label>
                                <input type="text"name="price" min="1" class="form-control shadow-none" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">Quantity</label>
                                <input type="number"name="quantity" min="1" class="form-control shadow-none" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">Adult(Max.)</label>
                                <input type="number"name="adult" min="1" class="form-control shadow-none" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">Children(Max.)</label>
                                <input type="number"name="children" min="1"  class="form-control shadow-none" required>
                            </div>

                            <div class=" col-12 mb-3">
                                <label class="form-label fw-bold">Features</label>
                                <div class="row">
                                <?php 
                                    $res = selectAll('features');
                                    while($opt = mysqli_fetch_assoc($res)) {
                                        echo <<<data
                                        <div class="col-md-3 mb-1">
                                            <label>
                                                <input type='checkbox' name='features' value ='$opt[id]' class= 'form-check-input shadow-none'>
                                                $opt[name]
                                            </label>                                               
                                        </div>
                                        data;
                                    }
                                    ?>
                                </div>

                            </div>
                            <div class=" col-12 mb-3">
                                <label class="form-label fw-bold">Facilities</label>
                                <div class="row">
                                <?php 
                                    $res = selectAll('facilities');
                                    while($opt = mysqli_fetch_assoc($res)) {
                                        echo <<<data
                                        <div class="col-md-3 mb-1">
                                            <label>
                                                <input type='checkbox' name='facilities' value ='$opt[id]' class= 'form-check-input shadow-none'>
                                                $opt[name]
                                            </label>                                               
                                        </div>
                                        data;
                                    }
                                    ?>
                                </div>

                            </div>
                            <div class="col-12 mb-3">
                            <label class="form-label fw-bold">Description</label>
                            <textarea name="desc"  rows="4" class="form-control shadow-none" required></textarea> 
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="reset" class="btn text-secondary shadow-none" data-bs-dismiss="modal">
                            CANCEL
                        </button>
                        <button type="submit" class="btn custom-bg text-white shadow-none">
                            SUBMIT
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
      
    <!-- EDIT ROOM MODAL -->
    <div class="modal fade" id="edit-room" data-bs-backdrop="static" data-bs-keyboard="true" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <form id="edit_room_form" autocomplete="off">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Edit Room</h5>
                    </div>
                    <div class="modal-body">
                        <div id="modal-alert"></div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">Name</label>
                                    <input type="text"name="name" class="form-control shadow-none" required>
                            </div>                        
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">Area</label>
                                <input type="number"name="area" min="1" class="form-control shadow-none" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">Price</label>
                                <input type="text"name="price" min="1" class="form-control shadow-none" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">Quantity</label>
                                <input type="number"name="quantity" min="1" class="form-control shadow-none" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">Adult(Max.)</label>
                                <input type="number"name="adult" min="1" class="form-control shadow-none" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">Children(Max.)</label>
                                <input type="number"name="children" min="1"  class="form-control shadow-none" required>
                            </div>

                            <div class=" col-12 mb-3">
                                <label class="form-label fw-bold">Features</label>
                                <div class="row">
                                <?php 
                                    $res = selectAll('features');
                                    while($opt = mysqli_fetch_assoc($res)) {
                                        echo <<<data
                                        <div class="col-md-3 mb-1">
                                            <label>
                                                <input type='checkbox' name='features' value ='$opt[id]' class= 'form-check-input shadow-none'>
                                                $opt[name]
                                            </label>                                               
                                        </div>
                                        data;
                                    }
                                    ?>
                                </div>

                            </div>
                            <div class=" col-12 mb-3">
                                <label class="form-label fw-bold">Facilities</label>
                                <div class="row">
                                <?php 
                                    $res = selectAll('facilities');
                                    while($opt = mysqli_fetch_assoc($res)) {
                                        echo <<<data
                                        <div class="col-md-3 mb-1">
                                            <label>
                                                <input type='checkbox' name='facilities' value ='$opt[id]' class= 'form-check-input shadow-none'>
                                                $opt[name]
                                            </label>                                               
                                        </div>
                                        data;
                                    }
                                    ?>
                                </div>

                            </div>
                            <div class="col-12 mb-3">
                            <label class="form-label fw-bold">Description</label>
                            <textarea name="desc"  rows="4" class="form-control shadow-none" required></textarea> 
                            </div>
                            <input type = "hidden" name="room_id"></input>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="reset" class="btn text-secondary shadow-none" data-bs-dismiss="modal">
                            CANCEL
                        </button>
                        <button type="submit" class="btn custom-bg text-white shadow-none">
                            SUBMIT
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- MANAGE ROOM IMAGES-->
<div class="modal fade" id="room-images" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
 <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="staticBackdropLabel">Room Name</h5>
                <button type="button" class="btn-close shadow-none" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="image-alert"></div>
                <div class="border-bottom border-3 pb-3 mb-3">
                    <form id="add_image_form">
                        <label class="form-label fw-bold">Add Image</label>
                        <input type="file" name="image" accept=".jpg, .jpeg, .png, .webp" class="form-control shadow-none mb-3" required>
                        <button class="btn custom-bg text-white shadow-none"> Add</button>
                        <input type="hidden" name="room_id">
                    </form>
                </div> 
                <div class="table-responsive" style="height:350px; overflow-y">
                 <table class="table table-hover border text-center">
                    <thead>
                        <tr class="bg-dark text-light">
                        <th scope="col" width="50%">Image</th>
                        <th scope="col">Thumb</th>
                        <th scope="col">Delete</th>
                        </tr>
                    </thead>
                    <tbody id="room-image-data">
                    </tbody>
            </table>   
            </div>
        </div>
  </div>
</div>
      
    


    <?php require ('inc/script.php'); ?>
   <script src="scripts/rooms.js"></script>
</body>
</html>


</body>
</html>

