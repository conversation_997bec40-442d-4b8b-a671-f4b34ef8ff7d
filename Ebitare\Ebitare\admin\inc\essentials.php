<?php

//Frontend purpose process needs this data

define('SITE_URL', 'http://127.0.0.1/ebitare/');
define('ABOUT_IMG_PATH',SITE_URL. 'images/about/');
define('CAROUSEL_IMG_PATH',SITE_URL. 'images/carousel/');
define('FEATURES_IMG_PATH',SITE_URL. 'images/features/');
define('ROOMS_IMG_PATH',SITE_URL. 'images/rooms/');
define('USERS_IMG_PATH',SITE_URL. 'images/users/');
define('PAYMENT_PROOF_PATH', SITE_URL . 'images/payment_proofs/');

//backend upload process needs this data

    // for the path routing when deployed online
define('UPLOAD_IMAGE_PATH' , $_SERVER['DOCUMENT_ROOT'].'/h/images/');
define('ABOUT_FOLDER' , 'about/');
define('CAROUSEL_FOLDER' , 'carousel/');
define('FEATURES_FOLDER' , 'features/');
define('ROOMS_FOLDER' , 'rooms/');
define('USERS_FOLDER' , 'users/');
define('PAYMENT_PROOFS_FOLDER', 'payment_proofs/');
define('PAYSTACK_KEY', 'sk_test_c1372fb6fc186ecae5bc8ea098a784253d623703');
define('CALLBACK_URL', 'http://localhost/ebitare/payment/success.php');
define('ENDPOINT_URL', 'https://api.paystack.co/transaction/initialize');
define('VERIFICATIONEND_URL', 'https://api.paystack.co/transaction/verify/');

function adminLogin(){
    session_start();
    if(!(isset($_SESSION['adminLogin']) && $_SESSION['adminLogin']==true)){
        echo"<script>
        window.location.href='index.php';
        </script>";
        exit;
    }

}

function redirect($url){
    echo"<script>
        window.location.href='$url';
    </script>";
    exit;
}

function alert($type, $msg){

    // Correcting the condition for alert class
    $bs_class = ($type == "success") ? "alert-success" : "alert-danger";
   
    // Fixing the echo syntax for alert message
    echo <<<alert
    <div id="auto-remove-alert" class="alert $bs_class alert-dismissible fade show custom-alert" role="alert">
      <strong class="me-3">$msg</strong>
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <script>
        setTimeout(() => {
            let alertBox = document.getElementById('auto-remove-alert');
            if(alertBox) {
                alertBox.remove();
            }
        }, 1000);
    </script>
alert;
}

function uploadImage($image ,$folder)
{
    $valid_mime = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    $img_mime = $image ['type'];

    if(!in_array($img_mime, $valid_mime)){
        return 'inv_img';// invalid format or image
    }
    else if(($image['size']/(1024*1024))>6){
        return 'inv_size'; // invalid size greater than 2mb
    }
    else{
        $ext = pathinfo($image['name'], PATHINFO_EXTENSION);
        $rname = 'IMG_'.random_int(11111,99999). ".$ext";

        $img_path =UPLOAD_IMAGE_PATH.$folder.$rname;
        if(move_uploaded_file($image['tmp_name'], $img_path)){
            return $rname;
        }
        else{
            return 'upd_failed';
        }
        
    }
}

function deleteImage($image, $folder)
{
    if(unlink(UPLOAD_IMAGE_PATH.$folder.$image)){
        return true;
    }
    else{
        return false;
    }
}

function uploadSVGImage($image ,$folder)
{
    $valid_mime = ['image/svg+xml'];
    $img_mime = $image ['type'];

    if(!in_array($img_mime, $valid_mime)){
        return 'inv_img';// invalid format or image
    }
    else if(($image['size']/(1024*1024))>6){
        return 'inv_size'; // invalid size greater than 2mb
    }
    else{
        $ext = pathinfo($image['name'], PATHINFO_EXTENSION);
        $rname = 'IMG_'.random_int(11111,99999). ".$ext";

        $img_path =UPLOAD_IMAGE_PATH.$folder.$rname;
        if(move_uploaded_file($image['tmp_name'], $img_path)){
            return $rname;
        }
        else{
            return 'upd_failed';
        }
        
    }
}

function uploadUserImage($image)
{
    // Valid MIME types for images
    $valid_mime = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    $img_mime = $image['type'];

    // Check if MIME type is valid
    if (!in_array($img_mime, $valid_mime)) {
        return 'inv_img'; // Invalid image format
    }

    // Check if image size is within the 6MB limit
    if (($image['size'] / (1024 * 1024)) > 6) {
        return 'inv_size'; // Image size greater than 6MB
    }

    // Get file extension
    $ext = pathinfo($image['name'], PATHINFO_EXTENSION);

    // Generate a unique image name with a .jpeg extension
    $rname = 'IMG_' . random_int(11111, 99999) . ".jpeg";
    
    // Define the path to save the image
    $img_path = UPLOAD_IMAGE_PATH . USERS_FOLDER . $rname;

    // Create an image resource based on the file extension
    if ($ext == 'png' || $ext == 'PNG') {
        $img = imagecreatefrompng($image['tmp_name']);
    } else if ($ext == 'webp' || $ext == 'WEBP') {
        $img = imagecreatefromwebp($image['tmp_name']);
    } else {
        $img = imagecreatefromjpeg($image['tmp_name']);
    }

    // Save the image as a JPEG with 75% quality
    if (imagejpeg($img, $img_path, 75)) {
        return $rname; // Return the new file name if upload is successful
    } else {
        return 'upd_failed'; // Return error message if upload fails
    }
}
// Add this to your essentials.php file
function uploadPaymentProof($image)
{
    // Create payment_proofs directory if it doesn't exist
    $upload_dir = UPLOAD_IMAGE_PATH . 'payment_proofs/';
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }

    // Valid MIME types for images
    $valid_mime = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    $img_mime = $image['type'];

    // Check if MIME type is valid
    if (!in_array($img_mime, $valid_mime)) {
        return 'inv_img'; // Invalid image format
    }

    // Check if image size is within the 6MB limit
    if (($image['size'] / (1024 * 1024)) > 6) {
        return 'inv_size'; // Image size greater than 6MB
    }

    // Get file extension
    $ext = pathinfo($image['name'], PATHINFO_EXTENSION);

    // Generate a unique image name
    $rname = 'PAYMENT_' . random_int(11111, 99999) . ".$ext";
    
    // Define the path to save the image
    $img_path = $upload_dir . $rname;

    // Move the uploaded file to the destination
    if (move_uploaded_file($image['tmp_name'], $img_path)) {
        return $rname; // Return the new filename if upload successful
    } else {
        return 'upd_failed'; // Return error message if upload fails
    }
}
?>



