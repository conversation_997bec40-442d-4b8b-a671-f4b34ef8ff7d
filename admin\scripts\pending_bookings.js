/**
 * View payment proof image in a modal
 * @param {string} image_path - Path to the payment proof image
 */
window.view_payment_proof = function(image_path) {
    // Check if image path exists
    if (!image_path) {
        alert('Payment proof image not available!');
        return;
    }
    
    // Update the image source in the modal
    let imgElement = document.getElementById('paymentProofImg');
    if (imgElement) {
        imgElement.src = image_path;
        imgElement.onerror = function() {
            // Handle image loading error
            alert('Failed to load payment proof image. The file may not exist or may be inaccessible.');
        };
        
        // Show the modal
        let modalObj = new bootstrap.Modal(document.getElementById('paymentProofModal'));
        modalObj.show();
        
        
    } else {
        alert('Error loading payment proof viewer!');
    }
}

/**
 * Fetch and display pending bookings
 * @param {string} search - Optional search term
 */
function get_bookings(search='') {
    let xhr = new XMLHttpRequest();
    xhr.open("POST","../admin/ajax/pending_bookings_crud.php",true);
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
    
    xhr.onload = function(){
        document.getElementById('table-data').innerHTML = this.responseText;
    }
    
    xhr.send('get_bookings&search='+search);
}

/**
 * Confirm a booking
 * @param {number} id - Booking ID to confirm
 */
function confirm_booking(id) {
    if(confirm("Confirm this booking?")) {
        let data = new FormData();
        data.append('booking_id', id);
        data.append('confirm_booking', '');
        
        let xhr = new XMLHttpRequest();
        xhr.open("POST","../admin/ajax/pending_bookings_crud.php", true);
        
        xhr.onload = function() {
            if (this.responseText == 1) {
                alert('Booking confirmed successfully!');
                get_bookings();
            } else {
                alert('Server error! Please try again later.');
            }
        };
        
        xhr.send(data);
    }
}

// Load bookings when page loads
window.onload = function() {
    get_bookings();
}