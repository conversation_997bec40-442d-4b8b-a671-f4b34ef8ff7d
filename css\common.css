:root{
  --teal:  #2ec1ac ;
  --teal-hover:  #279eBc;;
}

*{
    font-family:'Poppins' , ' san-serif'
}

.h-font{
    font-family:'Merenda',cursive;
}


input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
-webkit-appearance: none;
margin: 0;
}


input[type=number] {
-moz-appearance: textfield;
}
.custom-bg{
background-color:var(--teal);
border: 1px solid var(--teal);
}

.custom-bg:hover{
background-color:var(--teal-hover);
border-color: 1px solid var(--teal-hover);
}

.h-line{
  width: 150px;
  margin:0 auto;
  height:1.7px;
}

.custom-alert{
  position: fixed;
  top:80px;
  right:25px;
  z-index: 9999 !important;
}

::-webkit-scrollbar{
  width:10px;
}
::-webkit-scrollbar-track{
  background: #f1f1f1;
}
::-webkit-scrollbar-thumb{
  background: #888;
}
::-webkit-scrollbar-thumb:hover{
  background: #555;
}

