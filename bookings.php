<?php
// Check if the HTTP_REFERER is set and matches your domain
// $referer = $_SERVER['HTTP_REFERER'] ?? '';  // Get the referer

// // Define your site's base URL
// $base_url = 'https://127.0.0.1/h';  // Replace with your actual domain

// // If the referer is empty or doesn't match your base URL, stop page loading
// if (empty($referer) || strpos($referer, $base_url) === false) {
//     // Redirect or display an error message
//     header("Location: $base_url/"); // Redirect to the homepage or another page
//     exit; // Stop further execution of the script
// }
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EBITARE - BOOKINGS</title>
    <?php require('inc/links.php'); ?>
    
    <!-- GOOGLE WEB FONT-->
    <link rel="preconnect" href="https://fonts.googleapis.com/">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Caveat:wght@400;500&amp;family=Montserrat:wght@300;400;500;600;700&amp;display=swap" rel="stylesheet">
    
    <!-- Add jQuery before Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <style>
        body {
            font-family: 'Montserrat', sans-serif;
            background-color: #f9f9f9;
        }

        .breadcrumbs {
            font-size: 14px;
            margin-bottom: 30px;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }

        .breadcrumbs a {
            text-decoration: none;
            color: #555;
            transition: color 0.3s ease;
        }

        .breadcrumbs a:hover {
            color: #333;
        }

        .page-title {
            font-size: 2.5rem;
            margin-bottom: 30px;
            color: #333;
            font-weight: 600;
        }

        .booking-card {
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            margin-bottom: 25px;
            background: #fff;
        }

        .booking-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .booking-header {
            padding: 15px 20px;
            color: #fff;
            font-weight: 500;
        }

        .booking-body {
            padding: 20px;
        }

        .booking-body h5 {
            font-size: 1.3rem;
            margin-bottom: 15px;
            color: #333;
        }

        .booking-details {
            margin-bottom: 15px;
        }

        .booking-details p {
            margin-bottom: 8px;
            color: #555;
            font-size: 0.95rem;
        }

        .badge {
            padding: 6px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            margin-bottom: 15px;
            border-radius: 4px;
        }

        .btn-custom, .btn-dark {
            padding: 8px 18px;
            font-size: 0.9rem;
            font-weight: 500;
            border-radius: 4px;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-custom:hover, .btn-dark:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 10px rgba(0,0,0,0.1);
        }

        .modal-content {
            border-radius: 8px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .modal-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #eee;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        .modal-title {
            color: #333;
            font-weight: 600;
        }

        .form-control {
            border-radius: 4px;
            padding: 10px 15px;
            border: 1px solid #ddd;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.25rem rgba(0, 123, 255, 0.1);
        }

        /* Status colors */
        .bg-success {
            background-color: #28a745 !important;
        }

        .bg-danger {
            background-color: #dc3545 !important;
        }

        .bg-warning {
            background-color: #ffc107 !important;
        }

        .bg-primary {
            background-color: #007bff !important;
        }

        /* Footer styling */
        .footer {
            background-position: center;
            background-size: cover;
            position: relative;
            padding: 60px 0 30px 0;
            color: white;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            z-index: 0;
        }

        .footer .container {
            position: relative;
            z-index: 1;
        }

        .footer h5 {
            color: #fff;
            font-weight: 600;
            margin-bottom: 20px;
            position: relative;
            padding-bottom: 10px;
        }

        .footer h5::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 50px;
            height: 2px;
            background: #fff;
        }

        .footer ul li {
            margin-bottom: 10px;
        }

        .footer a {
            color: #fff;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer a:hover {
            color: #ffd700;
        }

        /* Back to top button */
        .progress-wrap {
            position: fixed;
            right: 30px;
            bottom: 30px;
            height: 46px;
            width: 46px;
            cursor: pointer;
            display: block;
            border-radius: 50px;
            background: #fff;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(20px);
            transition: all 0.3s ease;
        }

        .progress-wrap.active-progress {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .progress-wrap::after {
            content: '↑';
            position: absolute;
            font-size: 20px;
            color: #333;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        /* Responsive container */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }

        /* Alert messages */
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }

        .alert-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        
        /* Navigation styles */
        .reveal_header {
            background-color: transparent;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            z-index: 999;
            padding: 15px 0;
            transition: all 0.3s ease;
        }

        .reveal_header.sticky {
            background-color: #fff;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .logo_normal, .logo_sticky {
            max-height: 45px;
        }

        .hamburger_2 {
            cursor: pointer;
        }

        /* Bookings container */
        .bookings-container {
            padding: 120px 0 60px;
        }

        /* Review modal */
        #reviewModal .modal-content {
            border: none;
        }
        
        /* Login prompt styling */
        #loginPrompt {
            display: block;
            cursor: pointer;
            transition: color 0.3s ease;
        }
        
        #loginPrompt:hover {
            color: #0056b3;
            text-decoration: underline;
        }
    </style>
</head>

<body>
    <?php require('inc/header.php'); ?>

    <div id="preloader">
        <div data-loader="circle-side"></div>
    </div><!-- /Page Preload -->

    <div class="layer"></div><!-- Opacity Mask -->

    <div class="bookings-container">
        <div class="container">
            <h2 class="page-title">My Bookings</h2>
            
            <div class="breadcrumbs mb-4">
                <a href="index.php">Home</a>
                <span> &gt; </span>
                <a href="#">Bookings</a>
            </div>

            <!-- Alert Messages -->
            <?php
                if(isset($_GET['cancel_status'])){
                    echo '<div class="alert alert-success">Booking cancelled successfully!</div>';
                }
                else if(isset($_GET['review_status'])){
                    echo '<div class="alert alert-success">Thank you! We appreciate your feedback and rating.</div>';
                }
            ?>

            <!-- Review Modal -->
            <div class="modal fade" id="reviewModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <form id="review-form">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title d-flex align-items-center">
                                <i class="bi bi-chat-square-heart-fill fs-3 me-2"></i>Rate & Review</h5>
                                <button type="reset" class="btn-close shadow-none" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            
                            <div class="modal-body">
                                <div class="mb-3">
                                <label class="form-label">Rating</label>
                                <select class="form-select shadow-none" name="rating">
                                    <option value="5">Exceptional</option>
                                    <option value="4">Very Good</option>
                                    <option value="3">Good</option>
                                    <option value="2">Fair</option>
                                    <option value="1">Poor</option>
                                </select>
                                </div>
                                <div class="mb-3">
                                <label class="form-label">Review</label>
                                <textarea type="text" name="review" rows="3" class="form-control shadow-none" name="pass" required></textarea>
                                </div>
                                <input type="hidden" name="booking_id">
                                <input type="hidden" name="room_id">
                                <div class="text-end">
                                <button type="submit" class="btn btn-primary btn-sm shadow-none"> 
                                  SUBMIT
                                </button>
                                </div>
                            </div>      
                        </div>
                    </form>
                </div>
            </div>

            <!-- Bookings Display Section -->
            <div class="row">
                <?php
                if(!isset($_SESSION['login']) || $_SESSION['login'] !== true){
                    // User is not logged in
                    echo '<div class="col-12"><div class="alert alert-warning">
                        <a href="#" id="loginPrompt" style="text-decoration: none; color: inherit;">Please log in to view your bookings.</a>
                    </div></div>';
                    echo '<script>
                        document.addEventListener("DOMContentLoaded", function() {
                            // Get the login modal element
                            var loginModal = new bootstrap.Modal(document.getElementById("loginModal"));
                            
                            // Set placeholder text for email field
                            document.querySelector("#loginModal input[name=\'email_mob\']").placeholder = "Email used to book room";
                            
                            // Set default password value
                            document.querySelector("#loginModal input[name=\'pass\']").value = "ebitare";
                            
                            // Add a note about default password
                            var passwordLabel = document.querySelector("#loginModal label.form-label:last-of-type");
                            if (passwordLabel) {
                                passwordLabel.innerHTML = "Email";
                            }
                            
                            // Add a note to change password
                            var loginForm = document.getElementById("login-form");
                            var passwordNote = document.createElement("div");
                            passwordNote.className = "alert alert-info small mt-2";
                            passwordNote.innerHTML = "<i class=\'bi bi-info-circle\'></i> Kindly change your password after successful login.";
                            
                            var modalBody = loginForm.querySelector(".modal-body");
                            modalBody.appendChild(passwordNote);
                            
                            // Add click event listener to the login prompt
                            document.getElementById("loginPrompt").addEventListener("click", function(e) {
                                e.preventDefault();
                                loginModal.show();
                            });
                        });
                    </script>';
                } else  {
                    $u_exist = select("SELECT * FROM `user_cred` WHERE `id`=? LIMIT 1", [$_SESSION['uId']], 'i');

                    $query = "SELECT bo.*, bd.* FROM `booking_order` bo
                    INNER JOIN `booking_details` bd ON bo.booking_id = bd.booking_id
                    WHERE (
                        (bo.booking_status = 'booked')
                        OR (bo.booking_status = 'cancelled')
                        OR (bo.booking_status = 'payment failed')
                    )
                    AND (
                        bo.user_id=?)
                    ORDER BY bo.booking_id DESC";

                    $result = select($query,[$_SESSION['uId']], 'i');

                    if(mysqli_num_rows($result) == 0) {
                        echo '<div class="col-12"><div class="alert alert-info">You have no bookings yet.</div></div>';
                    }

                    while($data = mysqli_fetch_assoc($result)) {
                        $date = date("d-m-Y", strtotime($data['datentime']));
                        $checkin = date("d-m-Y", strtotime($data['check_in']));
                        $checkout = date("d-m-Y", strtotime($data['check_out']));

                        $status_bg = "";
                        $btn = "";

                        if ($data['booking_status'] == 'booked') {
                            $status_bg = "bg-success";
                        
                            if ($data['arrival'] == 1) {
                                $btn = "
                                <div class='d-flex flex-column flex-sm-row gap-2'>
                                    <a href='generate_pdf.php?gen_pdf&id={$data['booking_id']}' class='btn btn-dark btn-sm fw-bold shadow-none w-100 w-sm-auto'>
                                        Download PDF
                                    </a>";
                                
                                if ($data['rate_review'] == 0) {
                                    $btn .= "
                                    <button type='button' onclick='review_room({$data['booking_id']}, {$data['room_id']})' data-bs-toggle='modal' data-bs-target='#reviewModal' class='btn btn-dark btn-sm fw-bold shadow-none w-100 w-sm-auto'>
                                        Rate & Review
                                    </button>
                                    ";
                                }
                                
                                $btn .= "</div>";
                                
                            } else {
                                $btn = "
                                    <button type='button' onclick='cancel_booking($data[booking_id])' class='btn btn-danger btn-sm fw-bold shadow-none'>
                                        Cancel
                                    </button>";
                            }
                        } elseif ($data['booking_status'] == 'cancelled') {
                            $status_bg = "bg-danger";
                        
                            if ($data['refund'] == 0) {
                                $btn = "<span class='badge bg-primary'>Refund in Process!</span>";
                            } else {
                                $btn = "
                                    <a href='generate_pdf.php?gen_pdf&id={$data['booking_id']}' class='btn btn-dark btn-sm fw-bold shadow-none'>
                                        Download PDF
                                    </a>";
                            }
                        } else {
                            $status_bg = "bg-warning";
                            $btn = "
                                <a href='generate_pdf.php?gen_pdf&id={$data['booking_id']}' class='btn btn-dark btn-sm fw-bold shadow-none'>
                                    Download PDF
                                </a>";
                        }
                        
                        // Display the booking info
                        echo <<<bookings
                        <div class='col-md-4 mb-4'>
                            <div class='booking-card'>
                                <div class='booking-header $status_bg'>
                                    <h5 class='fw-bold text-white m-0'>{$data['room_name']}</h5>
                                </div>
                                <div class='booking-body'>
                                    <div class='booking-details'>
                                        <p><strong>₦ {$data['total_pay']}</strong> paid</p>
                                        <p><i class="bi bi-calendar-check"></i> <strong>Check in:</strong> $checkin</p>
                                        <p><i class="bi bi-calendar-x"></i> <strong>Check out:</strong> $checkout</p>
                                        <p><i class="bi bi-receipt"></i> <strong>Order ID:</strong> $data[order_id]</p>
                                        <p><i class="bi bi-clock"></i> <strong>Date:</strong> $date</p>
                                    </div>
                                    <span class='badge $status_bg mb-3'>$data[booking_status]</span>
                                    <div>$btn</div>
                                </div>
                            </div>
                        </div>
                        bookings;
                    }
                }
                ?>
            </div>
        </div>
    </div>

    <?php require('inc/footer.php'); ?>

    <div class="progress-wrap">
        <svg class="progress-circle svg-content" width="100%" height="100%" viewBox="-1 -1 102 102">
            <path d="M50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98"/>
        </svg>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.3/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta2/dist/js/bootstrap.min.js"></script>
    <script>
    // Custom script to handle login form submission and password change
    document.addEventListener('DOMContentLoaded', function() {
        // Get login form
        const loginForm = document.getElementById('login-form');
        
        if (loginForm) {
            loginForm.addEventListener('submit', function(e) {
                // Add code here if you need to customize login form submission
                // This is just a placeholder - the actual login logic is likely handled elsewhere
                
                // You can add password change reminder notification logic here
                const passwordInput = loginForm.querySelector('input[name="pass"]');
                if (passwordInput && passwordInput.value === 'ebitare') {
                    // Maybe store a flag in sessionStorage to show a password change reminder after login
                    sessionStorage.setItem('showPasswordChangeReminder', 'true');
                }
            });
        }
    });
    
    // First define the review_form variable so it's available for the review_room function
    let review_form = document.getElementById('review-form');

    // Review function
    function review_room(bid, rid) {
        if (review_form) {
            review_form.elements['booking_id'].value = bid;
            review_form.elements['room_id'].value = rid;
        }
    }

    // Progress wrap for back-to-top functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Back-to-top functionality using vanilla JS instead of jQuery
        window.addEventListener('scroll', function() {
            var wScroll = window.scrollY;
            // Back to top
            var progressWrap = document.querySelector('.progress-wrap');
            if (wScroll > 300) {
                progressWrap.classList.add('active-progress');
            } else {
                progressWrap.classList.remove('active-progress');
            }
        });
        
        // Click event to scroll to top
        document.querySelector('.progress-wrap').addEventListener('click', function(e) {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
            return false;
        });
    });

    // Cancel booking function
    function cancel_booking(id) {
        if (confirm('Are you sure you want to cancel this booking?')) {
            let xhr = new XMLHttpRequest();
            xhr.open("POST", "ajax/cancel_booking_crud.php", true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

            xhr.onload = function () {
                if (this.responseText.trim() == "1") {
                    window.location.href = "bookings.php?cancel_status=true";
                } else {
                    alert('Cancellation Failed!');
                }
            };

            xhr.send('cancel_booking=1&id=' + encodeURIComponent(id));
        }
    }

    // Add event listener to the review form
    if (review_form) {
        review_form.addEventListener('submit', function (e) {
            e.preventDefault();

            let rating = review_form.elements['rating'].value.trim();
            let review = review_form.elements['review'].value.trim();

            if (!rating || !review) {
                alert("Please provide both a rating and a review.");
                return;
            }

            let data = new FormData();
            data.append('review_form', '');
            data.append('rating', rating);
            data.append('review', review);
            data.append('booking_id', review_form.elements['booking_id'].value);
            data.append('room_id', review_form.elements['room_id'].value);

            let xhr = new XMLHttpRequest();
            xhr.open("POST", "ajax/review_room_crud.php", true);

            xhr.onload = function () {
                if (this.responseText.trim() === "1") {
                    window.location.href = 'bookings.php?review_status=true';
                } else {
                    let myModal = document.getElementById('reviewModal');
                    let modal = bootstrap.Modal.getInstance(myModal) || new bootstrap.Modal(myModal);
                    modal.hide();

                    // Remove backdrop
                    document.querySelectorAll('.modal-backdrop').forEach(el => el.remove());
                    document.body.classList.remove('modal-open');

                    alert("Rating and Review Failed!");
                }
            };

            xhr.send(data);
        });
    }

    // Header scroll effect
    window.addEventListener('scroll', function() {
        var header = document.querySelector('.reveal_header');
        if (header) {
            if (window.scrollY > 50) {
                header.classList.add('sticky');
            } else {
                header.classList.remove('sticky');
            }
        }
    });
    </script>
</body>
</html>