<?php
require('../admin/inc/db_config.php');
require('../admin/inc/essentials.php');
require __DIR__ . '/../phpmailer/vendor/autoload.php';
date_default_timezone_set("Africa/Lagos");

if(isset($_POST['info_form'])){
    $frm_data = filteration($_POST);
    session_start();
    
    // Check if phone already exists but belongs to different user
    $u_exist = select("SELECT * FROM `user_cred` WHERE `phone`=? AND `id`!=? LIMIT 1",
    [$frm_data['phone'], $_SESSION['uId']], 'si');
    
    if(mysqli_num_rows($u_exist) != 0){
        echo 'phone_already';
        exit();
    }
    
    $query = "UPDATE `user_cred` SET `name`=?, `address`=?, `phone`=? WHERE `id`=? LIMIT 1";
    $values = [$frm_data['name'], $frm_data['address'], $frm_data['phone'], $_SESSION['uId']];
    
    if(update($query, $values, 'sssi')){
        $_SESSION['uName'] = $frm_data['name'];
        echo 1;
    } else {
        echo 0;
    }
}
if(isset($_POST['profile_form'])){
    session_start();

    $img = uploadUserImage($_FILES['profile']);
    if($img == 'inv_img'){
        echo 'inv_img';
        exit;
    }
    else if($img == 'upd_failed'){
        echo 'upd_failed';
        exit;
    }

    
    // Check if phone already exists but belongs to different user
    $u_exist = select("SELECT * FROM `user_cred` WHERE  `id`=? LIMIT 1", [ $_SESSION['uId'] ], 'i');

    $u_fetch = mysqli_fetch_assoc($u_exist);

    deleteImage($u_fetch['profile'], USERS_FOLDER);

    $query = "UPDATE `user_cred` SET `profile`=? WHERE `id`=? LIMIT 1";
    $values = [$img, $_SESSION['uId']];
    
    if(update($query, $values, 'si')){
        $_SESSION['uPic'] = $img;
        echo 1;
    } else {
        echo 0;
    }
}

if (isset($_POST['pass_form'])) {
    session_start();
    $frm_data = filteration($_POST);

    if ($frm_data['new_pass'] != $frm_data['confirm_pass']) {
        echo 'mismatch';
        exit;
    }

    $enc_pass = password_hash($frm_data['new_pass'], PASSWORD_BCRYPT);

    $query = "UPDATE `user_cred` SET `password`=? WHERE `id`=? LIMIT 1";
    $values = [$enc_pass, $_SESSION['uId']];

    if (update($query, $values, 'si')) {
        echo 1;
    } else {
        echo 0;
    }
}
