function booking_analytics(period = 1) {
    let xhr = new XMLHttpRequest();
    xhr.open("POST", "ajax/dashboard_crud.php", true);
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
    
    xhr.onload = function () {
        let data = JSON.parse(this.responseText);
        
        const formatCurrency = (amount) => {
            return '₦' + Number(amount).toLocaleString();
        }
        
        document.getElementById('total_bookings').textContent = data.total_bookings;
        document.getElementById('total_amt').textContent = formatCurrency(data.total_amt);
        
        document.getElementById('active_bookings').textContent = data.active_bookings;
        document.getElementById('active_amt').textContent = formatCurrency(data.active_amt);
        
        document.getElementById('pending_bookings').textContent = data.pending_bookings;
        document.getElementById('pending_amt').textContent = formatCurrency(data.pending_amt);
        
        document.getElementById('cancelled_bookings').textContent = data.cancelled_bookings;
        document.getElementById('cancelled_amt').textContent = formatCurrency(data.cancelled_amt);
    }
    
    xhr.send('booking_analytics&period=' + period);
}

function user_analytics(period = 1) {
    let xhr = new XMLHttpRequest();
    xhr.open("POST", "ajax/dashboard_crud.php", true);
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
    
    xhr.onload = function() {
        if (this.status == 200) {
            let data = JSON.parse(this.responseText);
            
            document.getElementById('new_registrations').textContent = data.new_registrations;
            document.getElementById('new_feedbacks').textContent = data.new_feedbacks;
            document.getElementById('new_reviews').textContent = data.new_reviews;
        }
    }
    
    xhr.send('user_analytics&period=' + period);
}

window.onload = function () {
    booking_analytics(1);
    user_analytics(1);
}