:root{
  --teal:  #2ec1ac ;
  --teal-hover:  #279eBc;;
}

*{
    font-family:'Poppins' , ' san-serif'
}

.h-font{
    font-family:'Merenda',cursive;
}


input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
-webkit-appearance: none;
margin: 0;
}


input[type=number] {
-moz-appearance: textfield;
}
.custom-bg{
background-color:var(--teal);
border: 1px solid var(--teal);
}

.custom-bg:hover{
background-color:var(--teal-hover);
border-color: 1px solid var(--teal-hover);
}

.h-line{
  width: 150px;
  margin:0 auto;
  height:1.7px;
}

.custom-alert{
  position: fixed;
  top:80px;
  right:25px;
}
#dashboard-menu {
  position: fixed;
  top: 10%;
  left:0;
  height: 100vh; /* Full height of the screen */
  z-index: 11; /* Ensure it is on top of other elements */
}


@media (max-width: 991px) {
  /* On small screens, let the content adjust */
  body {
      margin-left: 0; /* Remove offset on small screens */
  }

  #dashboard-menu {
      position: static;
      width: 100%;
      height: auto; /* Adjust to fit content */
  }
}

  #main-content{
    margin-top:60px;
  }
