<?php 
require('../admin/inc/db_config.php'); 
require('../admin/inc/essentials.php'); 

header("Pragma: no-cache");
header("Cache-Control: no-cache");
header("Expires:0");

// Paystack Webhook (paystack_webhook.php)
// Paystack secret key for webhook verification
$paystackSecretKey = PAYSTACK_KEY; // Replace with your Paystack secret key

// Retrieve the raw POST data (this is what Paystack will send)
$input = file_get_contents("php://input");

// Read the headers to get the signature from Paystack
$headers = getallheaders();
$paystackSignature = isset($headers['x-paystack-signature']) ? $headers['x-paystack-signature'] : '';

// Verify the webhook data by using Paystack's verification endpoint (using the secret key and signature)
$data = json_decode($input, true);
$reference = isset($data['data']['reference']) ? $data['data']['reference'] : '';

if (empty($reference)) {
    logToFile("Error: Missing transaction reference");
    http_response_code(400);
    exit();
}

$verificationEndpoint = "https://api.paystack.co/transaction/verify/" . $reference;

// Use cURL to verify the signature from Paystack
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $verificationEndpoint);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    "Authorization: Bearer $paystackSecretKey"
]);

$response = curl_exec($ch);
curl_close($ch);

// Decode the response
$responseData = json_decode($response, true);

// Log the raw response from Paystack for debugging purposes
logToFile("Raw Response: " . print_r($responseData, true));

if (isset($responseData['status']) && $responseData['status'] == 1) {
    $transaction = $responseData['data'];
    
    // Check the status of the payment (e.g., success)
    if ($transaction['status'] == 'success') {
        // Log the successful payment details
        logToFile("Payment Successful: " . print_r($transaction, true));
        
        // Create booking_payments table if it doesn't exist
        createBookingOrderTable($con);
        createBookingDetailsTable($con);
        
        // Insert payment data into the database
        insertPaymentData($con, $transaction);
        
        // Respond back to Paystack with success
        http_response_code(200);  // Acknowledge the receipt of the webhook
        exit();
    }
} else {
    // Log if the payment wasn't successful
    logToFile("Payment Failed: " . print_r($responseData, true));
    
    // If payment is not successful, return an error response
    http_response_code(400);  // Bad request
    exit();
}

// Function to create booking_order table if it doesn't exist
function createBookingOrderTable($con) {
    $sql = "CREATE TABLE IF NOT EXISTS `booking_order` (
        `booking_id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) DEFAULT NULL,
        `room_id` int(11) DEFAULT NULL,
        `check_in` date DEFAULT NULL,
        `check_out` date DEFAULT NULL,
        `order_id` varchar(150) NOT NULL,
        `trans_id` varchar(200) NOT NULL,
        `trans_amt` decimal(10,2) NOT NULL,
        `payment_status` varchar(100) NOT NULL,
        PRIMARY KEY (`booking_id`),
        UNIQUE KEY `order_id` (`order_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
    
    if (mysqli_query($con, $sql)) {
        logToFile("Table 'booking_order' created successfully or already exists");
    } else {
        logToFile("Error creating table: " . mysqli_error($con));
    }
}

// Function to create booking_details table if it doesn't exist
function createBookingDetailsTable($con) {
    $sql = "CREATE TABLE IF NOT EXISTS `booking_details` (
        `sr_no` int(11) NOT NULL AUTO_INCREMENT,
        `booking_id` int(11) NOT NULL,
        `room_name` varchar(100) DEFAULT NULL,
        `total_pay` decimal(10,2) NOT NULL,
        `customer_name` varchar(100) DEFAULT NULL,
        `customer_email` varchar(100) NOT NULL,        
        `customer_phone` varchar(20) DEFAULT NULL,
        `transaction_date` datetime NOT NULL,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`sr_no`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
    
    if (mysqli_query($con, $sql)) {
        logToFile("Table 'booking_details' created successfully or already exists");
    } else {
        logToFile("Error creating table: " . mysqli_error($con));
    }
}

// Function to insert payment data into the database
function insertPaymentData($con, $transaction) {
    // Extract custom fields
    $customFields = [];
    if (isset($transaction['metadata']['custom_fields'])) {
        foreach ($transaction['metadata']['custom_fields'] as $field) {
            $customFields[$field['variable_name']] = $field['value'];
        }
    }
    
    // Convert amount from kobo to naira (Paystack amount is in kobo)
    $amount = $transaction['amount'] / 100;

    $user_id = mysqli_real_escape_string($con, isset($customFields['user_id']) ? $customFields['user_id'] : '');
    $room_id = mysqli_real_escape_string($con, isset($customFields['room_id']) ? $customFields['room_id'] : '');
    $check_in = mysqli_real_escape_string($con, isset($customFields['check_in']) ? $customFields['check_in'] : '');
    $check_out = mysqli_real_escape_string($con, isset($customFields['check_out']) ? $customFields['check_out'] : '');
    $order_id = mysqli_real_escape_string($con, $transaction['reference']);
    $trans_id = mysqli_real_escape_string($con, $transaction['id']);
    $amount = mysqli_real_escape_string($con, $amount);
    $payment_status = mysqli_real_escape_string($con, $transaction['status']);
   
    // Prepare data for insertion
    $room_name = mysqli_real_escape_string($con, isset($customFields['room_name']) ? $customFields['room_name'] : '');
    $customer_name = mysqli_real_escape_string($con, isset($customFields['full_name']) ? $customFields['full_name'] : '');
    $customer_email = mysqli_real_escape_string($con, $transaction['customer']['email']);
    $customer_phone = mysqli_real_escape_string($con, isset($customFields['phone_number']) ? $customFields['phone_number'] : '');
    $transaction_date = mysqli_real_escape_string($con, $transaction['transaction_date']);
    
    // SQL query for insertion into booking_order
    $sql = "INSERT INTO booking_order (
                user_id, room_id, check_in, check_out, order_id, trans_id, trans_amt, payment_status
            ) VALUES (
                '$user_id', '$room_id', '$check_in', '$check_out', '$order_id', '$trans_id', '$amount', '$payment_status' 
            ) ON DUPLICATE KEY UPDATE 
                payment_status = '$payment_status'";
    
    if (mysqli_query($con, $sql)) {
        logToFile("Payment information inserted into booking_order successfully");
        
        // Get the booking_id from the insert or the existing record
        if (mysqli_affected_rows($con) > 0) {
            $booking_id = mysqli_insert_id($con);
        } else {
            // If duplicate, get the booking_id from existing record
            $check_sql = "SELECT booking_id FROM booking_order WHERE order_id = '$order_id'";
            $result = mysqli_query($con, $check_sql);
            if ($row = mysqli_fetch_assoc($result)) {
                $booking_id = $row['booking_id'];
            } else {
                logToFile("Error retrieving booking_id");
                return;
            }
        }
        
        // SQL query for insertion into booking_details
        $sq2 = "INSERT INTO booking_details (
                    booking_id, room_name, total_pay, customer_name, customer_email, customer_phone, transaction_date
                ) VALUES (
                    '$booking_id', '$room_name', '$amount', '$customer_name', '$customer_email', '$customer_phone', '$transaction_date'
                )";
        
        if (mysqli_query($con, $sq2)) {
            logToFile("Customer details inserted into booking_details successfully");
        } else {
            logToFile("Error inserting customer details: " . mysqli_error($con));
        }
    } else {
        logToFile("Error inserting payment information: " . mysqli_error($con));
    }
}

// Function to log data to a file
function logToFile($data) {
    $logFile = __DIR__ . '/payment_log.txt';  // Log file path (in the same directory as the script)
    
    // Check if the file exists, if not, create it
    if (!file_exists($logFile)) {
        // Create the file if it doesn't exist
        $handle = fopen($logFile, 'w');  // Open the file in write mode
        if ($handle) {
            fclose($handle);  // Close the file after creating it
        } else {
            error_log("Error: Unable to create log file.");
            return;  // Exit if the file cannot be created
        }
    }
    
    // Ensure the file is writable
    if (is_writable($logFile)) {
        // Append the log entry with a timestamp
        $date = date('Y-m-d H:i:s');
        $logEntry = "[$date] $data" . PHP_EOL;
        
        // Write to the log file
        file_put_contents($logFile, $logEntry, FILE_APPEND);
    } else {
        // Log an error if the file is not writable
        error_log("Error: Unable to write to log file.");
    }
}
?>