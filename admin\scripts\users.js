function get_users()
{
    let xhr = new XMLHttpRequest();
    xhr.open("POST","ajax/users_crud.php",true);
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');


    xhr.onload = function(){
        document.getElementById('users-data').innerHTML = this.responseText;
    }
    
        

    xhr.send('get_users');
}


function toggle_status(id, val) 
{
    let xhr = new XMLHttpRequest();
    xhr.open("POST", "ajax/users_crud.php", true);
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

    xhr.onload = function() {
        if (this.responseText==1) {
            alert('success','Status Changed!');
            get_users();
        } else {
            alert('error','Server Down!');
        }
    };

    // Fix the parameter sending with proper string concatenation
    xhr.send('toggle_status='+id+'&value='+val);
}


function remove_user(user_id)
{
    if(confirm ("Are you sure, you want to delete this user?"))
    {
        let data = new FormData();        
        data.append('user_id', user_id);
        data.append('remove_user', ''); 

        let xhr = new XMLHttpRequest();
    xhr.open("POST","ajax/users_crud.php", true);

    xhr.onload = function() {
        // Corrected alert handling by using the standard `alert()` function and ensuring message display
        if (this.responseText == 1) {
            alert('success', 'User Removed!');
            get_users();
        } else {
            alert('error', 'User Removal Failed');
        }
    };

    xhr.send(data);
    }
    
}

function search_user(username) {
    let xhr = new XMLHttpRequest();
    xhr.open("POST", "ajax/users_crud.php", true);
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

    xhr.onload = function() {
        document.getElementById('users-data').innerHTML = this.responseText;
    }

    // Sending the 'search_user' parameter correctly
    xhr.send('search_user&name='+username); // Correct parameter name here
}

window.onload = function (){
    get_users();
}

