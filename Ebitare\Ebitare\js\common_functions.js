// JS OBFUSCATED TO AVOID STEAL. ITEM PURCHASED/DOWNLOADED FROM THEMEFOREST IS NOT OBFUSCATED
function _0x4d90(){var _0x18f6f8=['html,\x20body','pause','loadvideo','.animate_hero','click','children','.carousel_item_3','webkit-playsinline','data-background','from','active-progress','.cat_nav_hover\x20ul\x20li\x20a','.wrapper_menu','siblings','.cat_nav_hover','.level-','top\x20center','attr','headroom','location','876oUqjtB','337128GYkmWn','timeline','a.show-submenu','querySelectorAll','transition','1qpDFwH','getBoundingClientRect','strokeDashoffset','strokeDasharray','removeClass','preventDefault','footer.revealed','niceSelect','.opacity-mask','style','parent','muted','.progress-wrap\x20path','hasClass','none','.carousel_testimonials','.pinned-image__container-overlay','forEach','#preloader','show_normal','.carousel_item_centered','header.reveal_header','next','842896JVhRfP','css','28fiRedK','init','owlCarousel','body','.pinned-image','#section_video\x20video','mouseleave','<i\x20class=\x27bi\x20bi-arrow-left-short\x27></i>','no_scroll','stroke-dashoffset\x2010ms\x20linear','height','parents','fadeOut','.pinned-image__container','background-color','fadeTo','mouseover','.carousel_item_centered_rooms','894185Ngkxgf','.sidebar-navigation\x20nav\x20li\x20a','is-active','.panel_menu','1336044liPwOv','load','sticky','animate','length','WebkitTransition','layer-is-visible','.pinned_over_content','17605280XPldIi','play','.nav_panel','.container-item','.carousel_item_1','.progress-wrap','addClass','querySelector','.footer_links\x20>\x20ul\x20>\x20li','a[href^=\x22#\x22].btn_scrollto','.main-menu','top','background-image','<i\x20class=\x27bi\x20bi-arrow-right-short\x27></i>','.fixed_header','hidden','width','toggleClass','8644wtrpCB','.menu_open','header','slideUp','show','header_color','animated','flipInX','active','hover','scrollTop','stop','swing','loaded','offset','scroll','2056482PCytgQ','each','data-opacity-mask','hash'];_0x4d90=function(){return _0x18f6f8;};return _0x4d90();}function _0x1407(_0x112758,_0x4f36e6){var _0x4d908b=_0x4d90();return _0x1407=function(_0x1407f0,_0x402a10){_0x1407f0=_0x1407f0-0x118;var _0x2f0ae1=_0x4d908b[_0x1407f0];return _0x2f0ae1;},_0x1407(_0x112758,_0x4f36e6);}(function(_0x495e69,_0x32c390){var _0xd79e7c=_0x1407,_0x275563=_0x495e69();while(!![]){try{var _0x242f52=-parseInt(_0xd79e7c(0x141))/0x1*(parseInt(_0xd79e7c(0x13c))/0x2)+-parseInt(_0xd79e7c(0x13b))/0x3*(parseInt(_0xd79e7c(0x18a))/0x4)+-parseInt(_0xd79e7c(0x16c))/0x5+parseInt(_0xd79e7c(0x170))/0x6+parseInt(_0xd79e7c(0x15a))/0x7*(-parseInt(_0xd79e7c(0x158))/0x8)+-parseInt(_0xd79e7c(0x123))/0x9+parseInt(_0xd79e7c(0x178))/0xa;if(_0x242f52===_0x32c390)break;else _0x275563['push'](_0x275563['shift']());}catch(_0x5647a5){_0x275563['push'](_0x275563['shift']());}}}(_0x4d90,0x56a1b),function(_0x570fc0){'use strict';var _0x390e01=_0x1407;_0x570fc0(window)['on'](_0x390e01(0x171),function(){var _0x197930=_0x390e01;_0x570fc0('[data-loader=\x22circle-side\x22]')[_0x197930(0x166)](),_0x570fc0(_0x197930(0x153))['addClass'](_0x197930(0x120)),_0x570fc0(_0x197930(0x12a))['addClass']('is-transitioned');}),_0x570fc0(_0x390e01(0x156))[_0x390e01(0x139)]({'offset':0x32,'tolerance':0x5,'classes':{'initial':_0x390e01(0x119),'pinned':'slideDown','unpinned':_0x390e01(0x18d)}}),_0x570fc0(window)['on']('scroll',function(){var _0x127da8=_0x390e01;_0x570fc0(this)[_0x127da8(0x11d)]()>0x1?_0x570fc0(_0x127da8(0x186))[_0x127da8(0x17e)](_0x127da8(0x172)):_0x570fc0(_0x127da8(0x186))['removeClass'](_0x127da8(0x172));}),_0x570fc0(window)[_0x390e01(0x122)](),scrollCue[_0x390e01(0x15b)]({'percentage':0.85}),_0x570fc0(_0x390e01(0x149))[_0x390e01(0x124)](function(){var _0x1c4b7d=_0x390e01;_0x570fc0(this)[_0x1c4b7d(0x159)](_0x1c4b7d(0x168),_0x570fc0(this)[_0x1c4b7d(0x138)](_0x1c4b7d(0x125)));}),_0x570fc0('.background-image')[_0x390e01(0x124)](function(){var _0x2b9f12=_0x390e01;_0x570fc0(this)[_0x2b9f12(0x159)](_0x2b9f12(0x184),_0x570fc0(this)[_0x2b9f12(0x138)](_0x2b9f12(0x12f)));}),_0x570fc0(_0x390e01(0x181))['on'](_0x390e01(0x12b),function(_0x5ed55f){var _0x44bdaf=_0x390e01;_0x5ed55f[_0x44bdaf(0x146)]();var _0x16e5e3=this[_0x44bdaf(0x126)],_0x5645b2=_0x570fc0(_0x16e5e3);_0x570fc0('html,\x20body')[_0x44bdaf(0x11e)]()[_0x44bdaf(0x173)]({'scrollTop':_0x5645b2['offset']()[_0x44bdaf(0x183)]-0x3c},0x12c,_0x44bdaf(0x11f),function(){var _0x520052=_0x44bdaf;window[_0x520052(0x13a)][_0x520052(0x126)]=_0x16e5e3;});});const _0x27e8a2=document[_0x390e01(0x13f)](_0x390e01(0x15e));_0x27e8a2[_0x390e01(0x152)](_0x4852c8=>{var _0x490568=_0x390e01;const _0x3d644f=_0x4852c8['querySelector'](_0x490568(0x167)),_0x479e1e=_0x3d644f[_0x490568(0x17f)]('img'),_0x20a7cd=_0x3d644f[_0x490568(0x17f)](_0x490568(0x151)),_0x523c2f=_0x4852c8[_0x490568(0x17f)](_0x490568(0x177)),_0x40418f=gsap[_0x490568(0x13d)]({'paused':!![]});_0x40418f['to'](_0x3d644f,{'scale':1.05},0x0),_0x40418f[_0x490568(0x130)](_0x523c2f,{'autoAlpha':0x0},0x0),_0x40418f[_0x490568(0x130)](_0x20a7cd,{'autoAlpha':0x0},0x0);const _0x48102f=ScrollTrigger['create']({'animation':_0x40418f,'trigger':_0x4852c8,'start':_0x490568(0x137),'markers':![],'pin':![],'scrub':![]});});var _0x1db8da=_0x570fc0(window),_0x236209=_0x570fc0(_0x390e01(0x15f)),_0x19deb3,_0x1cd697,_0x1501d1,_0xa82d51;function _0x324430(_0x7621a6){var _0x6e7dba=_0x390e01;return _0x19deb3=_0x570fc0(_0x7621a6)[_0x6e7dba(0x121)]()['top'],_0x1cd697=_0x19deb3+_0x570fc0(_0x7621a6)['outerHeight'](),_0x1501d1=_0x1db8da[_0x6e7dba(0x11d)](),_0xa82d51=_0x1501d1+_0x1db8da[_0x6e7dba(0x164)](),_0x1cd697>_0x1501d1&&_0x19deb3<_0xa82d51;}if(_0x236209[_0x390e01(0x174)]){var _0x51cfaa;_0x236209[_0x390e01(0x124)](function(){var _0x5640ce=_0x390e01;_0x570fc0(this)['attr'](_0x5640ce(0x12e),''),_0x570fc0(this)['attr']('playsinline',''),_0x570fc0(this)['attr'](_0x5640ce(0x14c),_0x5640ce(0x14c)),_0x570fc0(this)[_0x5640ce(0x138)]('id',_0x5640ce(0x129)),_0x51cfaa=document['getElementById']('loadvideo'),_0x51cfaa[_0x5640ce(0x171)]();}),_0x1db8da['scroll'](function(){var _0x4b7316=_0x390e01;_0x236209[_0x4b7316(0x124)](function(){var _0x31df55=_0x4b7316;_0x324430(this)==!![]?_0x570fc0(this)[0x0][_0x31df55(0x179)]():_0x570fc0(this)[0x0][_0x31df55(0x128)]();});});}_0x570fc0('.open_close_nav_panel')['on'](_0x390e01(0x12b),function(){var _0x1f7c9c=_0x390e01;_0x570fc0(_0x1f7c9c(0x17a))['toggleClass'](_0x1f7c9c(0x18e)),_0x570fc0('.layer')['toggleClass'](_0x1f7c9c(0x176));}),_0x570fc0(_0x390e01(0x16d))['on'](_0x390e01(0x12b),function(){var _0xf0d791=_0x390e01,_0x1b747a=_0x570fc0(this)[_0xf0d791(0x165)]('ul')[_0xf0d791(0x174)]-0x1,_0x504a17=_0x570fc0(this)['closest']('ul'),_0x3a3718=_0x570fc0(this)[_0xf0d791(0x14b)]('li'),_0x4857ee=_0x570fc0(_0xf0d791(0x136)+_0x1b747a),_0x5b82e6=_0x570fc0(this)[_0xf0d791(0x157)]('ul');if(_0x3a3718[_0xf0d791(0x14e)]('back'))_0x504a17[_0xf0d791(0x145)](_0xf0d791(0x11b)),_0x4857ee[_0xf0d791(0x145)](_0xf0d791(0x187));else _0x3a3718[_0xf0d791(0x12c)]('ul')[_0xf0d791(0x174)]>0x0&&(_0x5b82e6['toggleClass']('active'),_0x504a17[_0xf0d791(0x17e)]('hidden'));}),_0x570fc0('.open_close_menu')['on'](_0x390e01(0x12b),function(){var _0x319b8c=_0x390e01;_0x570fc0(_0x319b8c(0x182))[_0x319b8c(0x189)](_0x319b8c(0x18e)),_0x570fc0('.layer')[_0x319b8c(0x189)]('layer-is-visible');}),_0x570fc0(_0x390e01(0x18b))['on'](_0x390e01(0x12b),function(){var _0x535a34=_0x390e01;_0x570fc0('.hamburger')[_0x535a34(0x189)](_0x535a34(0x16e)),_0x570fc0(_0x535a34(0x16f))['toggleClass']('active'),_0x570fc0(_0x535a34(0x15d))[_0x535a34(0x189)](_0x535a34(0x162)),_0x570fc0(_0x535a34(0x18c))[_0x535a34(0x189)](_0x535a34(0x118));}),_0x570fc0('.wrapper_menu\x20ul\x20li\x20a')[_0x390e01(0x124)](function(){var _0x2ca361=_0x390e01;_0x570fc0(this)['on'](_0x2ca361(0x16a),function(){var _0x22f256=_0x2ca361;_0x570fc0(_0x22f256(0x133))['addClass'](_0x22f256(0x11c)),_0x570fc0(_0x22f256(0x17b))[_0x22f256(0x145)](_0x22f256(0x11b)),_0x570fc0(this)[_0x22f256(0x14b)]()[_0x22f256(0x17e)](_0x22f256(0x11b));})['on']('mouseleave',function(){var _0x2d3127=_0x2ca361;_0x570fc0('.wrapper_menu')[_0x2d3127(0x145)](_0x2d3127(0x11c));});}),_0x570fc0(_0x390e01(0x13e))['on'](_0x390e01(0x12b),function(){var _0x24faf2=_0x390e01;_0x570fc0(this)[_0x24faf2(0x189)](_0x24faf2(0x154));}),_0x570fc0(_0x390e01(0x17c))[_0x390e01(0x15c)]({'center':!![],'items':0x1,'loop':![],'addClassActive':!![],'margin':0x0,'autoplay':![],'autoplayTimeout':0xbb8,'autoplayHoverPause':!![],'animateOut':'fadeOut','responsive':{0x0:{'dots':!![]},0x3df:{'dots':!![]}}}),_0x570fc0(_0x390e01(0x155))[_0x390e01(0x15c)]({'loop':!![],'margin':0x5,'nav':!![],'dots':![],'center':!![],'navText':[_0x390e01(0x161),_0x390e01(0x185)],'responsive':{0x0:{'items':0x1},0x258:{'items':0x2},0x3e8:{'items':0x2}}}),_0x570fc0(_0x390e01(0x16b))[_0x390e01(0x15c)]({'loop':!![],'margin':0x5,'nav':!![],'dots':![],'center':!![],'navText':[_0x390e01(0x161),'<i\x20class=\x27bi\x20bi-arrow-right-short\x27></i>'],'responsive':{0x0:{'items':0x1},0x258:{'items':0x1},0x3df:{'items':0x2}}}),_0x570fc0(_0x390e01(0x12d))['owlCarousel']({'loop':![],'margin':0xf,'nav':!![],'dots':![],'center':![],'navText':[_0x390e01(0x161),_0x390e01(0x185)],'responsive':{0x0:{'items':0x1},0x258:{'items':0x2},0x3e8:{'items':0x3}}}),_0x570fc0(_0x390e01(0x150))['owlCarousel']({'items':0x1,'loop':!![],'autoplay':![],'animateIn':_0x390e01(0x11a),'margin':0x28,'stagePadding':0x1e,'smartSpeed':0x12c,'autoHeight':!![],'dots':!![],'responsiveClass':!![],'responsive':{0x258:{'items':0x1},0x3e8:{'items':0x1,'nav':![]}}}),_0x570fc0('.fixed_title')['theiaStickySidebar']({'minWidth':0x3df,'additionalMarginTop':0x78}),_0x570fc0('.custom_select\x20select')[_0x390e01(0x148)]();_0x570fc0(window)[_0x390e01(0x188)]()>=0x400&&_0x570fc0(_0x390e01(0x147))['footerReveal']({'shadow':![],'opacity':0.6,'zIndex':0x1});;_0x570fc0(_0x390e01(0x180))[_0x390e01(0x11c)](function(){var _0x46925c=_0x390e01;_0x570fc0(this)[_0x46925c(0x134)]()[_0x46925c(0x11e)]()['fadeTo'](0x12c,0.6),_0x570fc0(this)[_0x46925c(0x14b)]()['siblings']()[_0x46925c(0x11e)]()[_0x46925c(0x169)](0x12c,0.3);},function(){var _0x490b96=_0x390e01;_0x570fc0(this)['siblings']()[_0x490b96(0x11e)]()['fadeTo'](0x12c,0x1),_0x570fc0(this)[_0x490b96(0x14b)]()[_0x490b96(0x134)]()[_0x490b96(0x11e)]()['fadeTo'](0x12c,0x1);}),_0x570fc0(_0x390e01(0x132))[_0x390e01(0x124)](function(){var _0x336949=_0x390e01;_0x570fc0(this)['on']('mouseover',function(){var _0x3f44f5=_0x1407;_0x570fc0(_0x3f44f5(0x135))[_0x3f44f5(0x17e)]('hover'),_0x570fc0(_0x3f44f5(0x17b))['removeClass'](_0x3f44f5(0x11b)),_0x570fc0(this)['parent']()[_0x3f44f5(0x17e)](_0x3f44f5(0x11b));})['on'](_0x336949(0x160),function(){var _0x39b06c=_0x336949;_0x570fc0('.cat_nav_hover')[_0x39b06c(0x145)](_0x39b06c(0x11c));});});var _0x5b60c9=document[_0x390e01(0x17f)](_0x390e01(0x14d)),_0x36c1ea=_0x5b60c9['getTotalLength']();_0x5b60c9['style'][_0x390e01(0x140)]=_0x5b60c9[_0x390e01(0x14a)][_0x390e01(0x175)]=_0x390e01(0x14f),_0x5b60c9[_0x390e01(0x14a)][_0x390e01(0x144)]=_0x36c1ea+'\x20'+_0x36c1ea,_0x5b60c9[_0x390e01(0x14a)][_0x390e01(0x143)]=_0x36c1ea,_0x5b60c9[_0x390e01(0x142)](),_0x5b60c9[_0x390e01(0x14a)][_0x390e01(0x140)]=_0x5b60c9[_0x390e01(0x14a)][_0x390e01(0x175)]=_0x390e01(0x163);var _0x38ef8b=function(){var _0x10c2bf=_0x390e01,_0x5688b7=_0x570fc0(window)['scrollTop'](),_0x4d2f39=_0x570fc0(document)[_0x10c2bf(0x164)]()-_0x570fc0(window)[_0x10c2bf(0x164)](),_0x469ad1=_0x36c1ea-_0x5688b7*_0x36c1ea/_0x4d2f39;_0x5b60c9[_0x10c2bf(0x14a)][_0x10c2bf(0x143)]=_0x469ad1;};_0x38ef8b(),_0x570fc0(window)[_0x390e01(0x122)](_0x38ef8b);var _0x209ce0=0x32,_0x314162=0x226;_0x570fc0(window)['on'](_0x390e01(0x122),function(){var _0x345ed5=_0x390e01;jQuery(this)[_0x345ed5(0x11d)]()>_0x209ce0?jQuery(_0x345ed5(0x17d))[_0x345ed5(0x17e)](_0x345ed5(0x131)):jQuery(_0x345ed5(0x17d))['removeClass'](_0x345ed5(0x131));}),_0x570fc0('.progress-wrap')['on'](_0x390e01(0x12b),function(_0xe32ea7){var _0x5e4df0=_0x390e01;return _0xe32ea7[_0x5e4df0(0x146)](),jQuery(_0x5e4df0(0x127))[_0x5e4df0(0x173)]({'scrollTop':0x0},_0x314162),![];});}(jQuery));