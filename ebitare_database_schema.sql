-- =====================================================
-- EBITARE HOTEL BOOKING WEBSITE DATABASE SCHEMA
-- =====================================================
-- Database Type: MySQL
-- Character Set: utf8mb4
-- Collation: utf8mb4_general_ci
-- =====================================================

-- Create database (uncomment if needed)
-- CREATE DATABASE IF NOT EXISTS `u779745282_ebitare` CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
-- USE `u779745282_ebitare`;

-- =====================================================
-- 1. ADMIN CREDENTIALS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS `admin_cred` (
    `sr_no` int(11) NOT NULL AUTO_INCREMENT,
    `admin_name` varchar(150) NOT NULL,
    `admin_pass` varchar(150) NOT NULL,
    PRIMARY KEY (`sr_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- 2. WEBSITE SETTINGS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS `settings` (
    `sr_no` int(11) NOT NULL AUTO_INCREMENT,
    `site_title` varchar(50) NOT NULL,
    `site_about` varchar(250) NOT NULL,
    `shutdown` tinyint(1) NOT NULL DEFAULT 0,
    PRIMARY KEY (`sr_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- 3. CONTACT DETAILS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS `contact_details` (
    `sr_no` int(11) NOT NULL AUTO_INCREMENT,
    `address` varchar(120) NOT NULL,
    `gmap` varchar(100) NOT NULL,
    `pn1` bigint(20) NOT NULL,
    `pn2` bigint(20) NOT NULL,
    `email` varchar(100) NOT NULL,
    `fb` varchar(100) NOT NULL,
    `insta` varchar(100) NOT NULL,
    `tw` varchar(100) NOT NULL,
    `iframe` varchar(300) NOT NULL,
    PRIMARY KEY (`sr_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- 4. USER CREDENTIALS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS `user_cred` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `email` varchar(150) NOT NULL,
    `address` varchar(120) NOT NULL,
    `phone` varchar(100) NOT NULL,
    `profile` varchar(100) NOT NULL,
    `password` varchar(200) NOT NULL,
    `is_verified` int(11) NOT NULL DEFAULT 0,
    `token` varchar(200) DEFAULT NULL,
    `t_expire` datetime DEFAULT NULL,
    `status` int(11) NOT NULL DEFAULT 1,
    `datentime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- 5. FEATURES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS `features` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(50) NOT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- 6. FACILITIES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS `facilities` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `icon` varchar(100) NOT NULL,
    `name` varchar(50) NOT NULL,
    `description` varchar(250) NOT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- 7. ROOMS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS `rooms` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(150) NOT NULL,
    `area` int(11) NOT NULL,
    `price` int(11) NOT NULL,
    `quantity` int(11) NOT NULL,
    `adult` int(11) NOT NULL,
    `children` int(11) NOT NULL,
    `description` varchar(350) NOT NULL,
    `status` tinyint(4) NOT NULL DEFAULT 1,
    `removed` int(11) NOT NULL DEFAULT 0,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- 8. ROOM FEATURES JUNCTION TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS `room_features` (
    `sr_no` int(11) NOT NULL AUTO_INCREMENT,
    `room_id` int(11) NOT NULL,
    `features_id` int(11) NOT NULL,
    PRIMARY KEY (`sr_no`),
    KEY `room_id` (`room_id`),
    KEY `features_id` (`features_id`),
    CONSTRAINT `room_features_ibfk_1` FOREIGN KEY (`room_id`) REFERENCES `rooms` (`id`) ON DELETE CASCADE,
    CONSTRAINT `room_features_ibfk_2` FOREIGN KEY (`features_id`) REFERENCES `features` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- 9. ROOM FACILITIES JUNCTION TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS `room_facilities` (
    `sr_no` int(11) NOT NULL AUTO_INCREMENT,
    `room_id` int(11) NOT NULL,
    `facilities_id` int(11) NOT NULL,
    PRIMARY KEY (`sr_no`),
    KEY `room_id` (`room_id`),
    KEY `facilities_id` (`facilities_id`),
    CONSTRAINT `room_facilities_ibfk_1` FOREIGN KEY (`room_id`) REFERENCES `rooms` (`id`) ON DELETE CASCADE,
    CONSTRAINT `room_facilities_ibfk_2` FOREIGN KEY (`facilities_id`) REFERENCES `facilities` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- 10. ROOM IMAGES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS `room_images` (
    `sr_no` int(11) NOT NULL AUTO_INCREMENT,
    `room_id` int(11) NOT NULL,
    `image` varchar(150) NOT NULL,
    `thumb` tinyint(4) NOT NULL DEFAULT 0,
    PRIMARY KEY (`sr_no`),
    KEY `room_id` (`room_id`),
    CONSTRAINT `room_images_ibfk_1` FOREIGN KEY (`room_id`) REFERENCES `rooms` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- 11. CAROUSEL IMAGES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS `carousel` (
    `sr_no` int(11) NOT NULL AUTO_INCREMENT,
    `image` varchar(150) NOT NULL,
    PRIMARY KEY (`sr_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- 12. USER QUERIES TABLE (Contact Form)
-- =====================================================
CREATE TABLE IF NOT EXISTS `user_queries` (
    `sr_no` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(50) NOT NULL,
    `email` varchar(150) NOT NULL,
    `subject` varchar(200) NOT NULL,
    `message` varchar(500) NOT NULL,
    `date` date NOT NULL DEFAULT (CURRENT_DATE),
    `seen` tinyint(4) NOT NULL DEFAULT 0,
    PRIMARY KEY (`sr_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- 13. BOOKING ORDER TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS `booking_order` (
    `booking_id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) DEFAULT NULL,
    `room_id` int(11) DEFAULT NULL,
    `check_in` date DEFAULT NULL,
    `check_out` date DEFAULT NULL,
    `arrival` tinyint(4) NOT NULL DEFAULT 0,
    `refund` tinyint(4) NOT NULL DEFAULT 0,
    `booking_status` varchar(100) NOT NULL DEFAULT 'pending',
    `order_id` varchar(150) NOT NULL,
    `trans_id` varchar(200) NOT NULL,
    `trans_amt` decimal(10,2) NOT NULL,
    `payment_status` varchar(100) NOT NULL,
    `rate_review` tinyint(4) DEFAULT 0,
    `payment_proof` varchar(200) DEFAULT NULL,
    `datentime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `sender_name` varchar(100) DEFAULT NULL,
    `sender_email` varchar(100) DEFAULT NULL,
    `sender_phone` varchar(20) DEFAULT NULL,
    `sender_account_number` varchar(50) DEFAULT NULL,
    `bank_name` varchar(100) DEFAULT NULL,
    PRIMARY KEY (`booking_id`),
    UNIQUE KEY `order_id` (`order_id`),
    KEY `user_id` (`user_id`),
    KEY `room_id` (`room_id`),
    CONSTRAINT `booking_order_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user_cred` (`id`) ON DELETE SET NULL,
    CONSTRAINT `booking_order_ibfk_2` FOREIGN KEY (`room_id`) REFERENCES `rooms` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- 14. BOOKING DETAILS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS `booking_details` (
    `sr_no` int(11) NOT NULL AUTO_INCREMENT,
    `booking_id` int(11) NOT NULL,
    `room_name` varchar(100) DEFAULT NULL,
    `total_pay` decimal(10,2) NOT NULL,
    `customer_name` varchar(100) DEFAULT NULL,
    `customer_email` varchar(100) NOT NULL,
    `customer_phone` varchar(20) DEFAULT NULL,
    `room_no` varchar(10) DEFAULT NULL,
    `transaction_date` datetime NOT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`sr_no`),
    KEY `booking_id` (`booking_id`),
    CONSTRAINT `booking_details_ibfk_1` FOREIGN KEY (`booking_id`) REFERENCES `booking_order` (`booking_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- 15. RATING AND REVIEW TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS `rating_review` (
    `sr_no` int(11) NOT NULL AUTO_INCREMENT,
    `booking_id` int(11) NOT NULL,
    `room_id` int(11) NOT NULL,
    `user_id` int(11) NOT NULL,
    `rating` int(11) NOT NULL,
    `review` varchar(200) NOT NULL,
    `seen` tinyint(4) NOT NULL DEFAULT 0,
    `datentime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`sr_no`),
    KEY `booking_id` (`booking_id`),
    KEY `room_id` (`room_id`),
    KEY `user_id` (`user_id`),
    CONSTRAINT `rating_review_ibfk_1` FOREIGN KEY (`booking_id`) REFERENCES `booking_order` (`booking_id`) ON DELETE CASCADE,
    CONSTRAINT `rating_review_ibfk_2` FOREIGN KEY (`room_id`) REFERENCES `rooms` (`id`) ON DELETE CASCADE,
    CONSTRAINT `rating_review_ibfk_3` FOREIGN KEY (`user_id`) REFERENCES `user_cred` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- SAMPLE DATA INSERTION
-- =====================================================

-- Insert default admin credentials (Change password after setup)
INSERT INTO `admin_cred` (`admin_name`, `admin_pass`) VALUES
('admin', 'admin123')
ON DUPLICATE KEY UPDATE `admin_name` = VALUES(`admin_name`);

-- Insert default website settings
INSERT INTO `settings` (`site_title`, `site_about`, `shutdown`) VALUES
('Ebitare Hotels', 'Experience luxury and comfort at Ebitare Hotels. We provide world-class accommodation with exceptional service to make your stay memorable.', 0)
ON DUPLICATE KEY UPDATE `site_title` = VALUES(`site_title`);

-- Insert default contact details (Update with actual information)
INSERT INTO `contact_details` (`address`, `gmap`, `pn1`, `pn2`, `email`, `fb`, `insta`, `tw`, `iframe`) VALUES
('123 Hotel Street, Lagos, Nigeria', 'https://maps.google.com', 2348012345678, 2348087654321, '<EMAIL>', 'https://facebook.com/ebitare', 'https://instagram.com/ebitare', 'https://twitter.com/ebitare', '<iframe src="https://www.google.com/maps/embed" width="100%" height="320" frameborder="0"></iframe>')
ON DUPLICATE KEY UPDATE `address` = VALUES(`address`);

-- Insert sample features
INSERT INTO `features` (`name`) VALUES
('WiFi'),
('Television'),
('Air Conditioning'),
('Heater'),
('Balcony'),
('Kitchen'),
('Bathroom')
ON DUPLICATE KEY UPDATE `name` = VALUES(`name`);

-- Insert sample facilities
INSERT INTO `facilities` (`icon`, `name`, `description`) VALUES
('wifi.svg', 'WiFi', 'High-speed internet connection available throughout the hotel premises.'),
('television.svg', 'Television', 'Smart TV with cable channels and streaming services in every room.'),
('ac.svg', 'Air Conditioning', 'Climate-controlled rooms for your comfort in any weather.'),
('heater.svg', 'Room Heater', 'Individual room heating systems for cold weather comfort.'),
('spa.svg', 'Spa', 'Relaxing spa services including massage and wellness treatments.'),
('swimming.svg', 'Swimming Pool', 'Olympic-size swimming pool with poolside service.')
ON DUPLICATE KEY UPDATE `name` = VALUES(`name`);

-- =====================================================
-- INDEXES FOR PERFORMANCE OPTIMIZATION
-- =====================================================

-- Additional indexes for better query performance
CREATE INDEX idx_user_cred_email ON `user_cred` (`email`);
CREATE INDEX idx_user_cred_status ON `user_cred` (`status`);
CREATE INDEX idx_booking_order_status ON `booking_order` (`booking_status`);
CREATE INDEX idx_booking_order_dates ON `booking_order` (`check_in`, `check_out`);
CREATE INDEX idx_booking_order_user_date ON `booking_order` (`user_id`, `datentime`);
CREATE INDEX idx_rating_review_room ON `rating_review` (`room_id`, `rating`);
CREATE INDEX idx_user_queries_seen ON `user_queries` (`seen`, `date`);

-- =====================================================
-- NOTES AND IMPORTANT INFORMATION
-- =====================================================

/*
DATABASE SETUP NOTES:

1. DATABASE TYPE: MySQL 5.7+ or MariaDB 10.2+
2. CHARACTER SET: utf8mb4 (supports emojis and international characters)
3. COLLATION: utf8mb4_general_ci

SECURITY RECOMMENDATIONS:
1. Change the default admin password immediately after setup
2. Use strong passwords for database users
3. Implement proper backup strategies
4. Regular security updates

TABLE RELATIONSHIPS:
- user_cred: Main user table for customer accounts
- rooms: Hotel room inventory
- booking_order: Main booking transactions
- booking_details: Extended booking information
- rating_review: Customer feedback system
- features/facilities: Room amenities (many-to-many with rooms)

PAYMENT INTEGRATION:
- Supports Paystack payment gateway
- Bank transfer payment option available
- Payment proof upload functionality

ADMIN FEATURES:
- Complete booking management
- Room and facility management
- User query handling
- Review moderation
- Website settings control

IMPORTANT: Update the contact details, admin credentials, and payment gateway
settings according to your actual business requirements.
*/
