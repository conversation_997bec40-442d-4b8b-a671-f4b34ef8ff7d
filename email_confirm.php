<?php
require('admin/inc/db_config.php');
require('admin/inc/essentials.php');

if (isset($_GET['email_confirmation'])) {
    // Filter input data from URL (GET)
    $data = filteration($_GET);

    // Select query to verify the email and token from the database
    $query = select("SELECT * FROM `user_cred` WHERE `email`=? OR `token`=? LIMIT 1", 
        [$data['email'], $data['token']], 'ss');

    // Check if there is a matching row
    if (mysqli_num_rows($query) == 1) {
        $fetch = mysqli_fetch_assoc($query);

        // Check if the user is already verified
        if ($fetch['is_verified'] == 1) {
            // Alert if the email is already verified
            echo "<script>alert('Email already verified!')</script>";
            redirect('index.php');
        } else {
            // If not verified, update the user as verified
            $update = update("UPDATE `user_cred` SET `is_verified`=? WHERE `id`=?", [1, $fetch['id']], 'ii');
            
            // Check if the update is successful
            if ($update) {
                // Alert that the verification was successful
                echo "<script>alert('Email verification successful!')</script>";
                redirect('index.php');
            }
        }
    } else {
        // Alert if the verification link is invalid
        echo "<script>alert('Invalid Link!')</script>";
        redirect('index.php');
    }
}
?>
