<!DOCTYPE html>
<html lang="zxx">
<?php require('inc/links.php');?>
<?php require('inc/footer.php');?>



<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="Ansonika">
    <title>EBITARE - Hotel Gallery</title>
    
    <!-- Favicons-->
    <link rel="shortcut icon" href="img/favicon.ico" type="image/x-icon">
    <link rel="apple-touch-icon" type="image/x-icon" href="img/apple-touch-icon-57x57-precomposed.png">
    <link rel="apple-touch-icon" type="image/x-icon" sizes="72x72" href="img/apple-touch-icon-72x72-precomposed.png">
    <link rel="apple-touch-icon" type="image/x-icon" sizes="114x114" href="img/apple-touch-icon-114x114-precomposed.png">
    <link rel="apple-touch-icon" type="image/x-icon" sizes="144x144" href="img/apple-touch-icon-144x144-precomposed.png">

    <!-- GOOGLE WEB FONT-->
    <link rel="preconnect" href="https://fonts.googleapis.com/">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Caveat:wght@400;500&amp;family=Montserrat:wght@300;400;500;600;700&amp;display=swap" rel="stylesheet">

    <!-- BASE CSS -->
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
	<link href="css/vendors.min.css" rel="stylesheet">

    <!-- YOUR CUSTOM CSS -->
    <link href="css/custom.css" rel="stylesheet">
</head>

<body> 


    <div id="preloader">
        <div data-loader="circle-side"></div>
    </div><!-- /Page Preload -->

    <div class="layer"></div><!-- Opacity Mask -->

   <header class="reveal_header">
        <div class="container">
            <div class="row align-items-center">
                 <div class="col-6">
                    <a href="index.php" class="logo_normal"><img src="img/logo.png" width="135" height="45" alt=""></a>
                    <a href="index-2.php" class="logo_sticky"><img src="img/logo_sticky.png" width="135" height="45" alt=""></a>
                </div>
                <div class="col-6">
                    <nav>
                        <ul>
                            <li><a href="#rooms.php" class="btn_1 btn_scrollto">Book Now</a></li>
                            <li>
                               <div class="hamburger_2 open_close_nav_panel">
                                    <div class="hamburger__box">
                                        <div class="hamburger__inner"></div>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div><!-- /container -->
    </header><!-- /Header -->

    <div class="nav_panel">
        <a href="#0" class="closebt open_close_nav_panel"><i class="bi bi-x"></i></a>
        <div class="logo_panel"><img src="img/logo_sticky.png" width="135" height="45" alt=""></div>
        <div class="sidebar-navigation">
            <nav>
                <ul class="level-1">
                    <li class=""><a href="index-3.php">Home</a>
                    </li>
                    <li class=""><a href="rooms.php">Rooms & Suites</a>
                    </li>
                   
                    
                    <li><a href="about.php">About</a></li>
                    <li><a href="contacts.php">Contact Us</a></li>
                    <li class="parent"><a href="#0">Explore</a>
                        <ul class="level-2">
                            <li class="back"><a href="#0">Back</a></li>
                    
                            <li><a href="gallery.php">Ebitare Gallery</a></li>
                           
                        </ul>
                    </li>
                </ul>
                <div class="panel_footer">
                    <div class="phone_element"><a href="tel://423424234"><i class="bi bi-telephone"></i><span><em>Info and bookings</em>+<?php echo $contact_r['pn1']?></span></a></div>
                </div>
                <!-- /panel_footer -->
            </nav>
        </div>
        <!-- /sidebar-navigation -->
    </div>
    <!-- /nav_panel -->
    <main>

        <div class="hero medium-height jarallax" data-jarallax data-speed="0.2">
            <img class="jarallax-img" src="img/rooms/3.jpg" alt="">
            <div class="wrapper opacity-mask d-flex align-items-center justify-content-center text-center animate_hero" data-opacity-mask="rgba(0, 0, 0, 0.5)">
                <div class="container">
                    <small class="slide-animated one">Hospitality at its peak</small>
                    <h1 class="slide-animated two">Gallery</h1>
                </div>
            </div>
        </div>
        <!-- /Background Img Parallax -->

        <div class="container margin_120_95">
    <div class="isotope-wrapper">
        <div class="row justify-content-center">
            <?php 
                $res = selectAll('carousel');
                if ($res) {
                    while ($row = mysqli_fetch_assoc($res)) {
                        $path = CAROUSEL_IMG_PATH;
                        $image = $row['image'];
                        echo <<<HTML
                        <div class="item col-xl-4 col-lg-6 col-mb-6 mb-4">
                            <div class="item-img" data-cue="slideInUp">
                            <img src="{$path}{$image}" alt="" style="height: 200px; object-fit: cover; width: 100%;" />
                            <div class="content">
                                    <a data-fslightbox="gallery_1" data-type="image" href="{$path}{$image}">
                                        <i class="bi bi-arrows-angle-expand"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
HTML;
                    }
                } else {
                    echo "<p>Error fetching images from database.</p>";
                }
            ?>
        </div>
        <!--/row -->
    </div>
    <!--/isotope-wrapper -->

    <div class="pagination__wrapper">
        <ul class="pagination">
            <li><a href="#0" class="prev"><i class="bi bi-arrow-left-short"></i></a></li>
            <li><a href="#0" class="active">1</a></li>
            <li><a href="#0">2</a></li>
            <li><a href="#0">3</a></li>
            <li><a href="#0">4</a></li>
            <li><a href="#0" class="next"><i class="bi bi-arrow-right-short"></i></a></li>
        </ul>
    </div>
    <!-- /pagination -->
</div>

        <!--/container -->
    </main>


     <!-- /footer -->
     <footer class="revealed">
    <style>
        /* White color for secondary phone number and social links */
        .footer_bg a[href^="tel:+"]:has(i.bi-telephone-fill),
        .footer_bg a[href^="tel:+"] i.bi-telephone-fill,
        .footer_bg .social a,
        .footer_bg .social a i {
            color: white !important;
        }

        /* Optional: hover effect for social links */
        .footer_bg .social a:hover,
        .footer_bg .social a:hover i {
            color: #ffd700 !important; /* gold on hover */
        }
    </style>

    <div class="footer_bg">
        <div class="gradient_over"></div>
        <div class="background-image" data-background="url(img/rooms/3.jpg)"></div>
    </div>
    <div class="container">
        <div class="row move_content">
            <!-- Contact Section -->
            <div class="col-lg-4 col-md-12">
                <h5>Contact Us</h5>
                <ul>
                    <li>Address: <?php echo $contact_r['address']; ?><br><br></li>
                    <li><i class="bi bi-envelope-fill">   : </i> <strong><a href="mailto:<?php echo $contact_r['email']; ?>"><?php echo $contact_r['email']; ?></a></strong></li>
                    <li><i class="bi bi-telephone-fill">  : </i> <strong><a href="tel:+<?php echo ltrim($contact_r['pn1'], '+'); ?>">+<?php echo $contact_r['pn1']; ?></a></strong></li>
                    <?php if (!empty($contact_r['pn2'])): ?>
                        <li>
                            <strong>
                                <a href="tel:+<?php echo ltrim($contact_r['pn2'], '+'); ?>" class="d-inline-block mb-2 text-decoration-none">
                                    <i class="bi bi-telephone-fill">  : </i> +<?php echo $contact_r['pn2']; ?>
                                </a>
                            </strong>
                        </li>
                    <?php endif; ?>
                </ul>

                <!-- Social Media Links -->
                <div class="social">
                    <ul>
                        <?php if (!empty($contact_r['insta'])): ?>
                            <li>
                                <a href="<?php echo $contact_r['insta']; ?>" class="d-inline-block mb-2 text-decoration-none" target="_blank">
                                    <i class="bi bi-instagram me-1"></i> 
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if (!empty($contact_r['fb'])): ?>
                            <li>
                                <a href="<?php echo $contact_r['fb']; ?>" class="d-inline-block mb-2 text-decoration-none" target="_blank">
                                    <i class="bi bi-facebook me-1"></i> 
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if (!empty($contact_r['tw'])): ?>
                            <li>
                                <a href="<?php echo $contact_r['tw']; ?>" class="d-inline-block mb-2 text-decoration-none" target="_blank">
                                    <i class="bi bi-twitter-x me-1"></i> 
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>

            <!-- Explore Section -->
            <div class="col-lg-3 col-md-6 ms-lg-auto">
                <h5>Explore</h5>
                <div class="footer_links">
                    <ul>
                        <li><a href="index.php">Home</a></li>
                        <li><a href="about.php">About Us</a></li>
                        <li><a href="rooms.php">Rooms &amp; Suites</a></li>
                        <li><a href="contacts.php">Contact Us</a></li>
                    </ul>
                </div>
            </div>

            <!-- Newsletter Section -->
            <div class="col-lg-3 col-md-6">
                <div id="newsletter">
                    <h5>Newsletter</h5>
                    <div id="message-newsletter"></div>
                    <form method="post" action="" name="newsletter_form" id="newsletter_form">
                        <div class="form-group">
                            <input type="email" name="email_newsletter" id="email_newsletter" class="form-control" placeholder="Your email" required>
                            <button type="submit" id="submit-newsletter"><i class="bi bi-send"></i></button>
                        </div>
                    </form>
                    <p>Receive latest offers and promos without spam. You can cancel anytime.</p>
                </div>
            </div>
        </div>
        <!--/row-->
    </div>
</footer>
   

    <div class="progress-wrap">
        <svg class="progress-circle svg-content" width="100%" height="100%" viewBox="-1 -1 102 102">
            <path d="M50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98"/>
        </svg>
    </div>
    <!-- /back to top -->


<!-- COMMON SCRIPTS -->
<script src="js/common_scripts.js"></script>
<script src="js/common_functions.js"></script>
<script src="phpmailer/validate.js"></script>

<!-- SPECIFIC SCRIPTS -->
<script src="js/isotope.min.js"></script>
<script>
    $(window).on('load', function() {
        var $container = $('.isotope-wrapper');
        $container.isotope({ itemSelector: '.item', layoutMode: 'masonry', });
    });
</script>

</body>

<!-- Mirrored from www.ansonika.com/paradise/html-menu-5/gallery.html by HTTrack Website Copier/3.x [XR&CO'2014], Sat, 15 Mar 2025 23:40:34 GMT -->
</html>