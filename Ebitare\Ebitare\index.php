<!DOCTYPE html>
<html lang="zxx">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <title>EBITARE - Hotel Homepage</title>
    <?php require('inc/links.php');?>
    <?php require('inc/footer.php');?>
    
    
    <!-- Favicons-->
    <link rel="shortcut icon" href="img/favicon.ico" type="image/x-icon">
    <link rel="apple-touch-icon" type="image/x-icon" href="img/apple-touch-icon-57x57-precomposed.png">
    <link rel="apple-touch-icon" type="image/x-icon" sizes="72x72" href="img/apple-touch-icon-72x72-precomposed.png">
    <link rel="apple-touch-icon" type="image/x-icon" sizes="114x114" href="img/apple-touch-icon-114x114-precomposed.png">
    <link rel="apple-touch-icon" type="image/x-icon" sizes="144x144" href="img/apple-touch-icon-144x144-precomposed.png">

    <!-- GOOGLE WEB FONT-->
    <link rel="preconnect" href="https://fonts.googleapis.com/">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Caveat:wght@400;500&amp;family=Montserrat:wght@300;400;500;600;700&amp;display=swap" rel="stylesheet">

    <!-- BASE CSS -->
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
	<link href="css/vendors.min.css" rel="stylesheet">

    <!-- YOUR CUSTOM CSS -->
    <link href="css/custom.css" rel="stylesheet">
</head>

<body> 

    <div id="preloader">
        <div data-loader="circle-side"></div>
    </div><!-- /Page Preload -->

    <div class="layer"></div><!-- Opacity Mask -->

    <header class="reveal_header">
        <div class="container">
            <div class="row align-items-center">
            <div class="col-6">
                        <a href="index-2.php" class="logo_normal"><img src="img/logo.png" width="135" height="45" alt=""></a>
                        <a href="index-3.php" class="logo_sticky"><img src="img/logo_sticky.png" width="135" height="45" alt=""></a>
                </div>
                <div class="col-6">
                    <nav>
                        <ul>
                            <li><a href="rooms.php" class="btn_1 btn_scrollto">Book Now</a></li>
                            <li>
                               <div class="hamburger_2 open_close_nav_panel">
                                    <div class="hamburger__box">
                                        <div class="hamburger__inner"></div>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div><!-- /container -->
    </header><!-- /Header -->

    <div class="nav_panel">
        <a href="#0" class="closebt open_close_nav_panel"><i class="bi bi-x"></i></a>
        <div class="logo_panel"><img src="img/logo_sticky.png" width="135" height="45" alt=""></div>
        <div class="sidebar-navigation">
            <nav>
                <ul class="level-1">
                    <li class=""><a href="index-3.php">Home</a>
                    </li>
                    <li class=""><a href="rooms.php">Rooms & Suites</a>
                    </li>
                   
                    
                    <li><a href="about.php">About</a></li>
                    <li><a href="contacts.php">Contact Us</a></li>
                    <li class="parent"><a href="#0">Explore</a>
                        <ul class="level-2">
                            <li class="back"><a href="#0">Back</a></li>
                            <li><a href="bookings.php">My Bookings</a></li>
                            <li><a href="gallery.php">Ebitare Gallery</a></li>
                           
                        </ul>
                    </li>
                </ul>
                <div class="panel_footer">
                    <div class="phone_element"><a href="tel://423424234"><i class="bi bi-telephone"></i><span><em>Info and bookings</em>+<?php echo $contact_r['pn1']?></span></a></div>
                </div>
                <!-- /panel_footer -->
            </nav>
        </div>
        <!-- /sidebar-navigation -->
    </div>
    <!-- /nav_panel -->


    <main>
        <div class="hero home-search full-height jarallax" data-jarallax-video="mp4:./video/sunset.mp4,webm:./video/sunset.webm,ogv:./video/sunset.ogv" data-speed="0.2">
            <div class="wrapper opacity-mask d-flex align-items-center justify-content-center text-center animate_hero" data-opacity-mask="rgba(0, 0, 0, 0.5)">
                <div class="container">
                    <small class="slide-animated one">Hospitality at its peak</small>
                    <h3 class="slide-animated two">An Unforgettable Escape<br>Crafted Just for You</h3>
                    <div class="row justify-content-center slide-animated three">
                   
                    <div class="col-lg-12 bg-white shadow p-2 rounded my-0">
                     <h5 class="mb-2 text-start"> Check Booking Availability</h5>
                        <form action="rooms.php">
                            <div class="row align-items-end g-2">
                                
                                <div class="col-lg-3 mb-2">
                                    <input type="date" class="form-control shadow-none" name="checkin" required placeholder="Check-in">
                                </div>
                                <div class="col-lg-3 mb-2">
                                    <input type="date" class="form-control shadow-none" name="checkout" required placeholder="Check-out">
                                </div>
                                <div class="col-lg-3 mb-2">
                                    <select class="form-select shadow-none" name="adult" required>
                                        <option value="" disabled selected>Adults</option>
                                        <?php
                                        $guests_q = mysqli_query($con, "SELECT MAX(adult) AS `max_adult`, MAX(children) AS `max_children`
                                        FROM `rooms` WHERE `status`='1' AND `removed`='0'");
                                        $guest_res = mysqli_fetch_assoc($guests_q);
                                        for($i = 1; $i <= $guest_res['max_adult']; $i++){
                                            echo "<option value='$i'>$i</option>";
                                        }
                                        ?>
                                    </select>
                                </div>
                                <div class="col-lg-2 mb-2">
                                    <select class="form-select shadow-none" name="children" required>
                                        <option value="" disabled selected>Children</option>
                                        <?php
                                        for($i = 0; $i <= $guest_res['max_children']; $i++){
                                            echo "<option value='$i'>$i</option>";
                                        }
                                        ?>
                                    </select>
                                </div>
                                <input type="hidden" name="check_availability">
                                <div class="col-lg-1 mb-2">
                                <button type="submit" class="btn text-white shadow-none w-100" style="background-color: #343a40; border-radius: 4px; font-weight: 500;"> Submit</button>

                                </div>
                            </div>
                        </form>
                    </div>
                    </div>
                    </div>
                </div>
                <div class="mouse_wp slide-animated four">
                    <a href="#first_section" class="btn_scrollto">
                        <div class="mouse"></div>
                    </a>
                </div>
                <!-- /mouse_wp -->
            </div>
        </div>
        <!-- /jarallax video background -->

        <div class="pattern_2">
            <div class="container margin_120_95" id="first_section">
                <div class="row justify-content-between flex-lg-row-reverse align-items-center">
                    <div class="col-lg-5">
                        <div class="parallax_wrapper">
                            <img src="img/home_2.jpeg" alt="" class="img-fluid rounded-img">
                            <div data-cue="slideInUp" class="img_over"><span data-jarallax-element="-30"><img src="img/home_1.jpeg" alt="" class="rounded-img" ></span></div>
                        </div>
                    </div>
                    <div class="col-lg-5">
                        <div class="intro">
                            <div class="title">
                                <small>About us</small>
                                <h2>Tailored services and the experience of unique holidays</h2>
                            </div>
                            <p class="lead"><?php echo $settings_r['site_about']?></p>
                            <p><em>Seiyefa Mathew  ... the Manager</em></p>
                        </div>
                    </div>
                </div>
                <!-- /Row -->
            </div>
            <div class="pinned-image pinned-image--medium">
                <div class="pinned-image__container" id="section_video">
                    <video loop="loop" muted="muted" id="video_home">
                        <source src="video/swimming_pool_2.mp4" type="video/mp4">
                        <source src="video/swimming_pool_2.webm" type="video/webm">
                        <source src="video/swimming_pool_2.ogv" type="video/ogg">
                    </video>
                    <div class="pinned-image__container-overlay"></div>
                </div>
                <div class="pinned_over_content">
                    <div class="title white">
                        <small data-cue="slideInUp" data-delay="200">Hospitality at its peak</small>
                        <h2 data-cue="slideInUp" data-delay="300">Immerse Yourself<br>in Pure Relaxation</h2>
                    </div>
                </div>
            </div>
            <!-- /pinned content -->
        </div>
        <!-- /Pattern  -->   

        <div class="container margin_120_95">
            <div class="title mb-3">
                <small data-cue="slideInUp">Luxury experience</small>
                <h2 data-cue="slideInUp" data-delay="200">Rooms & Suites</h2>
            </div>
            <div class="row justify-content-center add_bottom_90" data-cues="slideInUp" data-delay="300">
            <?php
                $room_res = select("SELECT * FROM rooms WHERE status=? AND removed=? ORDER BY id DESC LIMIT 3", [1, 0], 'ii');

                while($room_data = mysqli_fetch_assoc($room_res)) {
                    
                    // Thumbnail image
                    $room_thumb = ROOMS_IMG_PATH . "thumbnail.jpg";
                    $thumb_q = mysqli_query($con, "SELECT * FROM room_images
                        WHERE room_id = {$room_data['id']} AND thumb = '1'");
                    if(mysqli_num_rows($thumb_q) > 0){
                        $thumb_res = mysqli_fetch_assoc($thumb_q);
                        $room_thumb = ROOMS_IMG_PATH . $thumb_res['image'];
                    }

                    // Booking button
                    $book_btn = "";
                    if(!$settings_r['shutdown']){
                        
                        $book_btn = "<button onclick='checkLoginToBook({$room_data['id']})' class='btn btn-sm text-white custom-bg shadow-none'>Book Now</button>";
                    }

                    // Rating logic
                    $rating_q = "SELECT AVG(rating) AS avg_rating FROM rating_review 
                        WHERE room_id = {$room_data['id']} ORDER BY sr_no DESC LIMIT 20";

                    $rating_res = mysqli_query($con, $rating_q);
                    $rating_fetch = mysqli_fetch_assoc($rating_res);

                    $rating_data = "";
                    if ($rating_fetch['avg_rating'] != NULL) {
                        $avg = round($rating_fetch['avg_rating']);
                        $rating_data .= "<div class='rating mb-3'>";
                        for ($i = 0; $i < $avg; $i++) {
                            $rating_data .= "<i class='bi bi-star-fill text-warning'></i> ";
                        }
                        $rating_data .= "</div>";
                    }

                    // Display room card
                    echo <<<html
                    <div class="col-xl-4 col-lg-6 col-md-6 col-sm-12">
                        <a href="room_detail.php?id={$room_data['id']}" class="box_cat_rooms">
                            <figure>
                                <div class="background-image" style="background-image: url('{$room_thumb}');"></div>
                                <div class="info">
                                    <small>₦{$room_data['price']} / night</small>
                                    <h3>{$room_data['name']}</h3>
                                    {$rating_data}
                                    <div class="d-flex justify-content-between align-items-center">
                                        {$book_btn}
                                        <span class="btn btn-sm btn-outline-dark shadow-none">More Details</span>
                                    </div>
                                </div>
                            </figure>
                        </a>
                    </div>
                html;
                }
                ?>
<p class="text-end"><a href="rooms.php" class="btn_1 outline mt-2">View all Rooms</a></p>


            <!-- /row-->

            <div class="title text-center mb-5">
                <small data-cue="slideInUp">Ebitare Hotel</small>
                <h2 data-cue="slideInUp" data-delay="100">Main Facilities</h2>
            </div>
            <?php
// Fetch facilities from the database
$res = mysqli_query($con, "SELECT * FROM `facilities` ORDER BY `id` DESC LIMIT 4"); // Fetching 4 facilities for a balanced display
$path = FEATURES_IMG_PATH;

echo <<<HTML
<style>
/* Container for the facility box */
.box_facilities {
    background-color: #fff;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* Hover effect for the facility box */
.box_facilities:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

/* Style for the facility icon */
.facility-icon {
    max-width: 40px;
    transition: transform 0.3s ease;
    filter: grayscale(100%) brightness(50%); /* Make icon color gray */
}

.facility-icon:hover {
    transform: scale(1.1);
    filter: none; /* Restore original color on hover */
}

/* Styling for the facility name */
.facility-name {
    font-size: 1.25rem;
    font-weight: 600;
    color: #343a40; /* Heading color */
    margin-left: 15px;
    transition: color 0.3s ease;
}

/* On hover, change the name color */
.box_facilities:hover .facility-name {
    color: #007bff; /* Hover color */
}

/* Styling for the facility description */
.facility-description {
    font-size: 1rem;
    color: #343a40; /* Description color */
    margin-top: 15px;
    line-height: 1.5;
}

/* Adding animation (optional) */
[data-cue="slideInUp"] {
    opacity: 0;
    transform: translateY(20px);
    animation: slideInUp 0.6s forwards;
}

@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mobile responsiveness */
@media (max-width: 576px) {
    .box_facilities {
        padding: 20px;
    }
    .facility-name {
        font-size: 1.1rem;
    }
    .facility-description {
        font-size: 0.9rem;
    }
}
</style>

<div class="row mt-4">
HTML;

while($row = mysqli_fetch_assoc($res)) {
    // Display each facility dynamically
    echo <<<data
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="box_facilities shadow-sm p-4 rounded border border-0 hover-effect" data-cue="slideInUp">
            <div class="d-flex align-items-center mb-3">
                <img src="{$path}{$row['icon']}" width="40px" alt="{$row['name']} Icon" class="facility-icon">
                <h5 class="m-0 ms-3 facility-name">{$row['name']}</h5>
            </div>
            <p class="facility-description">{$row['description']}</p>
        </div>
    </div>
data;
}

echo "</div>";
?>



            <!-- /Row -->
        </div>
        <!-- /container-->

        <div class="marquee">
            <div class="track">
                <div class="content">&nbsp;Relax Enjoy Luxury Holiday Travel Discover Experience Relax Enjoy Luxury Holiday Travel Discover Experience Relax Enjoy Luxury Holiday Travel Discover Experience Relax Enjoy Luxury Holiday Travel Discover Experience</div>
            </div>
        </div>
        <!-- /marquee-->

        <div class="bg_white">
            <div class="container margin_120_95">
                <div class="row justify-content-between d-flex align-items-center add_bottom_90">
                    <div class="col-lg-6">
                        <div class="pinned-image rounded_container pinned-image--small mb-4">
                            <div class="pinned-image__container">
                                <img src="img/local_amenities_1.webp" alt="">
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-5">
                        <div class="title">
                            <small>Our Amenities</small>
                            <h3>Restaurant</h3>
                            <p>At our hotel restaurant, we pride ourselves on offering guests an unforgettable dining experience that blends international flavors with the authentic taste of Bayelsa. Whether you're here for a casual meal or a fine dining experience, our restaurant serves up a range of dishes that cater to every palate.</p>
                            <p><a href="restaurant.php" class="btn_1 mt-1 outline">Read more</a></p>
                        </div>
                    </div>
                </div>
                <!-- /row-->
                <div class="row justify-content-between d-flex align-items-center">
                    <div class="col-lg-6 order-lg-2">
                        <div class="pinned-image rounded_container pinned-image--small mb-4">
                            <div class="pinned-image__container">
                                <img src="img/local_amenities_3.jpg" alt="">
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-5 order-lg-1">
                        <div class="title">
                            <small>Our Amenities</small>
                            <h3>Event Halls</h3>
                            <p>Our hotel features a range of modern, fully-equipped event halls designed to accommodate any type of event, from intimate gatherings to large-scale conferences and weddings. Whether you’re planning a corporate meeting, a social celebration, or a lavish wedding, our event halls offer the perfect setting and top-notch facilities to ensure your event runs smooth</p>
                            <p><a href="gallery.php" class="btn_1 mt-1 outline">Read more</a></p>
                        </div>
                    </div>
                </div>
                <!-- /row-->
            </div>
            <!-- /container-->
        </div>
        <!-- /bg_white -->

        <div class="parallax_section_1 jarallax" data-jarallax data-speed="0.2">
    <img class="jarallax-img kenburns-2" src="img/hero_home_1.jpg" alt="">
    <div class="wrapper opacity-mask d-flex align-items-center justify-content-center text-center" data-opacity-mask="rgba(0, 0, 0, 0.5)">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="title white">
                        <small class="mb-1">Testimonials</small>
                        <h2>What Clients Say</h2>
                    </div>

                    <div class="carousel_testimonials owl-carousel owl-theme nav-dots-orizontal">
                        <?php 
                        $review_q = "SELECT rr.*, uc.name AS uname, uc.profile, r.name AS rname 
                                     FROM `rating_review` rr
                                     INNER JOIN `user_cred` uc ON rr.user_id = uc.id
                                     INNER JOIN `rooms` r ON rr.room_id = r.id
                                     ORDER BY `sr_no` DESC LIMIT 6";

                        $review_res = mysqli_query($con, $review_q);
                        $img_path = USERS_IMG_PATH;

                        if (mysqli_num_rows($review_res) == 0) {
                            echo '<div>No reviews yet!</div>';
                        } else {
                            while ($row = mysqli_fetch_assoc($review_res)) {
                                // Create star rating
                                $stars = "";
                                for ($i = 0; $i < $row['rating']; $i++) {
                                    $stars .= "<i class='bi bi-star-fill text-warning'></i>";
                                }

                                // Display each review in the carousel
                                echo <<<HTML
                                <div>
                                    <div class="box_overlay">
                                        <div class="pic">
                                            <figure><img src="$img_path$row[profile]" alt="" class="img-circle"></figure>
                                            <h4>$row[uname]<small>$row[rname]</small></h4>
                                        </div>
                                        <div class="comment">
                                            "$row[review]"
                                        </div>
                                        <div class="rating">
                                            $stars
                                        </div>
                                    </div>
                                </div>
                                HTML;
                            }
                        }
                        ?>
                    </div>
                    <!-- End carousel_testimonials -->
                </div>
            </div>
        </div>
    </div>
</div>
<!-- /parallax_section_1-->



       

    </main>

    
  
    <!-- /footer -->
    <footer class="revealed">
    <style>
        /* White color for secondary phone number and social links */
        .footer_bg a[href^="tel:+"]:has(i.bi-telephone-fill),
        .footer_bg a[href^="tel:+"] i.bi-telephone-fill,
        .footer_bg .social a,
        .footer_bg .social a i {
            color: white !important;
        }

        /* Optional: hover effect for social links */
        .footer_bg .social a:hover,
        .footer_bg .social a:hover i {
            color: #ffd700 !important; /* gold on hover */
        }
    </style>

    <div class="footer_bg">
        <div class="gradient_over"></div>
        <div class="background-image" data-background="url(img/rooms/3.jpg)"></div>
    </div>
    <div class="container">
        <div class="row move_content">
            <!-- Contact Section -->
            <div class="col-lg-4 col-md-12">
                <h5>Contact Us</h5>
                <ul>
                    <li>Address: <?php echo $contact_r['address']; ?><br><br></li>
                    <li><i class="bi bi-envelope-fill">   : </i> <strong><a href="mailto:<?php echo $contact_r['email']; ?>"><?php echo $contact_r['email']; ?></a></strong></li>
                    <li><i class="bi bi-telephone-fill">  : </i> <strong><a href="tel:+<?php echo ltrim($contact_r['pn1'], '+'); ?>">+<?php echo $contact_r['pn1']; ?></a></strong></li>
                    <?php if (!empty($contact_r['pn2'])): ?>
                        <li>
                            <strong>
                                <a href="tel:+<?php echo ltrim($contact_r['pn2'], '+'); ?>" class="d-inline-block mb-2 text-decoration-none">
                                    <i class="bi bi-telephone-fill">  : </i> +<?php echo $contact_r['pn2']; ?>
                                </a>
                            </strong>
                        </li>
                    <?php endif; ?>
                </ul>

                <!-- Social Media Links -->
                <div class="social">
                    <ul>
                        <?php if (!empty($contact_r['insta'])): ?>
                            <li>
                                <a href="<?php echo $contact_r['insta']; ?>" class="d-inline-block mb-2 text-decoration-none" target="_blank">
                                    <i class="bi bi-instagram me-1"></i> 
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if (!empty($contact_r['fb'])): ?>
                            <li>
                                <a href="<?php echo $contact_r['fb']; ?>" target="_blank"class="d-inline-block mb-2 text-decoration-none" target="_blank">
                                    <i class="bi bi-facebook me-1"></i> 
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if (!empty($contact_r['tw'])): ?>
                            <li>
                                <a href="<?php echo $contact_r['tw']; ?>" class="d-inline-block mb-2 text-decoration-none" target="_blank">
                                    <i class="bi bi-twitter-x me-1"></i> 
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>

            <!-- Explore Section -->
            <div class="col-lg-3 col-md-6 ms-lg-auto">
                <h5>Explore</h5>
                <div class="footer_links">
                    <ul>
                        <li><a href="index-3.php">Home</a></li>
                        <li><a href="about.php">About Us</a></li>
                        <li><a href="rooms.php">Rooms &amp; Suites</a></li>
                        <li><a href="contacts.php">Contact Us</a></li>
                    </ul>
                </div>
            </div>

            <!-- Newsletter Section -->
            <div class="col-lg-3 col-md-6">
                <div id="newsletter">
                    <h5>Newsletter</h5>
                    <div id="message-newsletter"></div>
                    <form method="post" action="" name="newsletter_form" id="newsletter_form">
                        <div class="form-group">
                            <input type="email" name="email_newsletter" id="email_newsletter" class="form-control" placeholder="Your email" required>
                            <button type="submit" id="submit-newsletter"><i class="bi bi-send"></i></button>
                        </div>
                    </form>
                    <p>Receive latest offers and promos without spam. You can cancel anytime.</p>
                </div>
            </div>
        </div>
        <!--/row-->
    </div>
</footer>



    <div class="progress-wrap">
        <svg class="progress-circle svg-content" width="100%" height="100%" viewBox="-1 -1 102 102">
            <path d="M50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98"/>
        </svg>
    </div>
    <!-- /back to top -->

<!-- COMMON SCRIPTS -->
<script src="js/common_scripts.js"></script>
<script src="js/common_functions.js"></script>
<!-- <script src="js/datepicker_search.js"></script> -->
<script src="js/datepicker_inline.js"></script>
<script src="phpmailer/validate.js"></script>

</body>
</html>