{"packages": [{"name": "tecnickcom/tcpdf", "version": "6.9.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/tecnickcom/TCPDF.git", "reference": "ed27e28a4c478f7f4015b5e7e7b1912af9e85f2b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tecnickcom/TCPDF/zipball/ed27e28a4c478f7f4015b5e7e7b1912af9e85f2b", "reference": "ed27e28a4c478f7f4015b5e7e7b1912af9e85f2b", "shasum": ""}, "require": {"ext-curl": "*", "php": ">=7.1.0"}, "time": "2025-04-03T06:38:07+00:00", "type": "library", "installation-source": "dist", "autoload": {"classmap": ["config", "include", "tcpdf.php", "tcpdf_barcodes_1d.php", "tcpdf_barcodes_2d.php", "include/tcpdf_colors.php", "include/tcpdf_filters.php", "include/tcpdf_font_data.php", "include/tcpdf_fonts.php", "include/tcpdf_images.php", "include/tcpdf_static.php", "include/barcodes/datamatrix.php", "include/barcodes/pdf417.php", "include/barcodes/qrcode.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "TCPDF is a PHP class for generating PDF documents and barcodes.", "homepage": "http://www.tcpdf.org/", "keywords": ["PDFD32000-2008", "TCPDF", "barcodes", "datamatrix", "pdf", "pdf417", "qrcode"], "support": {"issues": "https://github.com/tecnickcom/TCPDF/issues", "source": "https://github.com/tecnickcom/TCPDF/tree/6.9.1"}, "funding": [{"url": "https://www.paypal.com/cgi-bin/webscr?cmd=_donations&currency_code=GBP&business=<EMAIL>&item_name=donation%20for%20tcpdf%20project", "type": "custom"}], "install-path": "../tecnickcom/tcpdf"}], "dev": true, "dev-package-names": []}