/*
Theme Name: Paradise 4.3
Theme URI: https://www.ansonika.com/paradise/
Author: Ansonika
Author URI: https://themeforest.net/user/Ansonika/

[Table of contents] 

1. SITE STRUCTURE and TYPOGRAPHY
- 1.0 Preloader
- 1.1 Typography
- 1.2 Animations title
- 1.3 Buttons
- 1.4 Structure

2. CONTENT
- 2.1 Home
- 2.2 About
- 2.3 Room list
- 2.4 Room details
- 2.5 News page
- 2.6 Contact page
- 2.7 Error page
- 2.8 Gallery
- 2.9 Restaurant
- 2.10 Advertise modal
- 2.11 Cookie bar
- 2.12 Home Categories Hover effect
- 2.13 Home Booking form on top

3. COMMON
- 3.1 Misc
- 3.2 Datepicker
- 3.3 Forms
- 3.4 Success submit 
- 3.5 Progess Scroll to top
- 3.6 Spacing
- 3.7 Pagination
- 3.8 Success submit

/*============================================================================================*/
/* 1.  SITE STRUCTURE and TYPOGRAPHY */
/*============================================================================================*/
/*-------- 1.0 Preloader --------*/
@import url("https://fonts.googleapis.com/css2?family=Lora:ital,wght@1,400;1,500&amp;display=swap");
@keyframes background {
  0% {
    top: 0;
  }
  100% {
    top: -100%;
  }
}
#preloader {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  bottom: 0;
  background-color: #292b33;
  z-index: 99999999999 !important;
  overflow: hidden;
}
#preloader.loaded {
  animation: background 0.65s normal forwards cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition-delay: 1s;
}

[data-loader=circle-side] {
  position: absolute;
  width: 50px;
  height: 50px;
  top: 50%;
  left: 50%;
  margin-left: -25px;
  margin-top: -25px;
  animation: circle infinite 0.95s linear;
  border: 2px solid #111;
  border-top-color: rgba(255, 255, 255, 0.2);
  border-right-color: rgba(255, 255, 255, 0.2);
  border-bottom-color: rgba(255, 255, 255, 0.2);
  border-radius: 100%;
}
@keyframes circle {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
/*-------- 1.1 Typography --------*/
/* rem reference
10px = 0.625rem
12px = 0.75rem
14px = 0.875rem
16px = 1rem (base)
18px = 1.125rem
20px = 1.25rem
24px = 1.5rem
30px = 1.875rem
32px = 2rem
*/
html * {
  -webkit-font-smoothing: antialiased;
}

body {
  background: #faf8f5;
  font-size: 15px;
  font-size: 0.9375rem;
  line-height: 1.6;
  font-family: "Montserrat", Helvetica, sans-serif;
  color: #333;
}

main {
  position: relative;
  z-index: 2;
  background-color: #faf8f5;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: #333;
  font-weight: 700;
  letter-spacing: -0.03em;
  line-height: 1.3;
}

h3,
h4,
h5,
h6 {
  font-weight: 600;
}

p {
  margin-bottom: 25px;
}

strong {
  font-weight: 600;
}

p.lead strong {
  font-weight: 500;
}

hr {
  margin: 30px 0 30px 0;
  border-color: #d9e1e6;
  opacity: 1;
}
hr.more_margin {
  margin: 60px 0 60px 0;
}

/* General links color */
a {
  color: #978667;
  text-decoration: none;
  transition: all 0.3s ease-in-out;
  outline: none;
}
a:hover, a:focus {
  color: #24262d;
  text-decoration: none;
  outline: none;
}

.title small {
  text-transform: uppercase;
  color: #978667;
  letter-spacing: 3px;
  font-weight: 600;
  display: block;
  margin-bottom: 5px;
  font-size: 12px;
  font-size: 0.75rem;
}
.title h2,
.title h3 {
  font-weight: 700;
  font-size: 38px;
  font-size: 2.375rem;
  margin-bottom: 15px;
}
.title h3 {
  font-size: 31px;
  font-size: 1.9375rem;
}
.title.center {
  text-align: center;
}
.title.white small {
  color: #ebd7b2;
  opacity: 0.8;
}
.title.white h2,
.title.white h3 {
  color: #fff;
}

/*-------- 1.2 Animations title --------*/
.slide-animated {
  transform: translateY(20px);
  opacity: 0;
  visibility: hidden;
  transition: all 0.05s ease;
}

.is-transitioned .slide-animated {
  transform: none;
  opacity: 1;
  visibility: visible;
  transition: all 0.5s ease;
}
.is-transitioned .slide-animated.one {
  transition-delay: 0.5s;
  -webkit-transition-delay: 0.5s;
  -moz-transition-delay: 0.5s;
  -o-transition-delay: 0.5s;
}
.is-transitioned .slide-animated.two {
  transition-delay: 0.7s;
  -webkit-transition-delay: 0.7s;
  -moz-transition-delay: 0.7s;
  -o-transition-delay: 0.7s;
}
.is-transitioned .slide-animated.three {
  transition-delay: 0.9s;
  -webkit-transition-delay: 0.9s;
  -moz-transition-delay: 0.9s;
  -o-transition-delay: 0.9s;
}
.is-transitioned .slide-animated.four {
  transition-delay: 2.5s;
  -webkit-transition-delay: 2.5s;
  -moz-transition-delay: 2.5s;
  -o-transition-delay: 2.5s;
}

/*-------- 1.3 Buttons --------*/
a.btn_1,
.btn_1 {
  border: none;
  color: #fff;
  background: #978667;
  outline: none;
  cursor: pointer;
  display: inline-flex;
  text-decoration: none;
  padding: 14px 25px 14px 25px;
  font-weight: 600;
  transition: all 0.3s ease-in-out;
  border-radius: 25px;
  align-items: center;
  justify-content: center;
  line-height: 1;
}
a.btn_1:hover,
.btn_1:hover {
  background-color: #4b514d;
}
a.btn_1.inverse,
.btn_1.inverse {
  background-color: #fff;
  color: #333;
}
a.btn_1.full-width,
.btn_1.full-width {
  width: 100%;
  text-align: center;
}
a.btn_1.outline,
.btn_1.outline {
  background-color: transparent;
  border: 2px solid #24262d;
  color: #24262d;
}
a.btn_1.outline.white,
.btn_1.outline.white {
  border-color: #fff;
  color: #fff;
}
a.btn_1.outline:hover,
.btn_1.outline:hover {
  background-color: #4b514d;
  border: 2px solid transparent;
  color: #fff;
}

.btn_4 {
  position: relative;
  display: inline-block;
  cursor: pointer;
}
.btn_4.learn-more {
  width: 160px;
  height: auto;
}
.btn_4.learn-more .circle {
  transition: all 0.45s cubic-bezier(0.65, 0, 0.076, 1);
  position: relative;
  display: block;
  margin: 0;
  width: 2.6rem;
  height: 2.6rem;
  background: #978667;
  border-radius: 1.625rem;
}
.btn_4.learn-more .circle .icon {
  transition: all 0.45s cubic-bezier(0.65, 0, 0.076, 1);
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto;
  background: #fff;
}
.btn_4.learn-more .circle .icon.arrow {
  transition: all 0.45s cubic-bezier(0.65, 0, 0.076, 1);
  left: 0.425rem;
  width: 1.125rem;
  height: 0.125rem;
  background: none;
}
.btn_4.learn-more .circle .icon.arrow::before {
  position: absolute;
  content: "";
  top: -0.25rem;
  right: 0.0625rem;
  width: 0.625rem;
  height: 0.625rem;
  border-top: 0.125rem solid #fff;
  border-right: 0.125rem solid #fff;
  transform: rotate(45deg);
}
.btn_4.learn-more .button-text {
  transition: all 0.45s cubic-bezier(0.65, 0, 0.076, 1);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 0.75rem 0;
  margin: 0 0 0 1.5rem;
  color: #333;
  font-weight: 600;
  line-height: 1.1;
  text-align: center;
}
.btn_4:hover .circle {
  width: 100%;
}
.btn_4:hover .circle .icon.arrow {
  background: #fff;
  transform: translate(1rem, 0);
}
.btn_4:hover .button-text {
  color: #fff;
}

a.animated_link {
  position: relative;
  text-decoration: none;
}
a.animated_link::before {
  content: "";
  position: absolute;
  display: block;
  width: 100%;
  height: 2px;
  bottom: -5px;
  left: 0;
  background-color: #24262d;
  transform: scaleX(0);
  transform-origin: top left;
  transition: transform 0.3s ease;
}
a.animated_link:hover::before {
  transform: scaleX(1);
}
a.animated_link.active::before {
  transform: scaleX(1);
}

.mouse_wp {
  position: absolute;
  right: 30px;
  bottom: 30px;
  text-align: right;
  width: 40px;
  z-index: 999;
}

.mouse {
  background: #4e5559 linear-gradient(transparent 0%, transparent 50%, #ffffff 50%, #ffffff 100%);
  width: 36px;
  height: 58px;
  border-radius: 100px;
  background-size: 100% 200%;
  animation: colorSlide 15s linear infinite, nudgeMouse 15s ease-out infinite;
}

.mouse:before,
.mouse:after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
}

.mouse:before {
  width: 32px;
  height: 54px;
  background-color: #222a30;
  border-radius: 100px;
}

.mouse:after {
  background-color: #ffffff;
  width: 5px;
  height: 5px;
  border-radius: 100%;
  animation: trackBallSlide 15s linear infinite;
}
@keyframes colorSlide {
  0% {
    background-position: 0% 100%;
  }
  20% {
    background-position: 0% 0%;
  }
  21% {
    background-color: #4e5559;
  }
  29.99% {
    background-color: #ffffff;
    background-position: 0% 0%;
  }
  30% {
    background-color: #4e5559;
    background-position: 0% 100%;
  }
  50% {
    background-position: 0% 0%;
  }
  51% {
    background-color: #4e5559;
  }
  59% {
    background-color: #ffffff;
    background-position: 0% 0%;
  }
  60% {
    background-color: #4e5559;
    background-position: 0% 100%;
  }
  80% {
    background-position: 0% 0%;
  }
  81% {
    background-color: #4e5559;
  }
  90%, 100% {
    background-color: #ffffff;
  }
}
@keyframes trackBallSlide {
  0% {
    opacity: 1;
    transform: scale(1) translateY(-20px);
  }
  6% {
    opacity: 1;
    transform: scale(0.9) translateY(5px);
  }
  14% {
    opacity: 0;
    transform: scale(0.4) translateY(40px);
  }
  15%, 19% {
    opacity: 0;
    transform: scale(0.4) translateY(-20px);
  }
  28%, 29.99% {
    opacity: 1;
    transform: scale(1) translateY(-20px);
  }
  30% {
    opacity: 1;
    transform: scale(1) translateY(-20px);
  }
  36% {
    opacity: 1;
    transform: scale(0.9) translateY(5px);
  }
  44% {
    opacity: 0;
    transform: scale(0.4) translateY(40px);
  }
  45%, 49% {
    opacity: 0;
    transform: scale(0.4) translateY(-20px);
  }
  58%, 59.99% {
    opacity: 1;
    transform: scale(1) translateY(-20px);
  }
  60% {
    opacity: 1;
    transform: scale(1) translateY(-20px);
  }
  66% {
    opacity: 1;
    transform: scale(0.9) translateY(5px);
  }
  74% {
    opacity: 0;
    transform: scale(0.4) translateY(40px);
  }
  75%, 79% {
    opacity: 0;
    transform: scale(0.4) translateY(-20px);
  }
  88%, 100% {
    opacity: 1;
    transform: scale(1) translateY(-20px);
  }
}
@keyframes nudgeMouse {
  0% {
    transform: translateY(0);
  }
  20% {
    transform: translateY(8px);
  }
  30% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(8px);
  }
  60% {
    transform: translateY(0);
  }
  80% {
    transform: translateY(8px);
  }
  90% {
    transform: translateY(0);
  }
}
@keyframes nudgeText {
  0% {
    transform: translateY(0);
  }
  20% {
    transform: translateY(2px);
  }
  30% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(2px);
  }
  60% {
    transform: translateY(0);
  }
  80% {
    transform: translateY(2px);
  }
  90% {
    transform: translateY(0);
  }
}
@keyframes colorText {
  21% {
    color: #4e5559;
  }
  30% {
    color: #ffffff;
  }
  51% {
    color: #4e5559;
  }
  60% {
    color: #ffffff;
  }
  81% {
    color: #4e5559;
  }
  90% {
    color: #ffffff;
  }
}
/*-------- 1.4 Structure --------*/
/* Header */
header {
  padding: 20px 0;
  width: 100%;
  z-index: 999;
  position: fixed;
  left: 0;
  top: 0;
  background-color: rgba(255, 255, 255, 0);
  transition: all 0.3s ease;
}
@media (max-width: 767px) {
  header {
    padding: 15px 0;
  }
}
header .logo_sticky {
  display: none;
}
header .logo_normal {
  display: block;
}
header.reveal_header .container-fluid {
  padding-right: 25px;
  padding-left: 25px;
}
@media (max-width: 767px) {
  header.reveal_header .container-fluid {
    padding-right: 15px;
    padding-left: 15px;
  }
}
header.reveal_header.headroom--not-top {
  border-bottom: 1px solid #ededed;
  background-color: rgb(255, 255, 255);
}
header.reveal_header.headroom--top .logo_normal {
  display: block;
}
header.reveal_header.headroom--top nav ul li a {
  color: #fff;
}
header.reveal_header.headroom--top nav ul li a.animated_link:before {
  background-color: #fff;
}
header.reveal_header.headroom--top .logo_sticky {
  display: none;
}
header.reveal_header.headroom--not-top .logo_normal {
  display: none;
}
header.reveal_header.headroom--not-top .logo_sticky {
  display: block;
}
header nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
  text-align: right;
}
header nav ul li {
  display: inline-block;
  font-weight: 600;
  margin-left: 25px;
}
header nav ul li:first-child {
  margin-left: 0;
}
@media (max-width: 991px) {
  header nav ul li:first-child {
    display: none;
  }
}
header.fixed_header nav ul li a {
  color: #fff;
}
header.fixed_header nav ul li a.animated_link:before {
  background-color: #fff;
}
header.fixed_header.sticky {
  transition: all 0.3s ease-in-out;
  border-bottom: 1px solid #d9e1e6;
  background-color: #fff;
}
header.fixed_header.sticky .logo_normal {
  display: none;
}
header.fixed_header.sticky .logo_sticky {
  display: inline-block;
}
header.fixed_header.sticky nav ul li a {
  color: #333;
}
header.fixed_header.sticky nav ul li a.animated_link:before {
  background-color: #333;
}
header.fixed_header.sticky nav ul li a.btn_1 {
  color: #fff;
}
header.fixed_header.sticky .hamburger__inner,
header.fixed_header.sticky .hamburger__inner::before,
header.fixed_header.sticky .hamburger__inner::after {
  background-color: #333;
}

.hamburger_2 {
  position: relative;
  top: 8px;
  padding: 0 0;
  display: inline-block;
  cursor: pointer;
  transition-property: opacity, filter;
  transition-duration: 0.15s;
  transition-timing-function: linear;
  font: inherit;
  color: inherit;
  text-transform: none;
  background-color: transparent;
  border: 0;
  margin: 0;
  overflow: visible;
}
@media (max-width: 991px) {
  .hamburger_2 {
    top: 5px;
  }
}
.hamburger_2:hover .hamburger__inner {
  animation: burger 0.4s ease;
}
.hamburger_2:hover .hamburger__inner:before {
  animation: burger 0.4s 0.1s ease;
}
.hamburger_2:hover .hamburger__inner:after {
  animation: burger 0.4s 0.2s ease;
}
.hamburger_2 .hamburger__box {
  width: 24px;
  height: 24px;
  display: inline-block;
  position: relative;
}
.hamburger_2 .hamburger__inner {
  display: block;
  top: 50%;
  margin-top: -2px;
}
.hamburger_2 .hamburger__inner,
.hamburger_2 .hamburger__inner::before,
.hamburger_2 .hamburger__inner::after {
  width: 24px;
  height: 2px;
  background-color: #fff;
  border-radius: 0px;
  position: absolute;
  transition-property: transform;
  transition-duration: 0.15s;
  transition-timing-function: ease;
}
.hamburger_2 .hamburger__inner::before,
.hamburger_2 .hamburger__inner::after {
  content: "";
  display: block;
}
.hamburger_2 .hamburger__inner::before {
  top: -8px;
}
.hamburger_2 .hamburger__inner::after {
  bottom: -8px;
}

@keyframes burger {
  0% {
    transform-origin: 0 100%;
    transform: scale(1, 1);
  }
  50% {
    transform-origin: 0 100%;
    transform: scale(0, 1);
  }
  100% {
    transform-origin: 0 100%;
    transform: scale(1, 1);
  }
}
.headroom--not-top .hamburger__inner,
.headroom--not-top .hamburger__inner::before,
.headroom--not-top .hamburger__inner::after {
  background-color: #333;
}

/* Hamburger with close */
.hamburger {
  position: relative;
  top: 8px !important;
}

.headroom--not-top .hamburger-inner,
.headroom--not-top .hamburger-inner::before,
.headroom--not-top .hamburger-inner::after {
  background-color: #333;
}

/*Animated header revealed*/
.animated {
  animation-duration: 0.5s;
  animation-fill-mode: both;
  will-change: transform, opacity;
}
@keyframes slideDown {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(0);
  }
}
.animated.slideDown {
  animation-name: slideDown;
}
@keyframes slideUp {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-100%);
  }
}
.animated.slideUp {
  animation-name: slideUp;
}

/*-------- Menu Version 1 --------*/
.nav_panel {
  overflow-y: auto;
  transform: translateX(100%);
  top: 0;
  right: 0;
  bottom: 0;
  width: 350px;
  height: 100%;
  position: fixed;
  z-index: 99999;
  background-color: #fff;
  box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1);
  transition: all 0.5s cubic-bezier(0.77, 0, 0.175, 1);
}
.nav_panel.show {
  transform: translateX(0);
}
@media (max-width: 767px) {
  .nav_panel {
    width: 100%;
  }
}

.logo_panel {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 135px;
  background-color: #faf8f5;
}

.sidebar-navigation {
  border-top: 1px solid #ededed;
  overflow-x: hidden;
  width: 100%;
  position: absolute;
  padding-top: 20px;
  top: 135px;
  bottom: 0;
  left: 0;
  padding-left: 20px;
  padding-right: 10px;
  z-index: 9999;
}
.sidebar-navigation nav {
  position: relative;
  height: 100%;
  min-height: 450px;
}
.sidebar-navigation nav a {
  font-size: 14px;
  font-size: 0.875rem;
  color: #333;
  text-decoration: none;
  font-weight: 600;
  text-transform: uppercase;
}
.sidebar-navigation nav a.active {
  color: #978667;
}
.sidebar-navigation nav ul {
  list-style: none;
  position: absolute;
  top: 0;
  padding: 1rem;
  width: 100%;
  height: 100%;
  background: #fff;
  z-index: 1;
  transition: all 0.4s cubic-bezier(0.645, 0.045, 0.355, 1);
}
.sidebar-navigation nav ul ul {
  right: calc(200% + 10px);
  left: calc(100% + 20px);
}
.sidebar-navigation nav ul li.parent > a:after {
  font-family: "bootstrap-icons";
  content: "\f285";
  float: right;
}

.sidebar-navigation nav ul li.back a {
  color: #999;
  text-transform: none;
  background-color: rgba(255, 255, 255, 0.2);
}

.sidebar-navigation nav ul li.back {
  margin-left: -20px;
}
.sidebar-navigation nav ul li.back > a:before {
  font-family: "bootstrap-icons";
  content: "\f284";
  margin-right: 5px;
  float: left;
}

.sidebar-navigation nav ul li.back a:focus,
.sidebar-navigation nav ul li.back a:hover {
  color: #333;
}

.sidebar-navigation nav ul li a {
  margin-bottom: 10px;
  padding: 0.35rem 0.65rem;
  display: block;
  position: relative;
  transition: all 0.2s;
}

.sidebar-navigation nav ul li a:focus,
.sidebar-navigation nav ul li a:hover {
  color: #978667;
}

.sidebar-navigation nav ul .active {
  left: 0;
  right: 0;
}
.sidebar-navigation nav ul .active > li > a {
  opacity: 1;
}

.sidebar-navigation nav ul.hidden > li > a {
  opacity: 0;
}

.sidebar-navigation nav ul ul .active {
  left: calc(100% + 20px);
  right: calc(100% + 20px);
}

.panel_footer {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 99999;
  padding: 0 25px;
  font-size: 14px;
  font-size: 0.875rem;
}

.phone_element {
  border-top: 1px solid #d9e1e6;
  padding: 20px 0 20px 0;
  position: relative;
  line-height: 1.4;
}
.phone_element a {
  display: flex;
  align-items: center;
}
.phone_element a span {
  font-size: 18px;
  font-size: 1.125rem;
  font-weight: 600;
  color: #978667;
}
.phone_element a span em {
  color: #333;
  display: block;
  font-style: inherit;
  text-transform: uppercase;
  font-size: 11px;
  font-size: 0.6875rem;
  font-weight: 500;
  letter-spacing: 2px;
}
.phone_element a i {
  margin-right: 15px;
  font-size: 30px;
  font-size: 1.875rem;
  color: #978667;
}
.phone_element.no_borders {
  padding-top: 5px;
  border-top: none;
}

.closebt {
  transition: all 0.3s ease-in-out;
  cursor: pointer;
  color: #ccc;
  display: inline-block;
  text-align: center;
  position: absolute;
  right: 25px;
  top: 0;
}
.closebt i {
  font-size: 42px;
  font-size: 2.625rem;
}
.closebt:hover {
  transform: rotate(90deg);
  opacity: 1;
}

/*-------- Menu Version 2 --------*/
a.close_panel {
  position: absolute;
  right: 20px;
  top: 10px;
  font-size: 32px;
  font-size: 2rem;
  color: #ccc;
}
a.close_panel:hover {
  color: #444;
}

.panel_menu {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 99;
  width: 100%;
  height: 100%;
  background-color: #4b514d;
  overflow: hidden;
  transition: all 600ms cubic-bezier(0.19, 1, 0.56, 1);
  transform: translate3d(0, -100%, 0);
}
.panel_menu.active {
  transform: translate3d(0, 0, 0);
}
.panel_menu .panel__content {
  height: 100%;
  width: 100%;
  text-align: left;
  opacity: 0;
  transition: all 600ms;
  transition-delay: 600ms;
}
.panel_menu .panel__content .panel__content_wrapper {
  padding: 0 15px;
  background-color: #4a504c;
  background-color: rgba(75, 81, 77, 0.9);
  width: 100%;
  height: 100%;
  color: #fff;
  display: flex;
  align-items: center;
}
@media (max-width: 767px) {
  .panel_menu .panel__content .panel__content_wrapper {
    padding-top: 30px;
    overflow: auto;
    display: block;
  }
}
.panel_menu .panel__content .contacts ul {
  list-style: none;
  padding: 0;
  margin: 0 0 25px 0;
}
.panel_menu .panel__content .contacts ul li a {
  display: flex;
  align-items: center;
}
.panel_menu .panel__content .contacts ul li a i {
  font-size: 24px;
  font-size: 1.5rem;
  margin-right: 10px;
}
@media (max-width: 991px) {
  .panel_menu .wrapper_menu {
    margin-top: 100px;
  }
}
.panel_menu .wrapper_menu > ul {
  list-style: none;
  margin: 0 0 25px 0;
  padding: 0;
}
.panel_menu .wrapper_menu > ul li a {
  position: relative;
  font-size: 36px;
  font-size: 2.25rem;
  font-weight: 600;
  letter-spacing: -0.03em;
  text-decoration: none;
  color: #FFF;
  transition: opacity 0.3s ease-in-out;
}
@media (max-width: 767px) {
  .panel_menu .wrapper_menu > ul li a {
    font-size: 24px;
    font-size: 1.5rem;
  }
}

.panel_menu.active .panel__content {
  opacity: 1;
}

.no_scroll {
  overflow: hidden;
}

@media (max-width: 991px) {
  .header_color {
    background-color: #4b514d;
  }
}

.image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  opacity: 0;
  visibility: hidden;
  transition: all 1s ease-in-out;
}
.image img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: 50% 50%;
     object-position: 50% 50%;
  filter: grayscale(100%);
}

ul > li:first-child .image {
  opacity: 1;
  visibility: visible;
  transition-delay: 1000ms;
}

.container-item.active a + .image {
  opacity: 1;
  visibility: visible;
  overflow: hidden;
  width: 100%;
  height: 100%;
}

.wrapper_menu.hover a {
  opacity: 0.4;
}

.container-item.active a {
  opacity: 1;
}

.menu_panel_details {
  width: 100%;
  font-size: 16px;
  font-size: 1rem;
  margin-bottom: 10px;
}
.menu_panel_details h5 {
  color: #fff;
  font-size: 19px;
  font-size: 1.1875rem;
}
.menu_panel_details p {
  margin-bottom: 20px;
}
.menu_panel_details a {
  color: #fff;
}
.menu_panel_details a:hover {
  color: #ebd7b2;
}

/*-------- Menu v4 --------*/
.menu_v4 .hamburger_2 {
  top: 5px;
}
.menu_v4 .hamburger_2,
.menu_v4 .closebt,
.menu_v4 .layer {
  display: none;
}
@media (max-width: 991px) {
  .menu_v4 .hamburger_2,
  .menu_v4 .closebt,
  .menu_v4 .layer {
    display: block;
  }
}
.menu_v4 .logo_panel {
  display: none;
  border-bottom: 1px solid #ededed;
}
@media (max-width: 991px) {
  .menu_v4 .logo_panel {
    display: flex;
  }
}

@media only screen and (max-width: 991px) {
  .main-menu {
    overflow-y: auto;
    transform: translateX(102%);
    top: 0;
    right: 0;
    bottom: 0;
    width: 350px;
    height: 100%;
    position: fixed;
    z-index: 99999;
    background-color: #fff;
    transition: all 0.5s cubic-bezier(0.77, 0, 0.175, 1);
    box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1);
    -webkit-transition: all 0.4s cubic-bezier(0.77, 0, 0.175, 1);
    -moz-transition: all 0.4s cubic-bezier(0.77, 0, 0.175, 1);
  }
  .main-menu #mainNav {
    padding: 30px;
  }
  .main-menu #mainNav ul {
    text-align: left;
  }
  .main-menu #mainNav ul li {
    display: block;
    margin: 0;
  }
  .main-menu #mainNav ul li a {
    display: block;
    color: #333 !important;
    text-transform: uppercase;
    padding: 0.35rem 0.65rem !important;
    margin: 0 0 10px 0;
  }
  .main-menu #mainNav ul li a:before {
    display: none !important;
  }
  .main-menu #mainNav ul li a:hover, .main-menu #mainNav ul li a.active {
    color: #978667 !important;
  }
  .main-menu #mainNav ul li a.btn_1 {
    background-color: transparent;
  }
  .main-menu.show {
    transform: translateX(0);
  }
}
.nav_panel #mainNav nav ul > li {
  display: block;
  margin: 15px 0;
  padding: 0 !important;
  opacity: 0;
  transition: all 0.8s ease 500ms;
  transform: translateY(30px);
}

.nav_panel #mainNav nav ul > li:first-child {
  transition-delay: 0.2s;
}

.nav_panel #mainNav nav ul > li:nth-child(2) {
  transition-delay: 0.3s;
}

.nav_panel #mainNav nav ul > li:nth-child(3) {
  transition-delay: 0.4s;
}

.nav_panel #mainNav nav ul > li:nth-child(4) {
  transition-delay: 0.5s;
}

.nav_panel #mainNav nav ul > li:nth-child(5) {
  transition-delay: 0.6s;
}

.nav_panel.show #mainNav nav ul > li {
  transform: translateY(0);
  opacity: 1;
}

/*-------- Menu version 4 with submenu example on menu-5.html --------*/
.submenu_version .main-menu ul li {
  position: relative;
}

/* All styles for screen size over 992px
================================================== */
@media only screen and (min-width: 992px) {
  .submenu_version .main-menu {
    width: auto;
  }
  .submenu_version .main-menu ul > li > a:hover {
    opacity: 0.6;
  }
  .submenu_version .main-menu ul > li > a.btn_1:hover {
    opacity: 0.6;
  }
  .submenu_version .main-menu ul li.submenu:hover > a:before,
  .submenu_version .main-menu ul li.submenu:hover > a:after {
    bottom: -10px;
    opacity: 0;
  }
  .submenu_version .main-menu ul ul {
    position: absolute;
    border-top: 2px solid #978667;
    z-index: 1;
    visibility: hidden;
    left: 0;
    top: 130%;
    margin: 0;
    display: block;
    text-align: left;
    padding: 0;
    background: #fff;
    min-width: 210px;
    box-shadow: 0px 6px 12px 0px rgba(0, 0, 0, 0.175);
    transform: scale(0.4);
    transform-origin: 10% top;
    transition: 0.15s linear, 0.1s opacity cubic-bezier(0.39, 0.575, 0.565, 1), 0.15s transform cubic-bezier(0.1, 1.26, 0.83, 1);
    border-radius: 3px;
    opacity: 0;
    transition: all 0.2s ease;
  }
  .submenu_version .main-menu ul ul:before {
    bottom: 100%;
    left: 12%;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
    border-bottom-color: #978667;
    border-width: 7px;
    margin-left: -7px;
  }
  .submenu_version .main-menu ul li:hover > ul {
    padding: 0;
    visibility: visible;
    opacity: 1;
    transform: scale(1);
    transition-delay: 0.2s;
    transition-duration: 0s, 0.2s, 0.2s;
  }
  .submenu_version .main-menu ul ul li {
    display: block;
    margin: 0;
  }
  .submenu_version .main-menu ul ul li a {
    font-size: 13px;
    font-size: 0.8125rem;
    color: #333 !important;
    display: block;
    padding: 12px 15px;
    font-weight: 500;
    transition: all 0.3s ease;
  }
  .submenu_version .main-menu ul ul li:last-child a {
    border-bottom: none;
  }
  .submenu_version .main-menu ul ul li:first-child a:hover {
    border-radius: 3px 3px 0 0;
  }
  .submenu_version .main-menu ul ul li:last-child a {
    border-bottom: none;
  }
  .submenu_version .main-menu ul ul li:last-child a:hover {
    border-radius: 0 0 3px 3px;
  }
  .submenu_version .main-menu ul ul li:hover > a {
    background-color: #f9f9f9;
    color: #978667 !important;
    padding-left: 20px;
    opacity: 1;
  }
}
@media only screen and (max-width: 991px) {
  .submenu_version .main-menu ul > li > a.show-submenu:hover:after {
    color: #978667;
  }
  .submenu_version .main-menu ul > li > a.show-submenu:after {
    transition: all 0.3s ease-in-out;
    font-family: "bootstrap-icons";
    font-size: 18px;
    font-size: 1.125rem;
    content: "\f282";
    line-height: 1;
    float: right;
    transform: rotate(0);
  }
  .submenu_version .main-menu ul > li > a.show-submenu.show_normal:after {
    transform: rotate(180deg);
  }
  .submenu_version .main-menu ul li.submenu ul {
    border-left: 1px solid #ededed;
    margin: 0 0 15px 25px;
  }
  .submenu_version .main-menu #mainNav ul li ul li a {
    text-transform: none !important;
    font-weight: 500;
    font-size: 14px;
    font-size: 0.875rem;
  }
  .submenu_version .main-menu .show-submenu + ul {
    opacity: 0;
    display: none;
  }
  .submenu_version .main-menu a.show-submenu.show_normal + ul {
    opacity: 1;
    display: block;
  }
  .submenu_version .main-menu #mainNav a.show-submenu.show_normal {
    color: #978667 !important;
  }
}
@media only screen and (max-width: 560px) {
  .main-menu.show {
    width: 100%;
  }
}
/*-------- Hero --------*/
.opacity-mask {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 2;
}

.background-image {
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
  transition: all 0.5s ease;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1;
}

.jarallax {
  position: relative;
  z-index: 0;
}

.jarallax > .jarallax-img {
  position: absolute;
  -o-object-fit: cover;
     object-fit: cover;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

span[data-jarallax-element] {
  display: block;
}

.kenburns .owl-item.active .item img,
img.jarallax-img.kenburns {
  transform-origin: 50% 50%;
  animation: kenburns 15s linear 0s;
  transition: all 1s ease-in-out;
  transition: all 1s ease-in-out;
  animation-fill-mode: forwards;
  /* or: backwards, both, none */
}

@keyframes kenburns {
  0% {
    transform: scale3d(1.4, 1.4, 1.4) translate3d(-5%, -2%, 0px);
    transition: transform 7000ms linear 1s;
  }
  100% {
    transform: scale3d(1.25, 1.25, 1.25) translate3d(0px, 0px, 0px);
    transition: transform 7000ms linear 1s;
  }
}
img.jarallax-img.kenburns-2 {
  transform-origin: 50% 50%;
  animation: kenburns 10s linear 0s;
  transition: all 1s ease-in-out;
  transition: all 1s ease-in-out;
  animation-fill-mode: forwards;
  /* or: backwards, both, none */
}

.hero {
  color: #fff;
  overflow: hidden;
}
.hero.full-height {
  height: 100vh;
}
@media (max-width: 991px) {
  .hero.full-height {
    min-height: 600px;
  }
}
@media (max-width: 767px) {
  .hero.full-height {
    height: 87vh;
  }
}
.hero.medium-height {
  height: 70vh;
}
@media (max-width: 991px) {
  .hero.medium-height {
    min-height: 600px;
  }
}
.hero.small-height {
  height: 60vh;
}
@media (max-width: 991px) {
  .hero.small-height {
    min-height: 600px;
  }
}
.hero .wrapper {
  text-align: left;
  height: 100%;
}
.hero .wrapper small {
  text-transform: uppercase;
  letter-spacing: 3px;
  display: block;
  margin: 0 0 10px 0;
  color: #ebd7b2;
  font-weight: 600;
}
.hero .wrapper h1,
.hero .wrapper h3 {
  color: #fff;
  font-size: 51px;
  font-size: 3.1875rem;
  margin: 0 0 10px 0;
  font-weight: 700;
  text-transform: uppercase;
}
@media (max-width: 767px) {
  .hero .wrapper h1,
  .hero .wrapper h3 {
    font-size: 36px;
    font-size: 2.25rem;
  }
}
@media (max-width: 575px) {
  .hero .wrapper h1,
  .hero .wrapper h3 {
    font-size: 32px;
    font-size: 2rem;
  }
}
.hero .wrapper p {
  font-weight: 400;
  font-size: 24px;
  font-size: 1.5rem;
  line-height: 1.4;
  margin-bottom: 0;
}
.hero .wrapper p strong {
  font-weight: 600;
}
@media (max-width: 767px) {
  .hero .wrapper p {
    padding: 0;
    font-size: 18px;
    font-size: 1.125rem;
  }
}

.parallax_section_1 {
  overflow: hidden;
  height: 70vh;
}
@media (max-width: 991px) {
  .parallax_section_1 {
    min-height: 650px;
  }
}

/* Hero new datepicker */
.hero.home-search {
  overflow: visible;
}
@media (max-width: 991px) {
  .hero.home-search {
    z-index: 9999 !important;
  }
}

/*-------- Footer --------*/
footer {
  background-color: #24262d;
  position: relative;
  color: #fff;
}
@media (max-width: 991px) {
  footer {
    z-index: 0;
  }
}
footer .footer_bg {
  position: relative;
  padding-top: 230px;
  z-index: 2;
  overflow: hidden;
}
@media (max-width: 767px) {
  footer .footer_bg {
    height: 160px;
    padding-top: 180px;
  }
}
footer .footer_bg .background-image {
  opacity: 0.3;
  filter: grayscale(100%);
}
footer .footer_bg .gradient_over {
  background: transparent;
  background: linear-gradient(to bottom, transparent, #24262d);
  top: 0;
  bottom: 0;
  height: 100%;
  left: 0;
  pointer-events: none;
  position: absolute;
  width: 100%;
  z-index: 3;
}
footer .move_content {
  margin-top: -80px;
  position: relative;
  z-index: 4;
  padding-bottom: 120px;
}
@media (max-width: 767px) {
  footer .move_content {
    padding-bottom: 60px;
  }
}
footer h5 {
  color: #fff;
  margin-bottom: 20px;
  font-size: 20px;
  font-size: 1.25rem;
}
@media (max-width: 767px) {
  footer h5 {
    margin-bottom: 10px;
  }
}
footer hr {
  opacity: 0.1;
}
footer ul {
  list-style: none;
  padding: 0;
  margin: 0 0 25px 0;
}
footer a {
  color: #ebd7b2;
}
footer a:hover {
  color: #fff;
}
footer .copy {
  text-align: center;
  padding: 25px 0;
  background-color: #1f2127;
  font-size: 13px;
  font-size: 0.8125rem;
}

.social ul {
  display: block;
  list-style: none;
  margin: 0 0 25px 0;
  padding: 0;
}
.social ul li {
  display: inline-block;
  margin: 0 5px;
}
.social ul li a {
  display: block;
  color: #fff;
}
.social ul li a:hover {
  color: #ebd7b2;
}
.social ul li a i {
  font-size: 21px;
  font-size: 1.3125rem;
}

.styled-select {
  max-width: 180px;
  overflow: hidden;
  height: 40px;
  line-height: 40px;
  border: none;
  background-color: #f3f3f3;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
  padding-right: 35px;
  position: relative;
}
.styled-select.lang-selector:after {
  font-family: "bootstrap-icons";
  content: "\f3ee";
  color: #fff;
  position: absolute;
  right: 15px;
  top: 0;
}
.styled-select select {
  background: transparent;
  width: 110%;
  padding-left: 15px;
  border: 0;
  border-radius: 0;
  box-shadow: none;
  height: 30px;
  color: #ccc;
  font-size: 13px;
  font-size: 0.8125rem;
  margin: 0;
  font-weight: 500;
  appearance: window;
  -moz-appearance: window;
  -webkit-appearance: none;
  cursor: pointer;
  outline: none;
}
.styled-select select:focus {
  color: #fff;
  outline: none;
  box-shadow: none;
}
.styled-select select::-ms-expand {
  display: none;
}

.styled-select.lang-selector select option {
  color: #555;
}

.footer_links {
  margin-bottom: 25px;
}
.footer_links ul {
  line-height: 1.9;
}
.footer_links ul li a {
  display: inline-block;
  color: #fff;
}
.footer_links ul li a:hover {
  transform: translateX(10px);
}

#newsletter .form-group {
  position: relative;
}
#newsletter h5 {
  margin: 0 0 25px 0;
}
@media (max-width: 767px) {
  #newsletter h5 {
    margin: 0 0 15px 0;
  }
}
#newsletter .form-group {
  position: relative;
}
#newsletter input[type=email] {
  border: 0;
  height: 45px;
  border-radius: 3px;
  padding-left: 15px;
  background-color: #f3f3f3;
  background-color: rgba(255, 255, 255, 0.06);
  color: #fff;
}
#newsletter input[type=email]:focus {
  border: 0;
  box-shadow: none;
}
#newsletter button[type=submit] {
  position: absolute;
  right: 5px;
  color: #fff;
  font-size: 22px;
  font-size: 1.375rem;
  top: 2px;
  border: 0;
  opacity: 0.6;
  height: 45px;
  cursor: pointer;
  background-color: transparent;
  transition: all 0.3s ease-in-out;
  outline: none;
}
#newsletter button[type=submit]:hover {
  opacity: 1;
}

/* Opacity mask when left open */
.layer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  min-width: 100%;
  min-height: 100%;
  z-index: 9999;
  background-color: rgba(0, 0, 0, 0.35);
  transition: all 0.1s ease;
  opacity: 0;
  visibility: hidden;
}

.layer-is-visible {
  opacity: 1;
  visibility: visible;
}

/*============================================================================================*/
/* 2.  CONTENT */
/*============================================================================================*/
/*-------- 2.1 Home page --------*/
.booking_form {
  background-color: #fff;
  padding: 5px;
  border-radius: 5px;
  margin: 15px auto 0;
  position: relative;
  box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.3);
}
@media (max-width: 991px) {
  .booking_form {
    padding: 0;
    background: none;
    box-shadow: none;
  }
}
.booking_form input {
  border: 0;
  height: 50px;
  padding-left: 15px;
  border-radius: 0;
  background-color: transparent;
  border-right: 1px solid #d9e1e6;
  font-weight: 500;
  font-size: 15px;
  font-size: 0.9375rem;
  color: #6c757d;
}
@media (max-width: 991px) {
  .booking_form input {
    border: none;
    background-color: #fff;
    border-radius: 5px;
    margin-bottom: 10px;
  }
}
.booking_form input:focus {
  box-shadow: none;
  border-right: 1px solid #d9e1e6;
}
@media (max-width: 991px) {
  .booking_form input:focus {
    border-right: none;
  }
}
.booking_form .form-group {
  margin: 0;
  position: relative;
}
@media (max-width: 991px) {
  .booking_form .form-group {
    margin-bottom: 5px;
  }
}
.booking_form .form-group i {
  border-radius: 3px;
  font-size: 21px;
  font-size: 1.3125rem;
  position: absolute;
  line-height: 50px;
  top: 2px;
  right: 4px;
  padding-right: 15px;
  display: block;
  width: 20px;
  box-sizing: content-box;
  height: 50px;
  z-index: 1;
  color: #978667;
}
.booking_form input[type=submit] {
  transition: all 0.3s ease-in-out;
  color: #fff;
  font-weight: 600;
  font-size: 15px;
  font-size: 0.9375rem;
  border: 0;
  height: 50px;
  cursor: pointer;
  outline: none;
  width: 100%;
  border-radius: 3px;
  background-color: #4b514d;
  margin-right: 5px;
}
@media (max-width: 991px) {
  .booking_form input[type=submit] {
    margin: 20px 0 0 0;
    border-radius: 3px;
  }
}
.booking_form input[type=submit]:hover {
  background-color: #978667;
  color: #fff;
}

/* Quantity incrementer input */
.qty-buttons {
  position: relative;
  width: 100%;
  display: inline-block;
}
.qty-buttons label {
  position: absolute;
  color: #999;
  left: 15px;
  top: 13px;
  font-weight: 500;
  color: #6c757d;
  font-size: 15px;
  font-size: 0.9375rem;
}
.qty-buttons input.qty {
  width: 100%;
  text-align: left;
  padding-left: 80px;
}
.qty-buttons input.qtyminus,
.qty-buttons input.qtyplus {
  position: absolute;
  width: 36px;
  height: 36px;
  border: 0;
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
  -moz-appearance: none;
       appearance: none;
  text-indent: -9999px;
  box-shadow: none;
  border-radius: 50%;
  top: 8px;
}
.qty-buttons input.qtyplus {
  background: #f5f5f5 url(../img/plus.svg) no-repeat center center;
  right: 15px;
}
.qty-buttons input.qtyminus {
  background: #f5f5f5 url(../img/minus.svg) no-repeat center center;
  right: 55px;
}
.qty-buttons.version_2 .form-control {
  height: 50px;
}
.qty-buttons.version_2 input.qty {
  padding-left: 15px;
}
.qty-buttons.version_2 input.qtyplus {
  background: #fff url(../img/plus.svg) no-repeat center center;
  right: 5px;
}
.qty-buttons.version_2 input.qtyminus {
  background: #fff url(../img/minus.svg) no-repeat center center;
  right: 40px;
}

.intro em {
  font-family: "Caveat", cursive;
  font-size: 32px;
  font-size: 2rem;
  font-style: normal;
  color: #555;
}

/*-------- Carousel Home Page --------*/
#carousel-home {
  position: relative;
}
#carousel-home .static {
  position: static;
}
#carousel-home .cover {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  max-height: none !important;
  z-index: 2;
  opacity: 1 !important;
}
#carousel-home .owl-carousel {
  position: relative;
}
#carousel-home .owl-carousel .owl-slide {
  height: 100vh;
  position: relative;
}
@media (max-width: 767px) {
  #carousel-home .owl-carousel .owl-slide {
    height: 90vh;
  }
}
#carousel-home .owl-carousel .owl-slide-animated {
  transform: translateY(20px);
  opacity: 0;
  visibility: hidden;
  transition: all 0.05s ease;
}
#carousel-home .owl-carousel .owl-slide-animated.is-transitioned {
  transform: none;
  opacity: 1;
  visibility: visible;
  transition: all 0.5s ease;
}
#carousel-home .owl-carousel .owl-slide-title.is-transitioned {
  transition-delay: 0.2s;
  -webkit-transition-delay: 0.2s;
  -moz-transition-delay: 0.2s;
  -o-transition-delay: 0.2s;
}
#carousel-home .owl-carousel .owl-slide-title-2.is-transitioned {
  transition-delay: 0.4s;
  -webkit-transition-delay: 0.4s;
  -moz-transition-delay: 0.4s;
  -o-transition-delay: 0.4s;
}
#carousel-home .owl-carousel .owl-slide-title-3.is-transitioned {
  transition-delay: 0.6s;
  -webkit-transition-delay: 0.6s;
  -moz-transition-delay: 0.6s;
  -o-transition-delay: 0.6s;
}
#carousel-home .owl-carousel .owl-slide-title-4.is-transitioned {
  transition-delay: 0.8s;
  -webkit-transition-delay: 0.8s;
  -moz-transition-delay: 0.8s;
  -o-transition-delay: 0.8s;
}
#carousel-home .owl-carousel .owl-dots .owl-dot,
#carousel-home .owl-carousel .owl-nav [class*=owl-]:focus {
  outline: none;
}

.owl-carousel .owl-dots {
  margin-top: 0 !important;
  right: 15px;
  width: 40px;
  top: 50%;
  transform: translateY(-50%);
  position: absolute;
  z-index: 3;
}

.owl-carousel.nav-dots-orizontal .owl-dots {
  margin-top: 20px !important;
  width: 100%;
  position: static;
}

.slide-text h2,
.slide-text h3 {
  font-size: 51px;
  font-size: 3.1875rem;
  text-transform: uppercase;
  font-weight: 700;
}
@media (max-width: 767px) {
  .slide-text h2,
  .slide-text h3 {
    font-size: 42px;
    font-size: 2.625rem;
  }
}
.slide-text small {
  text-transform: uppercase;
  color: #ebd7b2;
  letter-spacing: 4px;
  font-weight: 600;
  display: block;
  margin: 0 0 10px 0;
}
.slide-text p {
  font-size: 21px;
  font-size: 1.3125rem;
}
.slide-text.white {
  color: #fff;
}
.slide-text.white h2,
.slide-text.white h3 {
  color: #fff;
}
.slide-text.black {
  color: #111;
}
.slide-text.black h2 {
  color: #000;
}

#icon_drag_mobile {
  position: absolute;
  right: 20px;
  bottom: 20px;
  background: url(../img/drag_icon.svg) 0 0 no-repeat;
  width: 50px;
  height: 30px;
  opacity: 0.6;
  z-index: 99;
  display: none;
}
@media (max-width: 767px) {
  #icon_drag_mobile {
    display: block;
  }
}

a.box_cat_rooms {
  display: block;
  margin-bottom: 25px;
  height: 500px;
  position: relative;
  overflow: hidden;
  background-color: #ccc;
  border-radius: 10px;
  box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.2);
}
@media (max-width: 991px) {
  a.box_cat_rooms {
    height: 400px;
  }
}
a.box_cat_rooms .info {
  position: absolute;
  width: 100%;
  z-index: 9;
  display: block;
  padding: 25% 30px 0 30px;
  color: #fff;
  left: 0;
  bottom: -75px;
  padding-bottom: 60px;
  backface-visibility: hidden;
  transform: translate(0, 0);
  transition: all 0.4s ease;
  background: transparent;
  background: linear-gradient(to bottom, transparent, #000);
}
a.box_cat_rooms .info small {
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 2px;
  color: #999;
  display: block;
  margin-bottom: 0;
  font-size: 12px;
  font-size: 0.75rem;
  color: #fff;
}
a.box_cat_rooms .info h3 {
  color: #fff;
  font-size: 26px;
  font-size: 1.625rem;
  font-weight: 600;
  margin-bottom: 15px;
}
a.box_cat_rooms .info span {
  font-size: 15px;
  font-size: 0.9375rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  font-weight: 600;
  opacity: 0;
  position: relative;
}
a.box_cat_rooms .info span:before {
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 10px;
  height: 2px;
  content: "";
  transition: width 500ms ease;
  background-color: #fff;
}
a.box_cat_rooms .info span:hover {
  background-color: #24262d;
  border-color: transparent;
}
a.box_cat_rooms figure {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
}
a.box_cat_rooms:hover .info {
  transform: translate(0, -65px);
}
a.box_cat_rooms:hover .info span {
  opacity: 1;
}
a.box_cat_rooms:hover .info span:before {
  width: 100%;
}
a.box_cat_rooms:hover .background-image {
  transform: scale(1.1);
}

/*-------- 2.2 About  --------*/
.box_facilities {
  padding: 0 25px 25px 25px;
  border-width: 2px;
  text-align: center;
  border-style: solid;
  border-right: 0;
  -o-border-image: linear-gradient(to bottom, #ededed, rgba(0, 0, 0, 0)) 1 60%;
     border-image: linear-gradient(to bottom, #ededed, rgba(0, 0, 0, 0)) 1 60%;
}
@media (max-width: 1199px) {
  .box_facilities {
    border-width: 0;
  }
}
.box_facilities i {
  font-size: 62px;
  font-size: 3.875rem;
  color: #978667;
}
.box_facilities h3 {
  font-size: 21px;
  font-size: 1.3125rem;
}
@media (max-width: 767px) {
  .box_facilities h3 {
    font-size: 18px;
    font-size: 1.125rem;
  }
}
.box_facilities.no-border {
  border-width: 0;
}
.box_facilities.white {
  -o-border-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), rgba(0, 0, 0, 0)) 1 60%;
     border-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), rgba(0, 0, 0, 0)) 1 60%;
}
.box_facilities.white h3 {
  color: #fff;
}
.box_facilities.white i {
  color: #ebd7b2;
}
@media (max-width: 767px) {
  .box_facilities.white {
    padding: 0;
    margin-bottom: 25px;
  }
  .box_facilities.white i {
    font-size: 62px;
    font-size: 3.875rem;
  }
}

/* Testimonials */
.owl-carousel .animated {
  animation-duration: 1s;
  animation-fill-mode: both;
}

.animated.flip {
  backface-visibility: visible;
  animation-name: flip;
}
@keyframes flipInX {
  0% {
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    transition-timing-function: ease-in;
    opacity: 0;
  }
  40% {
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    transition-timing-function: ease-in;
  }
  60% {
    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    opacity: 1;
  }
  80% {
    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
  }
  100% {
    transform: perspective(400px);
  }
}
.flipInX {
  backface-visibility: visible !important;
  animation-name: flipInX;
}

.box_overlay {
  background-color: #24262d;
  background-color: #24262d;
  padding: 45px 45px 45px 225px;
  min-height: 170px;
  color: #fff;
  position: relative;
  margin-bottom: 20px;
  border-radius: 10px;
}
@media (max-width: 767px) {
  .box_overlay {
    padding: 30px;
  }
}

.pic {
  position: absolute;
  left: 45px;
  top: 45px;
  text-align: left;
}
@media (max-width: 767px) {
  .pic {
    position: static;
    margin-bottom: 20px;
    width: 100%;
    text-align: center;
  }
}
.pic h4 {
  position: absolute;
  left: 95px;
  top: 18px;
  color: #fff;
  font-size: 14px;
  font-size: 0.875rem;
  font-weight: 500;
}
@media (max-width: 767px) {
  .pic h4 {
    position: static;
  }
}
.pic h4 small {
  display: block;
  color: #fff;
  margin-top: 3px;
  letter-spacing: normal !important;
}
.pic figure {
  width: 80px;
  height: 80px;
  overflow: hidden;
  border-radius: 50%;
}
@media (max-width: 767px) {
  .pic figure {
    margin: 0 auto 10px;
  }
}
.pic figure img {
  width: 80px;
  height: auto;
}

.comment {
  border-left: 1px solid rgba(255, 255, 255, 0.3);
  padding-left: 30px;
  font-size: 18px;
  font-size: 1.125rem;
  font-style: italic;
  text-align: left;
}
@media (max-width: 767px) {
  .comment {
    text-align: center;
    border: 0;
    padding: 0;
  }
}

/* Accordion */
.accordion .card {
  border: 0;
  margin-bottom: 10px;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.05);
  border-radius: 5px;
}
.accordion .card .card-header {
  background-color: #fff;
  padding: 20px;
  border: 0;
}
.accordion .card-body {
  padding: 0 20px;
}
.accordion .card-header h5 {
  font-size: 17px;
  font-size: 1.0625rem;
  color: #333;
}
.accordion .card-header h5 a {
  border: 0;
  display: block;
  color: #333;
}
.accordion .card-header h5 a i.indicator {
  font-size: 18px;
  font-size: 1.125rem;
  font-weight: normal;
  float: right;
  color: #333;
  transform: rotate(45deg);
  transition: all 0.3s ease-in-out;
}
.accordion .card-header h5 a.collapsed i {
  transform: rotate(0deg);
}

/*-------- 2.3 Room list --------*/
.box_item_info {
  background-color: #fff;
  z-index: 9;
  position: relative;
  padding: 40px 40px 20px 40px;
  box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.1);
}
.box_item_info small {
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 2px;
  display: block;
  margin-bottom: 0;
  font-size: 14px;
  font-size: 0.875rem;
  color: #978667;
}
.box_item_info h2 {
  font-size: 30px;
  font-size: 1.875rem;
  font-weight: 600;
  margin-bottom: 20px;
}
.box_item_info .facilities ul {
  list-style: none;
  padding: 0;
  margin: 0 0 15px 0;
}
.box_item_info .facilities ul li {
  float: left;
  display: flex;
  align-items: center;
  line-height: 1;
  margin: 0 25px 10px 0;
}
.box_item_info .facilities ul li:last-child {
  margin: 0;
}
.box_item_info .facilities ul li i {
  height: 36px;
  font-size: 30px;
  font-size: 1.875rem;
  color: #978667;
  margin-right: 15px;
}
@media (max-width: 767px) {
  .box_item_info .facilities ul li {
    float: none;
    width: 100%;
  }
}
.box_item_info .box_item_footer {
  border-top: 2px solid #ededed;
  margin-top: 15px;
  padding: 25px 0 15px 0;
}
.box_item_info .box_item_footer a.animated_link {
  color: #333;
}

.row_list_version_1 {
  margin-bottom: 60px;
}
.row_list_version_1 .box_item_info {
  max-width: 750px;
  margin: -250px 60px 60px 60px;
}
@media (max-width: 767px) {
  .row_list_version_1 .box_item_info {
    margin: -150px 10px 0 10px;
    padding: 20px 25px 10px 25px;
  }
}

.row_list_version_2 {
  margin-bottom: 120px;
}
@media (max-width: 767px) {
  .row_list_version_2 {
    margin-bottom: 70px;
  }
}
.row_list_version_2 .owl-carousel .owl-dots {
  right: auto;
  left: 15px;
}
.row_list_version_2 .box_item_info {
  margin: 0 0 0 -100px;
}
@media (max-width: 1199px) {
  .row_list_version_2 .box_item_info {
    margin: -150px 30px 0 30px;
  }
}
@media (max-width: 767px) {
  .row_list_version_2 .box_item_info {
    margin: -50px 10px 0 10px;
    padding: 20px 25px 10px 25px;
  }
}
.row_list_version_2.inverted .owl-carousel .owl-dots {
  left: inherit;
  right: 15px;
}
.row_list_version_2.inverted .box_item_info {
  margin: 0 -100px 0 0;
}
@media (max-width: 1199px) {
  .row_list_version_2.inverted .box_item_info {
    margin: -50px 30px 0 30px;
  }
}
@media (max-width: 767px) {
  .row_list_version_2.inverted .box_item_info {
    margin: -50px 10px 0 10px;
    padding: 20px 25px 10px 25px;
  }
}

.row_list_version_3 {
  margin-bottom: 60px;
}
.row_list_version_3 .box_item_info {
  max-width: 750px;
  margin: -100px 60px 60px 60px;
}
@media (max-width: 767px) {
  .row_list_version_3 .box_item_info {
    margin: -40px 10px 0 10px;
    padding: 20px 25px 10px 25px;
  }
}

.booking_wrapper {
  border-radius: 10px;
  background-color: #89795d;
  background-color: rgba(151, 134, 103, 0.05);
  padding: 20px 20px 0 20px;
}
.booking_wrapper hr {
  border-color: #d9e1e6;
  opacity: 1;
  margin: 0 0 25px 0;
}
.booking_wrapper .form-control::-moz-placeholder {
  color: #333;
}
.booking_wrapper .form-control::placeholder {
  color: #333;
}

.owl-carousel.border-radius .owl-item {
  border-radius: 10px;
  overflow: hidden;
}

/*-------- 2.4 Room details --------*/
.room_facilities_list ul {
  margin: 0 0 25px 0;
  padding: 0;
  list-style: none;
}
.room_facilities_list ul li {
  float: left;
  width: 46%;
  margin: 0 5px;
  padding: 10px 0;
  border-bottom: 1px solid #ededed;
  display: flex;
  align-items: center;
}
.room_facilities_list ul li i {
  font-size: 34px;
  font-size: 2.125rem;
  margin-right: 15px;
  color: #978667;
}

.progress {
  margin-bottom: 0;
  height: 5px;
}

.progress-bar {
  background-color: #978667;
  width: 1px;
  transition: all 0.8s ease;
  transition-delay: 1s;
}

.reviews_sum_details {
  margin-bottom: 25px;
}
.reviews_sum_details h6 {
  font-size: 16px;
  font-size: 1rem;
}
.reviews_sum_details strong {
  position: relative;
  top: -10px;
}

.review_card {
  border-radius: 10px;
  padding: 25px 25px 1px 25px;
  margin-bottom: 30px;
  background-color: #89795d;
  background-color: rgba(151, 134, 103, 0.05);
}
.review_card .user_info {
  text-align: center;
}
@media (max-width: 767px) {
  .review_card .user_info {
    margin-bottom: 10px;
  }
}
.review_card .user_info figure {
  width: 50px;
  height: 50px;
  overflow: hidden;
  border-radius: 50%;
  display: inline-block;
  position: relative;
  margin-bottom: 10px;
}
@media (max-width: 767px) {
  .review_card .user_info figure {
    float: left;
  }
}
.review_card .user_info figure img {
  width: 50px;
  height: auto;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.review_card .user_info h5 {
  font-size: 13px;
  font-size: 0.8125rem;
}
@media (max-width: 767px) {
  .review_card .user_info h5 {
    float: left;
    margin: 18px 0 0 15px;
  }
}
.review_card .review_content h4 {
  font-size: 18px;
  font-size: 1.125rem;
}
.review_card .review_content .rating {
  float: left;
  font-weight: 600;
  font-size: 26px;
  font-size: 1.625rem;
  color: #978667;
}
.review_card .review_content .rating small {
  font-size: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}
.review_card .review_content .rating strong {
  font-size: 13px;
  font-size: 0.8125rem;
  font-weight: 400;
  color: #333;
}
@media (max-width: 575px) {
  .review_card .review_content .rating {
    float: none;
    display: block;
  }
}
.review_card .review_content .rating em {
  font-size: 14px;
  font-size: 0.875rem;
}
@media (max-width: 575px) {
  .review_card .review_content .rating em {
    float: none;
    display: inline-block;
    margin-left: 10px;
  }
}
.review_card .review_content em {
  font-size: 13px;
  font-size: 0.8125rem;
  padding-top: 12px;
  color: #777;
  float: right;
}
@media (max-width: 575px) {
  .review_card .review_content em {
    float: none;
    display: block;
    margin-top: 10px;
    margin-left: 0;
  }
}
.review_card .review_content ul {
  margin: 35px 0 25px 0;
  padding: 0;
}
.review_card .review_content ul li {
  display: inline-block;
  font-size: 13px;
  font-size: 0.8125rem;
  margin-right: 0;
}
@media (max-width: 575px) {
  .review_card .review_content ul li span {
    display: none;
  }
}
.review_card .review_content ul li a {
  background-color: #f0f0f0;
  padding: 7px 10px 5px 10px;
  border-radius: 3px;
  color: #333;
}
.review_card .review_content ul li a:hover {
  background-color: #24262d;
  color: #fff;
}
.review_card .review_content ul li a i {
  margin-right: 5px;
}
@media (max-width: 575px) {
  .review_card .review_content ul li a i {
    margin-right: 0;
  }
}
.review_card .review_content ul li:last-child {
  float: right;
  margin-right: 0;
}
.review_card .reply {
  padding-top: 30px;
}
@media (max-width: 767px) {
  .review_card .reply .user_info {
    display: none;
  }
}
.review_card .reply .user_info figure {
  width: 50px;
  height: 50px;
  overflow: hidden;
  border-radius: 50%;
  display: inline-block;
  position: relative;
  margin-bottom: 10px;
}
.review_card .reply .user_info figure img {
  width: 50px;
  height: auto;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.review_card .reply .user_info h5 {
  font-size: 14px;
  font-size: 0.875rem;
}
.review_card .reply .review_content {
  border-left: 3px solid #ddd;
  padding-left: 20px;
}
@media (max-width: 575px) {
  .review_card .reply .review_content strong {
    display: block;
  }
}
.review_card .reply .review_content em {
  color: #999;
  font-style: italic;
  float: right;
  position: relative;
  top: -10px;
}
@media (max-width: 575px) {
  .review_card .reply .review_content em {
    float: none;
    display: block;
    margin-top: 10px;
  }
}

/* hover effect 
.carousel_item {
    .item {
         img {
                @include transform (scale(1));
                transition: all 2s cubic-bezier(0.25, 0.46, 0.45, 0.94)
            }

            &:hover img {
                @include transform (scale(1.1));
            }
    }
}
*/
/* ------ 2.5 News page ----------- */
a.box_contents {
  margin: 0 10px 45px 10px;
  display: block;
  box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  overflow: hidden;
  background-color: #fff;
}
a.box_contents .wrapper {
  padding: 20px 40px 50px 60px;
  position: relative;
}
a.box_contents figure {
  position: relative;
  overflow: hidden;
  transition: all 0.7s ease;
}
a.box_contents figure img {
  transition: all 1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
a.box_contents figure em {
  position: absolute;
  z-index: 4;
  bottom: 0;
  left: 10px;
  background-color: #24262d;
  color: #fff;
  padding: 10px 15px;
  line-height: 1;
  text-transform: uppercase;
  font-style: normal;
  font-size: 12px;
  font-size: 0.75rem;
}
a.box_contents small {
  font-weight: 600;
  color: #ddd;
  text-transform: uppercase;
  position: absolute;
  top: 5px;
  left: 40px;
  display: block;
  width: 150%;
  transform: rotate(90deg);
  transform-origin: 0 0;
  white-space: nowrap;
  position: absolute;
  letter-spacing: 2px;
}
a.box_contents small:after {
  background-color: #eee;
  width: 100%;
  height: 1px;
  content: "";
  display: inline-block;
  margin: 4px;
}
a.box_contents h2 {
  font-size: 24px;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 30px;
}
a.box_contents em {
  font-style: normal;
  font-weight: 600;
  position: relative;
}
a.box_contents em:before {
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 10px;
  height: 2px;
  content: "";
  transition: width 500ms ease;
  background-color: #978667;
}

a.box_contents:hover em:before {
  width: 100%;
  background-color: #333;
}

a.box_contents:hover img {
  transform: scale(1.2);
}

.home a.box_contents {
  margin: 0 0 25px 0;
}

#comments {
  margin-bottom: 25px;
}
#comments ul {
  padding: 0;
  margin: 0;
  list-style: none;
}
#comments ul li {
  padding: 25px 0 0 0;
  list-style: none;
}
#comments .replied-to {
  margin-left: 35px;
}
@media (max-width: 767px) {
  #comments .replied-to {
    margin-left: 20px;
  }
}

.avatar {
  float: left;
  margin-right: 25px;
  width: 68px;
  height: 68px;
  overflow: hidden;
  border-radius: 50%;
  position: relative;
}
@media (max-width: 767px) {
  .avatar {
    float: none;
    margin: 0 0 5px 0;
  }
}
.avatar img {
  width: 68px;
  height: auto;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.comment_right {
  display: table;
  background-color: #f7f7f7;
  border-radius: 3px;
  padding: 20px 20px 0 20px;
  position: relative;
}

.comment_right:after,
.comment_right:before {
  right: 100%;
  top: 30px;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
  border-radius: 3px;
}

.comment_right:after {
  background-color: rgba(0, 0, 0, 0);
  border-right-color: #f7f7f7;
  border-width: 15px;
  margin-top: -15px;
}

.comment_right:before {
  border-color: rgba(0, 0, 0, 0);
  border-width: 16px;
  margin-top: -16px;
}

.comment_info {
  padding-bottom: 7px;
}
.comment_info span {
  padding: 0 10px;
}

/*-------- 2.6 Contact page --------*/
.contact_info {
  background-color: white;
  background-color: white;
  border-radius: 10px;
  padding: 30px 45px 20px 45px;
  margin-bottom: 25px;
  box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.05);
}
.contact_info ul {
  list-style: none;
  padding: 0;
  margin: 0 0 0 0;
}
.contact_info ul li {
  margin-bottom: 25px;
}
.contact_info ul li i {
  font-size: 36px;
  font-size: 2.25rem;
  color: #978667;
}
.contact_info ul li h4 {
  font-size: 21px;
  font-size: 1.3125rem;
}
.contact_info ul li small {
  font-style: oblique;
  opacity: 0.8;
}

.map_contact iframe {
  width: 100%;
  filter: grayscale(85%);
  height: 450px;
  margin-bottom: -10px;
}

/*-------- 2.7 Error page --------*/
.error_page {
  height: 100vh;
}
.error_page h1 {
  font-size: 90px;
  font-size: 5.625rem;
}

/*-------- 2.8 Gallery --------*/
.item-img {
  position: relative;
  border-radius: 10px;
  overflow: hidden;
}
.item-img img {
  max-width: 100%;
}
.item-img .content {
  position: absolute;
  text-align: center;
  height: 0;
  width: 0;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.65);
  z-index: 1;
  display: flex;
  justify-content: center;
  flex-direction: column;
  height: 100%;
  width: 100%;
  transition: 0.2s ease-in-out;
  opacity: 0;
}
.item-img .content a {
  opacity: 0;
  transition: 0.2s ease;
  font-size: 24px;
  width: 100%;
  height: 100%;
  display: block;
  display: flex;
  align-items: center;
  justify-content: center;
}

.item:hover .content {
  height: calc(100% - 30px);
  width: calc(100% - 30px);
  opacity: 1;
}

.item:hover .content a {
  opacity: 1;
  color: #fff;
}

/*-------- 2.9 Restaurant --------*/
.pattern_3 {
  background: #fff url(../img/restaurant/pattern_menu.jpg) no-repeat center center;
  background-size: cover;
  background-attachment: scroll;
}
@media (max-width: 767px) {
  .pattern_3 {
    background: #fff;
  }
}

.banner {
  background-position: center center;
  background-repeat: no-repeat;
  border-radius: 5px;
  overflow: hidden;
  background-size: cover;
  width: 100%;
  height: 350px;
  position: relative;
  margin-bottom: 60px;
}
.banner .wrapper {
  color: #fff;
  padding: 60px;
}
@media (max-width: 767px) {
  .banner .wrapper {
    padding: 30px;
  }
}
.banner .wrapper small {
  text-transform: uppercase;
  font-size: 14px;
  font-size: 0.875rem;
  font-weight: 500;
}
.banner .wrapper h3 {
  color: #fff;
  font-size: 36px;
  font-size: 2.25rem;
  margin: 0;
}
.banner .wrapper p {
  font-size: 18px;
  font-size: 1.125rem;
  font-family: "Lora", serif;
}

.menu_item {
  position: relative;
  padding: 20px 0 0 90px;
  margin-bottom: 30px;
  min-height: 80px;
}
.menu_item figure {
  position: absolute;
  left: 0;
  top: 0;
  width: 80px;
  height: 80px;
  overflow: hidden;
  border-radius: 50%;
}
.menu_item figure img {
  width: 80px;
  height: auto;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) scale(1);
}
.menu_item .menu_title {
  background: radial-gradient(#d5d5d5 40%, transparent 10%);
  background-position: 0 13px;
  background-size: 4px 4px;
  background-repeat: repeat-x;
}
.menu_item .menu_title h3 {
  background-color: #fff;
  font-size: 18px;
  font-size: 1.125rem;
  display: inline-block;
  margin: 0;
  padding-right: 3px;
}
.menu_item .menu_title em {
  float: right;
  font-weight: 600;
  background-color: #fff;
  font-style: normal;
  padding-left: 3px;
}
.menu_item p {
  font-family: "Lora", serif;
  margin-bottom: 0;
}

.menu_item figure a:hover img {
  transition: all 0.3s ease;
  transform: translate(-50%, -50%) scale(1.1);
}

.menu_item .menu_title h3,
.menu_item .menu_title em {
  background-color: #faf8f5;
}

.tabs_menu {
  margin-top: 15px;
}
.tabs_menu .nav-link {
  display: block;
  padding: 0 15px;
  height: 40px;
  font-size: 15px;
  font-size: 0.9375rem;
  color: #333;
  border: none;
  font-weight: 600;
}
.tabs_menu .nav-link:hover,
.tabs_menu .nav-link:focus {
  text-decoration: none;
  color: #978667;
}
.tabs_menu .nav-tabs .nav-link.disabled {
  color: #6c757d;
  background-color: transparent;
  border-color: transparent;
}
.tabs_menu .nav-tabs .nav-link.active,
.tabs_menu .nav-tabs .nav-item.show .nav-link {
  border-bottom: 2px solid #24262d;
  color: #24262d;
  background-color: transparent;
}
.tabs_menu .nav-tabs {
  border: none;
  border-bottom: 2px solid #ededed;
  display: flex;
  justify-content: center;
}
@media (max-width: 767px) {
  .tabs_menu .nav-tabs {
    display: none;
  }
}
.tabs_menu .nav-tabs .nav-item {
  text-transform: uppercase;
  font-weight: 500;
}
.tabs_menu .tab-content .card {
  border: none;
  background-color: #faf8f5;
  padding: 0 45px;
}
@media (max-width: 991px) {
  .tabs_menu .tab-content .card {
    padding: 0;
  }
}
.tabs_menu .tab-content .card .card-header {
  padding: 0;
  background-color: transparent;
  border: 0;
  background-color: #faf8f5;
}
.tabs_menu .tab-content .card .card-header a {
  display: block;
  font-size: 18px;
  font-size: 1.125rem;
  padding: 15px;
  color: #333;
}
.tabs_menu .tab-content .card .card-body {
  padding: 45px 0 20px 0;
}
@media (max-width: 991px) {
  .tabs_menu .tab-content .card .card-body {
    padding: 25px 15px 5px 15px;
  }
}
@media (max-width: 991px) {
  .tabs_menu .tab-content .card .card-body.reviews .add_bottom_45 {
    padding-bottom: 25px;
  }
}
@media (min-width: 768px) {
  .tabs_menu .tab-content .card {
    border: none;
  }
  .tabs_menu .tab-content .card .card-header {
    display: none;
  }
  .tabs_menu .tab-content .card .collapse {
    display: block;
  }
}
@media (max-width: 767px) {
  .tabs_menu .tab-content {
    margin-bottom: 25px;
  }
  .tabs_menu .tab-content .card-header a:after {
    font-family: "bootstrap-icons";
    width: 20px;
    display: block;
    float: right;
  }
  .tabs_menu .tab-content .card-header a.collapsed:after {
    content: "\f64d";
  }
  .tabs_menu .tab-content .card-header a:after {
    content: "\f63b";
  }
  .tabs_menu .tab-content .fade:not(.show) {
    opacity: 1;
  }
  .tabs_menu .tab-content .tab-pane {
    display: block !important;
    opacity: 1;
    margin-top: 10px;
  }
}

/*-------- 2.10 Advertise modal  --------*/
.cookie_popup {
  display: none !important;
}

.popup_wrapper {
  background: rgba(0, 0, 0, 0.7) none repeat scroll 0 0;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  transition: all 0.5s ease;
  width: 100%;
  z-index: 9999999;
  opacity: 0;
}
.popup_wrapper .popup_content {
  background-color: #fff;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  position: absolute;
  width: 500px;
  box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.35);
}
@media (max-width: 767px) {
  .popup_wrapper .popup_content {
    width: 300px;
  }
}
.popup_wrapper .popup_content .content {
  height: 100%;
  position: relative;
}
.popup_wrapper .popup_content.newsletter_c {
  width: 800px;
}
@media (max-width: 767px) {
  .popup_wrapper .popup_content.newsletter_c {
    width: 300px;
  }
}
.popup_wrapper .popup_content.newsletter_c figure {
  position: absolute;
  overflow: hidden;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0;
}
.popup_wrapper .popup_content.newsletter_c figure img {
  height: 100%;
  width: auto;
}
.popup_wrapper .popup_content.newsletter_c .content {
  height: 100%;
  padding: 120px 60px;
  text-align: center;
  display: flex;
  align-items: center;
  background-color: #fff;
}
@media (max-width: 767px) {
  .popup_wrapper .popup_content.newsletter_c .content {
    padding: 30px 30px 15px 30px;
    height: auto;
  }
}
.popup_wrapper .popup_content.newsletter_c .content .wrapper {
  width: 100%;
}
.popup_wrapper .popup_content.newsletter_c .content .wrapper h3 {
  font-size: 21px;
  font-size: 1.3125rem;
  margin-top: 25px;
}
.popup_wrapper .popup_close {
  width: 25px;
  height: 25px;
  position: absolute;
  top: 10px;
  right: 15px;
  z-index: 9999;
  font-size: 28px;
  font-size: 1.75rem;
  color: #555;
  cursor: pointer;
}
.popup_wrapper .popup_close:hover {
  color: #222;
}

/*-------- 2.11 Cookie bar --------*/
#cookie-bar {
  background: rgba(0, 0, 0, 0.8);
  height: auto;
  line-height: 24px;
  color: #eeeeee;
  text-align: center;
  padding: 15px 0;
  font-weight: 400;
  font-size: 13px;
  font-size: 0.8125rem;
  font-weight: 500;
}
#cookie-bar p {
  margin: 0;
  padding: 0;
}
#cookie-bar a {
  color: #fff;
  display: inline-block;
  border-radius: 3px;
  text-decoration: none;
  padding: 2px 10px;
  margin-left: 8px;
}
@media (max-width: 767px) {
  #cookie-bar a {
    display: block;
    max-width: 150px;
    margin: auto;
    margin-bottom: 5px;
  }
}
#cookie-bar .cb-enable {
  background: #978667;
}
#cookie-bar .cb-enable:hover {
  background: #fff;
  color: #292b33;
}
#cookie-bar .cb-disable {
  background: #990000;
}
#cookie-bar .cb-disable:hover {
  background: #bb0000;
}
#cookie-bar .cb-policy {
  background: #24262d;
}
#cookie-bar .cb-policy:hover {
  background: #fff;
  color: #292b33;
}
#cookie-bar.fixed {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 999999;
}
#cookie-bar.fixed.bottom {
  bottom: 0;
  top: auto;
}
@media (max-width: 767px) {
  #cookie-bar {
    padding: 5px 0;
  }
}

/*-------- 2.12 Home Categories Hover effect --------*/
.hero.cat_hover {
  background: rgba(0, 0, 0, 0.5);
  position: relative;
  padding: 0 0 30px 15px;
}
.hero.cat_hover .cat_nav_hover ul {
  list-style: none;
  padding: 0;
  margin: 25px 0 0 0;
  display: flex;
}
@media (max-width: 767px) {
  .hero.cat_hover .cat_nav_hover ul {
    overflow-x: scroll;
    justify-content: start;
  }
}
.hero.cat_hover .cat_nav_hover ul li {
  padding: 0 10px 0 0;
  width: calc(30% - 10px);
  min-width: 300px;
  text-align: left;
}
.hero.cat_hover .cat_nav_hover ul li a {
  display: block;
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
  background-color: #f2f2f2;
  background-color: rgba(255, 255, 255, 0);
  min-height: 135px;
  position: relative;
  overflow: hidden;
}
.hero.cat_hover .cat_nav_hover ul li a .info {
  position: absolute;
  width: 100%;
  z-index: 9;
  display: block;
  padding: 25% 30px 0 30px;
  color: #fff;
  left: 0;
  bottom: -75px;
  padding-bottom: 60px;
  transform: translate(0, 0);
  transition: all 0.4s ease;
}
.hero.cat_hover .cat_nav_hover ul li a .info h2 {
  color: #fff;
  font-size: 21px;
  font-size: 1.3125rem;
  font-weight: 600;
  margin-bottom: 0;
}
.hero.cat_hover .cat_nav_hover ul li a .info span {
  font-weight: 500;
  opacity: 0;
}
.hero.cat_hover .cat_nav_hover ul li a:hover .info {
  transform: translate(0, -35px);
}
.hero.cat_hover .cat_nav_hover ul li a:hover .info span {
  opacity: 1;
}
.hero.cat_hover .image-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  opacity: 0;
  visibility: hidden;
  transition: all 1s ease-in-out;
}
.hero.cat_hover .image-bg img, .hero.cat_hover .image-bg video {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: 50% 50%;
     object-position: 50% 50%;
}
.hero.cat_hover .container-item.active a {
  background-color: #f4f4f4;
  background-color: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
}
.hero.cat_hover .container-item.active a + .image-bg {
  opacity: 1;
  visibility: visible;
  overflow: hidden;
  width: 100%;
  height: 100%;
}

/*-------- 2.13 Home Booking form on top --------*/
@media (max-width: 767px) {
  .hero.with-form {
    height: auto;
    min-height: 1000px;
  }
}

.form_wp {
  background-color: black;
  background-color: rgba(0, 0, 0, 0.4);
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
  border-radius: 10px;
  padding: 30px;
}
.form_wp .form-control, .form_wp .nice-select {
  border: none;
}
@media (max-width: 991px) {
  .form_wp {
    margin-top: 60px;
    border-radius: 0;
  }
}

.form_wp #success_page h5 {
  color: white !important;
}

/*============================================================================================*/
/* 3.  COMMON */
/*============================================================================================*/
/*-------- 3.1 Misc --------*/
.custom_badge {
  line-height: 1;
  text-transform: uppercase;
  font-weight: 700;
  color: #fff;
  margin-left: 5px;
  display: inline-block;
  border-radius: 3px;
  background-color: red;
  font-size: 11px;
  font-size: 0.6875rem;
  padding: 3px 5px;
  position: relative;
  top: -1px;
}

/* Carousel Centered */
.carousel_item_centered .owl-item,
.carousel_item_centered_rooms .owl-item {
  opacity: 0.5;
  transform: scale(0.85) !important;
  backface-visibility: hidden;
  -webkit-transform: translateZ(0) scale(0.85, 0.85) !important;
  transition: all 0.3s ease-in-out 0s;
  overflow: hidden;
}

.carousel_item_centered .owl-item.active.center,
.carousel_item_centered_rooms .owl-item.active.center {
  opacity: 1;
  backface-visibility: hidden;
  transform: scale(1) !important;
}

/* Marquee */
.marquee {
  position: relative;
  width: 100vw;
  max-width: 100%;
  height: 100px;
  overflow: hidden;
  font-size: 120px;
  font-weight: bold;
  margin-top: -60px;
}
.marquee .track {
  position: absolute;
  white-space: nowrap;
  will-change: transform;
  animation: marquee 140s linear infinite;
}
.marquee .content {
  opacity: 0.05;
  line-height: 1.2;
}

@keyframes marquee {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-30%);
  }
}
.modal-content {
  border: none !important;
  border-radius: 0 !important;
  padding: 25px;
}

.list_ok ul {
  list-style: none;
  padding: 0;
  margin: 0 0 25px 0;
}
.list_ok ul li {
  padding-left: 45px;
  position: relative;
}
.list_ok ul li strong {
  color: #333;
}
.list_ok ul li:before {
  color: #978667;
  position: absolute;
  left: 0;
  top: -7px;
  font-family: "bootstrap-icons";
  content: "\f19f";
  font-size: 32px;
  font-size: 2rem;
}

/* Pinned content */
.pinned-image {
  display: flex;
  height: 90vh;
  justify-content: center;
  position: relative;
  overflow: hidden;
}
.pinned-image.rounded_container {
  border-radius: 15px;
}

.pinned-image--medium {
  height: 75vh;
}

.pinned-image--small {
  height: 45vh;
}

.pinned-image__container {
  position: relative;
  transform: scale(0.8);
  width: 100%;
  border-radius: 15px;
  overflow: hidden;
}

.pinned-image__container-overlay {
  background: rgba(0, 0, 0, 0.6);
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
}

.pinned-image__container img,
.pinned-image__container video,
.pinned-image__container {
  height: 100%;
  left: 0;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
  position: absolute;
  top: 0;
  width: 100%;
  background-color: #ccc;
}

.pinned_over_content {
  text-align: center;
  padding: 0 60px;
  width: 100%;
  left: 50%;
  position: absolute;
  text-align: center;
  top: 50%;
  transform: translate3d(-50%, -50%, 0);
}
@media (max-width: 767px) {
  .pinned_over_content {
    padding: 0 15px;
  }
}

.pinned_over_content h2 {
  color: #fff;
  font-size: 52px;
  font-size: 3.25rem;
  font-weight: 700;
  letter-spacing: -0.03em;
  line-height: 1.2;
}

.parallax_wrapper {
  position: relative;
  margin-bottom: 25px;
}
@media (max-width: 991px) {
  .parallax_wrapper {
    padding-left: 25%;
  }
}
.parallax_wrapper .img_over {
  left: -25%;
  bottom: 10%;
  position: absolute;
  z-index: 99;
}
@media (max-width: 991px) {
  .parallax_wrapper .img_over {
    left: 0;
  }
}
.parallax_wrapper .img_over img {
  border: 4px solid #fff;
  width: 50%;
  height: auto;
  box-shadow: 10px 10px 26px 0px rgba(0, 0, 0, 0.21);
}
@media (max-width: 991px) {
  .parallax_wrapper.inverted {
    padding-left: inherit;
    padding-right: 25%;
  }
}
.parallax_wrapper.inverted .img_over {
  text-align: right;
  left: inherit;
  right: -25%;
}
@media (max-width: 991px) {
  .parallax_wrapper.inverted .img_over {
    left: inherit;
    right: 0;
  }
}

img.rounded-img {
  border-radius: 10px;
}

.bg_gray {
  background-color: #f4f4f4;
}

.bg_color_1 {
  background-color: #4b514d;
}

.bg_white {
  background-color: #fff;
}

.pattern {
  background: url(../img/pattern.png) top left no-repeat;
  background-attachment: fixed;
}

.bg_gray.pattern {
  background: #f4f4f4 url(../img/pattern.png) top left no-repeat;
}
.bg_gray.pattern.bg_fixed {
  background-attachment: fixed;
}

.pattern_2 {
  background: #fff url(../img/pattern_2.png) top right no-repeat;
}
@media (max-width: 767px) {
  .pattern_2 {
    background-size: 80% auto;
  }
}

/*-------- 3.3 Forms --------*/
#message-newsletter {
  font-size: 14px;
  font-size: 0.875rem;
  padding-bottom: 5px;
}

.error_message {
  color: red;
}

.form-group {
  margin-bottom: 25px;
}
.form-group label {
  font-weight: 500;
  margin-bottom: 3px;
}
.form-group.with-icon {
  position: relative;
  margin-bottom: 0;
}
.form-group.with-icon i {
  font-size: 21px;
  font-size: 1.3125rem;
  position: absolute;
  top: 8px;
  right: 15px;
  color: #978667;
}

.form-control {
  border-color: #ededed;
  font-size: 14px;
  font-size: 0.875rem;
  color: #333;
  height: 50px;
}
.form-control:focus {
  border-color: #ddd;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0);
}

textarea.form-control {
  height: 130px !important;
}

.nice-select {
  border-radius: 0.375rem !important;
}

/*-------- 3.5 Progess Scroll to top --------*/
.progress-wrap {
  position: fixed;
  right: 30px;
  bottom: 30px;
  height: 46px;
  width: 46px;
  cursor: pointer;
  display: block;
  border-radius: 50px;
  z-index: 999;
  opacity: 0;
  display: none;
  visibility: hidden;
  transform: translateY(15px);
  transition: all 200ms linear;
  box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.2);
}
@media (max-width: 991px) {
  .progress-wrap {
    display: none !important;
  }
}
.progress-wrap.active-progress {
  display: block;
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}
.progress-wrap::after {
  position: absolute;
  font-family: "bootstrap-icons";
  content: "\f145";
  text-align: center;
  line-height: 46px;
  font-size: 24px;
  color: #999;
  left: 0;
  top: 0;
  height: 46px;
  width: 46px;
  cursor: pointer;
  display: block;
  z-index: 1;
  transition: all 200ms linear;
}
.progress-wrap:hover::after {
  color: #978667;
}
.progress-wrap svg path {
  fill: none;
}
.progress-wrap svg.progress-circle path {
  stroke: #978667;
  stroke-width: 4;
  box-sizing: border-box;
  transition: all 200ms linear;
}

/*-------- 3.6 Spacing --------*/
.margin_120_95 {
  padding-top: 120px;
  padding-bottom: 95px;
}
@media (max-width: 767px) {
  .margin_120_95 {
    padding-top: 90px;
    padding-bottom: 65px;
  }
}

.margin_120_0 {
  padding-top: 120px;
}

.margin_120 {
  padding-top: 120px;
  padding-bottom: 120px;
}

.margin_60_35 {
  padding-top: 60px;
  padding-bottom: 35px;
}
@media (max-width: 575px) {
  .margin_60_35 {
    padding-top: 30px;
    padding-bottom: 5px;
  }
}

.margin_80 {
  padding-top: 80px;
  padding-bottom: 80px;
}
@media (max-width: 991px) {
  .margin_80 {
    padding-bottom: 60px;
    padding-top: 60px;
  }
}

.margin_80_55 {
  padding-top: 80px;
  padding-bottom: 55px;
}
@media (max-width: 991px) {
  .margin_80_55 {
    padding-top: 60px;
    padding-bottom: 35px;
  }
}

.margin_80_0 {
  padding-top: 80px;
}
@media (max-width: 991px) {
  .margin_80_0 {
    padding-top: 60px;
  }
}
@media (max-width: 575px) {
  .margin_80_0 {
    padding-top: 45px;
  }
}

.margin_60_0 {
  padding-top: 60px;
}
@media (max-width: 991px) {
  .margin_60_0 {
    padding-top: 60px;
  }
}
@media (max-width: 575px) {
  .margin_60_0 {
    padding-top: 45px;
  }
}

.add_bottom_60 {
  padding-bottom: 60px;
}

.add_bottom_120 {
  padding-bottom: 120px;
}
@media (max-width: 767px) {
  .add_bottom_120 {
    padding-bottom: 90px;
  }
}

.add_bottom_90 {
  padding-bottom: 90px;
}
@media (max-width: 767px) {
  .add_bottom_90 {
    padding-bottom: 60px;
  }
}

/*-------- 3.7 Pagination --------*/
.clear:after, .pagination:after, .clear:before, .pagination:before {
  content: "";
  display: table;
}
.clear:after, .pagination:after {
  clear: both;
}

.pagination__wrapper {
  height: 50px;
  padding-top: 30px;
  margin-bottom: 30px;
  position: relative;
  text-align: center;
  width: 100%;
}
.pagination__wrapper:before {
  top: -1px;
}
.pagination__wrapper:after {
  bottom: -1px;
}

@keyframes hoverAnimation {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.pagination {
  display: inline-block;
  list-style: none;
  margin: 0;
  padding: 0;
}
.pagination li {
  display: block;
  float: left;
  padding: 5px;
}
.pagination li:first-child {
  border: none;
}
.pagination a,
.pagination span {
  background: none;
  border: none;
  border-radius: 50%;
  box-sizing: border-box;
  color: rgba(0, 0, 0, 0.6);
  display: block;
  font-size: 16px;
  height: 40px;
  line-height: 40px;
  min-width: 40px;
  padding: 0;
}
.pagination a {
  outline: none;
  position: relative;
  transition: all 170ms linear;
}
.pagination a:before {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 50%;
  content: "";
  cursor: pointer;
  height: 0;
  left: 50%;
  opacity: 0;
  position: absolute;
  transform: translate(-50%, -50%);
  transition: all 270ms linear;
  top: 50%;
  width: 0;
}
.pagination a:hover:not(.active) {
  color: rgb(0, 0, 0);
}
.pagination a:hover:not(.active):before {
  animation: hoverAnimation 810ms linear forwards;
  height: 40px;
  width: 40px;
}
.pagination a.active {
  background: #24262d;
  color: rgb(255, 255, 255);
  cursor: default;
}
.pagination .prev,
.pagination .next {
  font-size: 28px;
}

/*-------- 3.8 Success submit --------*/
#success_page {
  padding: 30px 0;
  text-align: center;
  font-size: 24px;
  font-size: 1.5rem;
}
#success_page h5 {
  margin-top: 15px;
}
#success_page h5 span {
  display: block;
  margin-top: 7px;
  font-size: 16px;
}

#confirm {
  text-align: center;
  padding: 60px 15px;
}
@keyframes checkmark {
  0% {
    stroke-dashoffset: 50px;
  }
  100% {
    stroke-dashoffset: 0;
  }
}
@keyframes checkmark-circle {
  0% {
    stroke-dashoffset: 240px;
  }
  100% {
    stroke-dashoffset: 480px;
  }
}
.inlinesvg .svg svg {
  display: inline;
}

.icon--order-success svg path {
  animation: checkmark 0.25s ease-in-out 0.7s backwards;
}

.icon--order-success svg circle {
  animation: checkmark-circle 0.6s ease-in-out backwards;
}