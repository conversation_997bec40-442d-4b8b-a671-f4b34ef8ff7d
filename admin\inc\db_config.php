<?php
$hname = 'localhost';
$uname = 'u779745282_ebitare';
$pass = 'Adminstrat1.';
$db = 'u779745282_ebitare';

$con = mysqli_connect($hname, $uname, $pass, $db);

if (!$con) {
    die("Cannot connect to database: " . mysqli_connect_error());
}

function filteration($data) {
    foreach ($data as $key => $value) {
        $value = trim($value);           // Remove spaces
        $value = stripslashes($value);   // Remove backslashes
        $value = strip_tags($value);     // Remove HTML tags
        $value = htmlspecialchars($value); // Prevent XSS attacks        
        $data[$key] = $value;
    }

    return $data;
}

function selectALL($table)
{
    $con = $GLOBALS['con'];
    $res = mysqli_query($con, "SELECT * FROM $table");
    return $res;
}

function select($sql, $values, $datatypes)
 {
     $con = $GLOBALS['con'];  // Access the global $con variable
    
    // Prepare the query
    if ($stmt = mysqli_prepare($con, $sql)) {
             mysqli_stmt_bind_param($stmt, $datatypes, ...$values);
         // Execute the query
        if (mysqli_stmt_execute($stmt)) {
            // Get the result of the query
            $res = mysqli_stmt_get_result($stmt);
            mysqli_stmt_close($stmt);

            return $res;  // Return result
         }

       
        else {
            mysqli_stmt_close($stmt);
            die("Query cannot be executed - Select");
        }
    } 
        else {
            // SQL query preparation failed
            die("Query cannot be prepared - Select");
        }
}
function update($sql, $values, $datatypes)
 {
     $con = $GLOBALS['con'];  // Access the global $con variable
    
    // Prepare the query
    if ($stmt = mysqli_prepare($con, $sql)) {
             mysqli_stmt_bind_param($stmt, $datatypes, ...$values);
         // Execute the query
        if (mysqli_stmt_execute($stmt)) {
            // Get the result of the query
            $res = mysqli_stmt_affected_rows($stmt);
            mysqli_stmt_close($stmt);

            return $res;  // Return result
         }

       
        else {
            mysqli_stmt_close($stmt);
            die("Query cannot be executed - Update");
        }
    } 
        else {
            // SQL query preparation failed
            die("Query cannot be prepared - Update");
        }
}

function insert($sql, $values, $datatypes)
 {
     $con = $GLOBALS['con'];  // Access the global $con variable
    
    // Prepare the query
    if ($stmt = mysqli_prepare($con, $sql)) {
             mysqli_stmt_bind_param($stmt, $datatypes, ...$values);
         // Execute the query
        if (mysqli_stmt_execute($stmt)) {
            // Get the result of the query
            $res = mysqli_stmt_affected_rows($stmt);
            mysqli_stmt_close($stmt);

            return $res;  // Return result
         }

       
        else {
            mysqli_stmt_close($stmt);
            die("Query cannot be executed - Insert");
        }
    } 
        else {
            // SQL query preparation failed
            die("Query cannot be prepared - Insert");
        }
}

function delete($sql, $values, $datatypes)
 {
     $con = $GLOBALS['con'];  // Access the global $con variable
    
    // Prepare the query
    if ($stmt = mysqli_prepare($con, $sql)) {
             mysqli_stmt_bind_param($stmt, $datatypes, ...$values);
         // Execute the query
        if (mysqli_stmt_execute($stmt)) {
            // Get the result of the query
            $res = mysqli_stmt_affected_rows($stmt);
            mysqli_stmt_close($stmt);

            return $res;  // Return result
         }

       
        else {
            mysqli_stmt_close($stmt);
            die("Query cannot be executed - Delete");
        }
    } 
        else {
            // SQL query preparation failed
            die("Query cannot be prepared - Delete");
        }
}
?>
