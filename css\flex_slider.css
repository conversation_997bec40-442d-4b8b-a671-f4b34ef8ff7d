/* ------ j<PERSON><PERSON><PERSON> v2.7.0 ----------- */
/* Resets */
.flex-container a:hover,
.flex-slider a:hover {
  outline: none;
}

.slides,
.slides > li,
.flex-control-nav,
.flex-direction-nav {
  margin: 0;
  padding: 0;
  list-style: none;
}

.flex-pauseplay span {
  text-transform: capitalize;
}

/* Base style */
.flexslider {
  margin: 0;
  padding: 0;
}

.flexslider .slides > li {
  display: none;
  -webkit-backface-visibility: hidden;
}

.flexslider .slides img {
  width: 100%;
  display: block;
}

.flexslider .slides:after {
  content: " ";
  display: block;
  clear: both;
  visibility: hidden;
  line-height: 0;
  height: 0;
}

html[xmlns] .flexslider .slides {
  display: block;
}

* html .flexslider .slides {
  height: 1%;
}

.no-js .flexslider .slides > li:first-child {
  display: block;
}

/* Default theme */
.flexslider {
  margin: 0;
  background: #111;
  position: relative;
  zoom: 1;
}

.flexslider .slides {
  zoom: 1;
}

.flexslider .slides img {
  height: auto;
  -moz-user-select: none;
}

.flex-viewport {
  max-height: 2000px;
  transition: all 1s ease;
}

.loading .flex-viewport {
  max-height: 300px;
}

.carousel li {
  margin-right: 5px;
}

.flex-direction-nav {
  position: absolute;
  bottom: 50px;
  height: 30px;
  line-height: 1;
  right: 0;
  bottom: 0;
  width: 100%;
  z-index: 9999;
}

.flex-direction-nav a {
  text-decoration: none;
  display: block !important;
  width: 45px;
  height: 52px;
  position: absolute;
  top: -65px;
  z-index: 10;
  overflow: hidden;
  opacity: 0;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease-in-out;
}

.flex-direction-nav a:before {
  font-family: "bootstrap-icons";
  font-size: 46px;
  display: inline-block;
  content: "\f284";
  color: rgb(255, 255, 255);
}

.flex-direction-nav a.flex-next:before {
  content: "\f285";
}

.flex-direction-nav .flex-next {
  text-align: right;
}

#carousel_slider .flex-direction-nav .flex-prev {
  opacity: 0.8;
  left: 15px;
}

#carousel_slider .flex-direction-nav .flex-prev:hover {
  opacity: 1;
}

#carousel_slider .flex-direction-nav .flex-next {
  opacity: 0.8;
  right: 15px;
}

#carousel_slider .flex-direction-nav .flex-next:hover {
  opacity: 1;
}

#carousel_slider .flex-direction-nav .flex-disabled {
  opacity: 0 !important;
  filter: alpha(opacity=0);
  cursor: default;
  z-index: -1;
}

.flex-control-nav {
  width: 100%;
  position: absolute;
  bottom: -40px;
  text-align: center;
}

.flex-control-nav li {
  margin: 0 6px;
  display: inline-block;
  zoom: 1;
  *display: inline;
}

/* Custom theme */
#slider.flexslider li {
  position: relative;
}

#slider.flexslider li .meta {
  position: absolute;
  bottom: 35%;
  left: 60px;
  color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  font-weight: 400;
  z-index: 99;
  padding-right: 45px;
}
@media (max-width: 767px) {
  #slider.flexslider li .meta {
    left: 15px;
    bottom: auto;
    top: 50%;
    bottom: auto;
  }
}

#slider.flexslider li h3 {
  font-size: 46px;
  font-size: 2.875rem;
  margin-bottom: 0;
  color: #fff;
}
@media (max-width: 767px) {
  #slider.flexslider li h3 {
    font-size: 32px;
    font-size: 2rem;
  }
}

#slider.flexslider li .info {
  display: flex;
  flex-direction: row;
}

#slider.flexslider li .info p {
  margin-bottom: 25px;
  font-size: 21px;
  font-size: 1.3125rem;
}
@media (max-width: 767px) {
  #slider.flexslider li .info p {
    font-size: 18px;
    font-size: 1.125rem;
  }
}

#slider.flexslider li h3,
#slider.flexslider li .info p {
  animation-duration: 0.6s;
  animation-fill-mode: both;
  animation-name: fadeOut;
}

#slider.flexslider li .meta a {
  animation-duration: 0.3s;
  animation-fill-mode: both;
  animation-name: zoomOut;
  padding: 12px 20px;
}

#slider.flexslider li.flex-active-slide .meta h3,
#slider.flexslider li.flex-active-slide .meta .info p {
  animation-delay: 0.4s;
  animation-duration: 0.6s;
  animation-fill-mode: both;
  animation-name: slideInUp;
}

#slider.flexslider li.flex-active-slide .meta a {
  animation-delay: 1s;
  animation-duration: 0.3s;
  animation-fill-mode: both;
  animation-name: zoomIn;
}

#slider.flexslider li.flex-active-slide .meta .info p {
  animation-delay: 0.6s;
}

.slider {
  position: relative;
}

#slider.flexslider {
  overflow: hidden;
}
#slider.flexslider .flex-direction-nav {
  opacity: 0;
  display: none;
}
#slider.flexslider ul.slides li {
  height: 80vh;
  background-color: #000;
}
@media (max-width: 1199px) {
  #slider.flexslider ul.slides li {
    height: 650px !important;
  }
}
@media (max-width: 767px) {
  #slider.flexslider ul.slides li {
    height: 500px !important;
  }
}
#slider.flexslider ul.slides li img {
  opacity: 0.5;
  width: 100%;
  height: auto;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
@media (max-width: 1199px) {
  #slider.flexslider ul.slides li img {
    height: 650px !important;
    width: auto;
  }
}
@media (max-width: 767px) {
  #slider.flexslider ul.slides li img {
    height: 500px !important;
    width: auto;
  }
}

#icon_drag_mobile {
  position: absolute;
  right: 20px;
  bottom: 20px;
  background: url(../img/drag_icon.svg) 0 0 no-repeat;
  width: 50px;
  height: 30px;
  opacity: 0.6;
  z-index: 99;
  display: none;
}
@media (max-width: 767px) {
  #icon_drag_mobile {
    display: block;
  }
}

#carousel_slider_wp {
  background: transparent;
  background: linear-gradient(to bottom, transparent, #000);
  position: absolute;
  bottom: 0;
  z-index: 9;
  width: 100%;
  padding: 0 60px 45px 60px;
}
@media (max-width: 767px) {
  #carousel_slider_wp {
    display: none;
  }
}

#carousel_slider.flexslider {
  overflow: hidden;
  background-color: transparent;
}
#carousel_slider.flexslider ul.slides {
  padding-top: 20px;
}
#carousel_slider.flexslider ul.slides li {
  position: relative;
  border-radius: 10px;
  background-color: #000;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  cursor: pointer;
}
#carousel_slider.flexslider ul.slides li:hover img {
  transform: scale(1.1);
}
#carousel_slider.flexslider ul.slides li img {
  transform: scale(1);
  opacity: 0.8;
  transition: all 0.3s ease-in-out;
}
#carousel_slider.flexslider ul.slides li .caption {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 9;
  width: 100%;
  height: 100%;
  padding: 20px;
  color: #fff;
  background: rgba(0, 0, 0, 0.7);
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.7), transparent);
}
#carousel_slider.flexslider ul.slides li .caption h3 {
  font-size: 18px;
  font-size: 1.125rem;
  color: #fff;
  font-weight: 600;
}
#carousel_slider.flexslider ul.slides li .caption h3 span {
  margin: 3px 0 0 0;
  display: block;
  text-transform: uppercase;
  font-size: 13px;
  font-size: 0.8125rem;
  letter-spacing: 1px;
  font-weight: 600;
}
#carousel_slider.flexslider ul.slides li .caption small {
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.77, 0, 0.175, 1);
  position: absolute;
  bottom: 10px;
  left: 20px;
  display: block;
  width: 100%;
  font-weight: 500;
  font-size: 14px;
  font-size: 0.875rem;
  transform: translate(0, 10px);
}
#carousel_slider.flexslider ul.slides li .caption small strong {
  float: right;
  display: inline-block;
  text-align: right;
  margin: 0 40px 0 0;
  padding: 3px 0 0 0;
}
#carousel_slider.flexslider ul.slides li .caption small strong h6 {
  color: #fff;
  margin: 0;
  font-weight: 500;
  font-size: 14px;
  font-size: 0.875rem;
}
#carousel_slider.flexslider ul.slides li:hover .caption small {
  opacity: 1;
  transform: translate(0, 0);
}