<?php
require('../admin/inc/db_config.php');
require('../admin/inc/essentials.php');

session_start();
date_default_timezone_set("Africa/Lagos");

// Check if form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Create uploads directory if it doesn't exist
    $uploadDir = '../uploads/payments/';
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    // Sanitize input data
    $sender_name = mysqli_real_escape_string($con, $_POST['sender_name']);
    $sender_email = mysqli_real_escape_string($con, $_POST['sender_email']);
    $sender_phone = mysqli_real_escape_string($con, $_POST['sender_phone']);
    $sender_account_number = mysqli_real_escape_string($con, $_POST['sender_account_number']);
    $bank_name = mysqli_real_escape_string($con, $_POST['bank_name']);
    $amount = mysqli_real_escape_string($con, $_POST['amount']);
    $reference = mysqli_real_escape_string($con, $_POST['reference']);
    $room_id = mysqli_real_escape_string($con, $_POST['room_id']);
    $user_id = mysqli_real_escape_string($con, $_POST['user_id']);
    $checkin = mysqli_real_escape_string($con, $_POST['checkin']);
    $checkout = mysqli_real_escape_string($con, $_POST['checkout']);
    $room_name = mysqli_real_escape_string($con, $_POST['room_name']);
    
    // Process file upload
    $screenshot = null;
    if (isset($_FILES['screenshot']) && $_FILES['screenshot']['error'] == 0) {
        $fileName = 'payment_' . time() . '_' . $_FILES['screenshot']['name'];
        $filePath = $uploadDir . $fileName;
        
        // Move uploaded file
        if (move_uploaded_file($_FILES['screenshot']['tmp_name'], $filePath)) {
            $screenshot = 'uploads/payments/' . $fileName;
        } else {
            echo "<script>alert('Error uploading file. Please try again.');</script>";
            echo "<script>window.history.back();</script>";
            exit;
        }
    } else {
        echo "<script>alert('Screenshot is required. Please try again.');</script>";
        echo "<script>window.history.back();</script>";
        exit;
    }
    
    // Current date and time
    $datentime = date('Y-m-d H:i:s');
    
    try {
        // Setting booking status to 'pending' and rate_review to NULL
        $booking_status = 'pending'; 
        $payment_status = 'pending';
        $refund = 0;
        $arrival = 0;
        
        // Direct insertion to avoid parameter binding errors
        $query = "INSERT INTO booking_order (
            user_id, room_id, check_in, check_out, arrival, refund, 
            booking_status, order_id, trans_id, trans_amt, 
            payment_status, rate_review, payment_proof, datentime,
            sender_name, sender_email, sender_phone, sender_account_number, bank_name
        ) VALUES (
            '$user_id', '$room_id', '$checkin', '$checkout', $arrival, $refund,
            '$booking_status', '$reference', '$reference', '$amount',
            '$payment_status', NULL, '$screenshot', '$datentime',
            '$sender_name', '$sender_email', '$sender_phone', '$sender_account_number', '$bank_name'
        )";
        
        $result = mysqli_query($con, $query);
        
        if ($result) {
            // Get the booking ID
            $booking_id = mysqli_insert_id($con);
            
            // Check if booking_details table exists and create it if needed
            $check_table = mysqli_query($con, "SHOW TABLES LIKE 'booking_details'");
            if (mysqli_num_rows($check_table) == 0) {
                $create_table = "CREATE TABLE `booking_details` (
                    `sr_no` int(11) NOT NULL AUTO_INCREMENT,
                    `booking_id` int(11) NOT NULL,
                    `room_name` varchar(100) DEFAULT NULL,
                    `total_pay` decimal(10,2) NOT NULL,
                    `customer_name` varchar(100) DEFAULT NULL,
                    `customer_email` varchar(100) NOT NULL,        
                    `customer_phone` varchar(20) DEFAULT NULL,
                    `transaction_date` datetime NOT NULL,
                    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (`sr_no`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
                mysqli_query($con, $create_table);
            }
            
            // Insert additional details into booking_details
            $details_query = "INSERT INTO booking_details (
                booking_id, room_name, total_pay, customer_name, 
                customer_email, customer_phone, transaction_date
            ) VALUES (
                $booking_id, '$room_name', '$amount', '$sender_name',
                '$sender_email', '$sender_phone', '$datentime'
            )";
            
            $details_result = mysqli_query($con, $details_query);
            
            if ($details_result) {
                // Set success session message
                $_SESSION['bank_transfer_success'] = true;
                
                // Redirect to thank you page
                header("Location: bank_transfer_success.php?ref=" . $reference);
                exit;
            } else {
                throw new Exception("Error inserting booking details: " . mysqli_error($con));
            }
        } else {
            throw new Exception("Error creating booking: " . mysqli_error($con));
        }
    } catch (Exception $e) {
        echo "<script>alert('An error occurred: " . $e->getMessage() . "');</script>";
        echo "<script>window.history.back();</script>";
    }
} else {
    // Redirect if not POST request
    header("Location: ../index.php");
    exit;
}
?>