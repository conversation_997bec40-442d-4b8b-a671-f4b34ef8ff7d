<?php
require('../inc/db_config.php');
require('../inc/essentials.php');

adminLogin();

if (isset($_POST['booking_analytics'])) {

    $frm_data  = filteration($_POST);

    $condition = "";

    if ($frm_data['period'] == 1) {
        $condition = "WHERE datentime BETWEEN (NOW() - INTERVAL 30 DAY) AND NOW()";
    } else if ($frm_data['period'] == 2) {
        $condition = "WHERE datentime BETWEEN (NOW() - INTERVAL 90 DAY) AND NOW()";
    } else if ($frm_data['period'] == 3) {
        $condition = "WHERE datentime BETWEEN (NOW() - INTERVAL 1 YEAR) AND NOW()";
    }

    $query = "SELECT
        COUNT(CASE WHEN booking_status!='pending' THEN 1 END) AS total_bookings,
        SUM(CASE WHEN booking_status!='pending' THEN trans_amt END) AS total_amt,

        COUNT(CASE WHEN booking_status='booked' AND arrival=1 THEN 1 END) AS active_bookings,
        SUM(CASE WHEN booking_status='booked' AND arrival=1 THEN trans_amt END) AS active_amt,
        
        COUNT(CASE WHEN booking_status='pending' THEN 1 END) AS pending_bookings,
        SUM(CASE WHEN booking_status='pending' THEN trans_amt END) AS pending_amt,

        COUNT(CASE WHEN booking_status='cancelled' AND refund=1 THEN 1 END) AS cancelled_bookings,
        SUM(CASE WHEN booking_status='cancelled' AND refund=1 THEN trans_amt END) AS cancelled_amt

        FROM booking_order $condition";

    $res = mysqli_query($con, $query);
    $data = mysqli_fetch_assoc($res);

    // Handle possible nulls for SUM
    foreach ($data as $key => $value) {
        $data[$key] = ($value !== null) ? $value : 0;
    }

    echo json_encode($data);
}

if (isset($_POST['user_analytics'])) {
    $frm_data = filteration($_POST);
    
    $condition = "";
    
    if ($frm_data['period'] == 1) {
        $condition = "WHERE datentime BETWEEN (NOW() - INTERVAL 30 DAY) AND NOW()";
    } else if ($frm_data['period'] == 2) {
        $condition = "WHERE datentime BETWEEN (NOW() - INTERVAL 90 DAY) AND NOW()";
    } else if ($frm_data['period'] == 3) {
        $condition = "WHERE datentime BETWEEN (NOW() - INTERVAL 1 YEAR) AND NOW()";
    }
    
    // New registrations query - using the pattern from your code
    $total_registration = mysqli_fetch_assoc(mysqli_query($con, "SELECT COUNT(id) AS `count` FROM `user_cred` $condition"));
    
    // Feedbacks query - using the same pattern
    $total_queries = mysqli_fetch_assoc(mysqli_query($con, "SELECT COUNT(sr_no) AS `count` FROM `user_queries` $condition"));
    
    // Reviews query - using the same pattern
    $total_reviews = mysqli_fetch_assoc(mysqli_query($con, "SELECT COUNT(sr_no) AS `count` FROM `rating_review` $condition"));
    
    $data = [
        'new_registrations' => $total_registration['count'] ?? 0,
        'new_feedbacks' => $total_queries['count'] ?? 0,
        'new_reviews' => $total_reviews['count'] ?? 0
    ];
    
    echo json_encode($data);
}
?>