<?php 
require('inc/db_config.php');
require('inc/links.php');
require('inc/essentials.php');
adminLogin();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ADMIN-DASHBOARD</title>
    <?php require("inc/links.php")?>
</head>
<body class="bg-light">
    <?php require('inc/header.php'); 
    
    $is_shutdown = mysqli_fetch_assoc(mysqli_query($con, "SELECT `shutdown` FROM `settings`"));

    $current_bookings_qry = mysqli_query($con, "SELECT
        COUNT(CASE WHEN booking_status='booked' AND arrival=0 THEN 1 END) AS `new_bookings`,
        COUNT(CASE WHEN booking_status='cancelled' AND refund =0 THEN 1 END) AS `refund_bookings`,
        COUNT(CASE WHEN booking_status='pending' THEN 1 END) AS `pending_bookings`
        FROM `booking_order` ");
    $current_bookings = mysqli_fetch_assoc($current_bookings_qry);

    $unread_queries = mysqli_fetch_assoc(mysqli_query($con, "SELECT COUNT(sr_no) AS `count` 
        FROM `user_queries` WHERE `seen` =0"));

    $unread_reviews = mysqli_fetch_assoc(mysqli_query($con, "SELECT COUNT(sr_no) AS `count` 
        FROM `rating_review` WHERE `seen` =0"));

    $current_users_qry = mysqli_query($con, "SELECT
        COUNT(id) AS `total`,
        COUNT(CASE WHEN `status`=1 THEN 1 END) AS `active`,
        COUNT(CASE WHEN `status`=0 THEN 1 END) AS `inactive`,
        COUNT(CASE WHEN `is_verified`=0 THEN 1 END) AS `unverified`
        FROM `user_cred` ");
    $user_bookings = mysqli_fetch_assoc($current_users_qry);
    ?>

    <div class="container-fluid" id="main-content">
        <div class="row">
            <div class="col-lg-10 ms-auto p-4 overflow-hidden">
                <div class="d-flex align-items-center justify-content-between mb-4">
                    <h3>DASHBOARD</h3>
                    <?php if ($is_shutdown['shutdown'] == 1): ?>
                        <h6 class="badge bg-danger py-2 px-3 rounded">Shutdown mode is Active</h6>
                    <?php endif; ?>
                </div>

                <div class="row mb-4">
                    <div class="col-md-3 mb-4">
                        <a href="new_bookings.php" class="text-decoration-none">
                            <div class="card text-center text-success p-3">
                                <h6>New Bookings</h6>
                                <h1 class="mt-2 mb-0"><?php echo $current_bookings['new_bookings']; ?></h1>
                            </div>
                        </a>
                    </div>

                    <div class="col-md-3 mb-4">
                        <a href="refund_bookings.php" class="text-decoration-none">
                            <div class="card text-center text-warning p-3">
                                <h6>Refund Bookings</h6>
                                <h1 class="mt-2 mb-0"><?php echo $current_bookings['refund_bookings']; ?></h1>
                            </div>
                        </a>
                    </div>

                    <div class="col-md-3 mb-4">
                        <a href="pending_bookings.php" class="text-decoration-none">
                            <div class="card text-center text-primary p-3">
                                <h6>Pending Bookings</h6>
                                <h1 class="mt-2 mb-0"><?php echo $current_bookings['pending_bookings']; ?></h1>
                            </div>
                        </a>
                    </div>

                    <div class="col-md-3 mb-4">
                        <a href="user_queries.php" class="text-decoration-none">
                            <div class="card text-center text-info p-3">
                                <h6>User Feedbacks</h6>
                                <h1 class="mt-2 mb-0"><?php echo $unread_queries['count']; ?></h1>
                            </div>
                        </a>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-3 mb-4">
                        <a href="rate_review.php" class="text-decoration-none">
                            <div class="card text-center text-info p-3">
                                <h6>Ratings & Reviews</h6>
                                <h1 class="mt-2 mb-0"><?php echo $unread_reviews['count']; ?></h1>
                            </div>
                        </a>
                    </div>
                </div>

                <div class="d-flex align-items-center justify-content-between mb-3">
                    <h5>Booking Analytics</h5>
                    <select class="form-select shadow-none bg-light w-auto" onchange="booking_analytics(this.value)" aria-label="Default select example">
                        <option value="1">Last 30 Days</option>
                        <option value="2">Last 90 Days</option>
                        <option value="3">Last 1 Year</option>
                        <option value="4">All time</option>
                    </select>
                </div>

                <div class="row mb-3">
                    <div class="col-md-3 mb-4">
                        <div class="card text-center text-primary p-3">
                            <h6>Total Bookings</h6>
                            <h1 class="mt-2 mb-0"id="total_bookings"></h1>
                            <h4 class="mt-2 mb-0" id="total_amt">₦0</h4>
                        </div>
                    </div>

                    <div class="col-md-3 mb-4">
                        <div class="card text-center text-success p-3">
                            <h6>Active Bookings</h6>
                            <h1 class="mt-2 mb-0" id="active_bookings">0</h1>
                            <h4 class="mt-2 mb-0" id="active_amt">₦0</h4>
                        </div>
                    </div>

                    <div class="col-md-3 mb-4">
                        <div class="card text-center text-primary p-3">
                            <h6>Pending Bookings</h6>
                            <h1 class="mt-2 mb-0" id="pending_bookings">0</h1>
                            <h4 class="mt-2 mb-0" id="pending_amt">₦0</h4>
                        </div>
                    </div>

                    <div class="col-md-3 mb-4">
                        <div class="card text-center text-danger p-3">
                            <h6>Cancelled Bookings</h6>
                            <h1 class="mt-2 mb-0" id="cancelled_bookings">0</h1>
                            <h4 class="mt-2 mb-0" id="cancelled_amt">₦0</h4>
                        </div>
                    </div>
                </div>

                <div class="d-flex align-items-center justify-content-between mb-3">
                    <h5>User, Feedbacks, Reviews Analytics</h5>
                    <select class="form-select shadow-none bg-light w-auto" onchange="user_analytics(this.value)" aria-label="Default select example">
                        <option value="1">Last 30 Days</option>
                        <option value="2">Last 90 Days</option>
                        <option value="3">Last 1 Year</option>
                        <option value="4">All time</option>
                    </select>
                </div>

                <div class="row mb-3">
                    <div class="col-md-4 mb-4">
                        <div class="card text-center text-success p-3">
                            <h6>New Registration</h6>
                            <h1 class="mt-2 mb-0" id="new_registrations">0</h1>
                        </div>
                    </div>

                    <div class="col-md-4 mb-4">
                        <div class="card text-center text-success p-3">
                            <h6>Feedbacks</h6>
                            <h1 class="mt-2 mb-0" id="new_feedbacks"><?php echo $unread_queries['count']; ?></h1>
                        </div>
                    </div>

                    <div class="col-md-4 mb-4">
                        <div class="card text-center text-primary p-3">
                            <h6>Reviews</h6>
                            <h1 class="mt-2 mb-0" id="new_reviews"><?php echo $unread_reviews['count']; ?></h1>
                        </div>
                    </div>
                </div>
                                <div class="d-flex align-items-center justify-content-between mb-3">
                    <h5>Users</h5>
                </div>

                <div class="row mb-3">
                    <div class="col-md-3 mb-3">
                        <div class="card text-center text-info p-3">
                            <h6>Total Users</h6>
                            <h1 class="mt-2 mb-0"><?php echo $user_bookings['total']; ?></h1>
                        </div>
                    </div>

                    <div class="col-md-3 mb-3">
                        <div class="card text-center text-success p-3">
                            <h6>Active Users</h6>
                            <h1 class="mt-2 mb-0"><?php echo $user_bookings['active']; ?></h1>
                        </div>
                    </div>

                    <div class="col-md-3 mb-3">
                        <div class="card text-center text-warning p-3">
                            <h6>Inactive Users</h6>
                            <h1 class="mt-2 mb-0"><?php echo $user_bookings['inactive']; ?></h1>
                        </div>
                    </div>

                    <div class="col-md-3 mb-3">
                        <div class="card text-center text-danger p-3">
                            <h6>Unverified Users</h6>
                            <h1 class="mt-2 mb-0"><?php echo $user_bookings['unverified']; ?></h1>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <?php require('inc/script.php'); ?>
    <script src="scripts/dashboard.js"></script>
</body>
</html>