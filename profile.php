<?php
// Check if the HTTP_REFERER is set and matches your domain
// $referer = $_SERVER['HTTP_REFERER'] ?? '';  // Get the referer

// // Define your site's base URL
// $base_url = 'https://127.0.0.1/h';  // Replace with your actual domain

// // If the referer is empty or doesn't match your base URL, stop page loading
// if (empty($referer) || strpos($referer, $base_url) === false) {
//     // Redirect or display an error message
//     header("Location: $base_url/"); // Redirect to the homepage or another page
//     exit; // Stop further execution of the script
// }
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EBITARE - PROFILE</title>
    <?php require('inc/links.php'); ?>
    
    <!-- GOOGLE WEB FONT-->
    <link rel="preconnect" href="https://fonts.googleapis.com/">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Caveat:wght@400;500&family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Montserrat', sans-serif;
            background-color: #f7f7f7;
        }

        .bg-white {
            background-color: #fff;
        }

        .custom-bg {
            background-color: #007bff;
        }

        .rounded-img {
            border-radius: 10px;
        }

        .rounded-circle {
            border-radius: 50%;
            object-fit: cover;
        }

        .shadow-sm {
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .container {
            max-width: 1200px;
            padding: 0 15px;
            margin: 0 auto;
        }

        .breadcrumbs {
            font-size: 14px;
            margin-bottom: 20px;
            padding: 15px 0;
        }

        .breadcrumbs a {
            text-decoration: none;
            color: #007bff;
            transition: color 0.3s;
        }

        .breadcrumbs a:hover {
            color: #0056b3;
        }

        .breadcrumbs span {
            color: #666;
        }

        .form-card {
            background-color: #fff;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .form-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }

        h5.fw-bold {
            font-size: 1.3rem;
            color: #333;
            margin-bottom: 20px;
            position: relative;
            padding-bottom: 10px;
        }

        h5.fw-bold:after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 3px;
            background-color: #007bff;
        }

        .form-label {
            font-weight: 500;
            color: #555;
        }

        .form-control {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px 15px;
            transition: border-color 0.3s;
        }

        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .btn {
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }

        .btn-custom {
            background-color: #007bff;
            color: #fff;
            border: none;
        }

        .btn-custom:hover {
            background-color: #0056b3;
            transform: translateY(-2px);
        }

        .profile-img-container {
            text-align: center;
            margin-bottom: 20px;
        }

        .profile-img-container img {
            width: 150px;
            height: 150px;
            object-fit: cover;
            border: 5px solid #fff;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        /* Responsive Styles */
        @media (max-width: 768px) {
            .form-card {
                padding: 20px;
            }
            
            h5.fw-bold {
                font-size: 1.1rem;
            }
            
            .btn {
                width: 100%;
                margin-bottom: 10px;
            }
        }
    </style>
</head>

<body>
    <?php require('inc/header.php'); ?>

    <?php
        if(!isset($_SESSION['login']) && $_SESSION['login'] === true){
            redirect('index.php');
            echo 'You are not logged so you cannot book room';
        }

        $u_exist = select("SELECT * FROM `user_cred` WHERE `id`=? LIMIT 1",[$_SESSION['uId']],'s');

        if(mysqli_num_rows($u_exist)==0){
            redirect('index.php');
        }

        $u_fetch = mysqli_fetch_assoc($u_exist);
    ?>

    <div class="container">
        <div class="breadcrumbs mb-4">
            <a href="index.php">Home</a>
            <span> &gt; </span>
            <span>Profile</span>
        </div>

        <div class="row">
            <!-- Basic Information Form -->
            <div class="col-md-8 mb-4">
                <div class="form-card">
                    <form id="info-form">
                        <h5 class="fw-bold">Basic Information</h5>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Name</label>
                                <input type="text" value="<?php echo isset($u_fetch['name']) ? htmlspecialchars($u_fetch['name']) : ''; ?>" class="form-control shadow-none" name="name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Phone</label>
                                <input type="number" value="<?php echo isset($u_fetch['phone']) ? htmlspecialchars($u_fetch['phone']) : ''; ?>" class="form-control shadow-none" name="phone" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Email</label>
                                <input type="email" value="<?php echo isset($u_fetch['email']) ? htmlspecialchars($u_fetch['email']) : ''; ?>" class="form-control shadow-none" name="email" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Address</label>
                                <textarea class="form-control shadow-none" name="address" rows="1" required><?php echo isset($u_fetch['address']) ? htmlspecialchars($u_fetch['address']) : ''; ?></textarea>
                            </div>
                            <div class="col-12 mt-3">
                                <button type="submit" class="btn btn-custom shadow-none">Save Changes</button>
                            </div>
                        </div>
                    </form>
                </div>
                
                <!-- Change Password Form -->
                <div class="form-card">
                    <form id="pass-form">
                        <h5 class="fw-bold">Change Password</h5>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">New Password</label>
                                <input type="password" class="form-control shadow-none" name="new_pass" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Confirm Password</label>
                                <input type="password" class="form-control shadow-none" name="confirm_pass" required>
                            </div>
                            <div class="col-12 mt-3">
                                <button type="submit" class="btn btn-custom shadow-none">Save Changes</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Profile Picture Form -->
            <div class="col-md-4 mb-4">
                <div class="form-card">
                    <form id="profile-form">
                        <h5 class="fw-bold">Profile Picture</h5>
                        <div class="profile-img-container">
                            <img src="<?php echo USERS_IMG_PATH.$u_fetch['profile']?>" class="rounded-circle mb-3">
                        </div>
                        <label class="form-label">New Picture</label>
                        <input type="file" class="form-control shadow-none mb-4" name="profile" accept=".jpg, .jpeg, .png, .webp" required>
                        <button type="submit" class="btn btn-custom shadow-none w-100">Save Changes</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <?php require('inc/footer.php'); ?>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.3/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta2/dist/js/bootstrap.min.js"></script>
    <script>
       let info_form = document.getElementById('info-form');

info_form.addEventListener('submit', function (e){
    e.preventDefault();

    let data = new FormData();
    data.append('info_form', '');
    data.append('name', info_form.elements['name'].value);
    data.append('phone', info_form.elements['phone'].value);
    data.append('address', info_form.elements['address'].value);

    let xhr = new XMLHttpRequest();
    xhr.open("POST", "ajax/profile_crud.php", true);
    
    // Remove this line:
    // xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

    xhr.onload = function () {
        if (this.responseText == "phone_already") {
            alert('error', "Phone number is already registered!")
        } 
        else if(this.responseText == 0){
            alert('error', 'No Changes Made');
        }
        else{
            alert('success', "Changes Saved");
        }
    };
    
    xhr.send(data);
});

let profile_form = document.getElementById('profile-form');
profile_form.addEventListener('submit', function (e){
    e.preventDefault();

    let data = new FormData();
    data.append('profile_form', '');
    data.append('profile', profile_form.elements['profile'].files[0]);

    let xhr = new XMLHttpRequest();
    xhr.open("POST", "ajax/profile_crud.php", true);
    
    // Remove this line:
    // xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

    xhr.onload = function () {
       if (responseText == 'inv_img') {
        alert('error', "Only JPG,WEBP and PNG images are allowed!");
        }
        else if (responseText == 'upd_failed') {
            alert('error', "Image Upload failed!");
        }
        else if(this.responseText == 0){
            alert('error', 'No Changes Made');
        }
        else{
            window.location.href=window.location.pathname;
        }
    };
    
    xhr.send(data);
});
let pass_form = document.getElementById('pass-form');

pass_form.addEventListener('submit', function (e) {
    e.preventDefault();

    let new_pass = pass_form.elements['new_pass'].value;
    let confirm_pass = pass_form.elements['confirm_pass'].value;

    if (new_pass != confirm_pass) {
        alert('error', 'Passwords do not match!');
        return false;
    }

    let data = new FormData();
    data.append('pass_form', '');
    data.append('new_pass', new_pass);
    data.append('confirm_pass', confirm_pass);

    let xhr = new XMLHttpRequest();
    xhr.open("POST", "ajax/profile_crud.php", true);

    xhr.onload = function () {
        let res = this.responseText.trim();
        if (res == 'mismatch') {
            alert('error', "Passwords do not match!");
        }
        else if (res == 0) {
            alert('error', 'Password change failed!');
        }
        else {
            alert('success', 'Password changed successfully!');
            pass_form.reset();
        }
    };

    xhr.send(data);
});
    </script>
</body>
</html>