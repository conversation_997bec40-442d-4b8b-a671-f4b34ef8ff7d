<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>EBITARE - Rooms</title>
  <?php require('inc/links.php'); ?>
  
  <!-- BASE CSS -->
  <link href="css/bootstrap.min.css" rel="stylesheet">
  <link href="css/style.css" rel="stylesheet">
  <link href="css/vendors.min.css" rel="stylesheet">
  
  <!-- SCRIPTS -->
  <script src="js/common_scripts.js"></script>
  <script src="js/common_functions.js"></script>
  
  <style>
    /* Styling from original template */
    .row_list_version_2 {
      margin-bottom: 90px;
    }
    .box_item_info {
      background-color: #fff;
      padding: 45px;
      margin-left: -45px;
      z-index: 99;
      position: relative;
      height: 100%;
      border-radius: 5px;
      box-shadow: 0 20px 40px -5px rgba(0, 0, 0, 0.1);
    }
    .box_item_info small {
      text-transform: uppercase;
      color: #222;
      font-weight: 500;
    }
    .box_item_info h2 {
      font-size: 28px;
      margin-top: 5px;
      margin-bottom: 15px;
    }
    .facilities ul {
      list-style: none;
      padding: 0;
      margin: 0;
      margin-bottom: 20px;
    }
    .facilities ul li {
      display: inline-block;
      margin-right: 15px;
    }
    .facilities ul li i {
      margin-right: 5px;
    }
    .box_item_footer {
      margin-top: 20px;
    }
    .btn_4 {
      border-radius: 25px;
      padding: 8px 25px;
      background-color: #279eBc;
      color: #fff;
      text-decoration: none;
      font-weight: 500;
      transition: all 0.3s ease;
    }
    .btn_4:hover {
      background-color: #1e7e96;
    }
    .animated_link {
      text-decoration: none;
      color: #333;
      position: relative;
    }
    .animated_link:after {
      content: '';
      position: absolute;
      width: 0;
      height: 1px;
      bottom: -3px;
      left: 0;
      background-color: #279eBc;
      transition: all 0.3s ease;
    }
    .animated_link:hover:after {
      width: 100%;
    }
    .rounded-img img {
      border-radius: 5px;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    .hero {
      height: 350px;
      position: relative;
      background-color: #000;
      margin-bottom: 80px;
    }
    .hero img {
      height: 100%;
      width: 100%;
      object-fit: cover;
      opacity: 0.7;
    }
    .wrapper {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      text-align: center;
      color: #fff;
    }
    .wrapper small {
      text-transform: uppercase;
      letter-spacing: 2px;
      display: block;
      margin-bottom: 5px;
    }
    .wrapper h1 {
      font-size: 46px;
      font-weight: 700;
    }
    
    /* Additional custom styles */
    .room-card {
      transition: all 0.3s ease;
      border-radius: 5px;
      overflow: hidden;
      margin-bottom: 30px;
    }
    .room-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 20px 40px -5px rgba(0, 0, 0, 0.1);
    }
    .filter-panel {
      background: #fff;
      border-radius: 5px;
      padding: 25px;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
      margin-bottom: 30px;
    }
    .filter-heading {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 15px;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
    }
    .badge {
      margin-right: 5px;
      padding: 6px 12px;
      background-color: #f8f9fa;
      border: 1px solid #eee;
      font-weight: 500;
    }
    /* Carousel styling */
    .owl-carousel .item img {
      height: 250px;
      object-fit: cover;
      border-radius: 5px;
    }
    .owl-dots {
      position: absolute;
      bottom: 10px;
      width: 100%;
      text-align: center;
    }
  </style>
</head>

<body>
  <?php 
  
  $checkin_default = "";
  $checkout_default = "";
  $adult_default = "";
  $children_default = "";
  
  if(isset($_GET['check_availability']))
  {
    $frm_data = filteration($_GET);

    $checkin_default = $frm_data['checkin'] ?? '';
    $checkout_default = $frm_data['checkout'] ?? '';
    $adult_default = $frm_data['adult'] ?? '';
    $children_default = $frm_data['children'] ?? '';
  }
  // Set default values if empty
  if(empty($adult_default)) $adult_default = 1;
  if(empty($children_default)) $children_default = 0;
  
  // Set minimum date to today
  $today_date = date('Y-m-d');
  ?>

  <!-- Hero Banner -->
  <div class="hero jarallax" data-jarallax data-speed="0.2">
    <img class="jarallax-img" src="img/rooms/2.jpg" alt="">
    <div class="wrapper d-flex align-items-center justify-content-center text-center">
      <div class="container">
        <small>Hospitality at its peak</small>
        <h1>Our Rooms & Suites</h1>
      </div>
    </div>
  </div>

  <div id="preloader">
        <div data-loader="circle-side"></div>
    </div><!-- /Page Preload -->

    <div class="layer"></div><!-- Opacity Mask -->

    <header class="reveal_header">
        <div class="container-fluid">
            <div class="row align-items-center">
                 <div class="col-6">
                        <a href="#" class="logo_normal"><img src="img/logo.png" width="135" height="45" alt=""></a>
                        <a href="index-3.php" class="logo_sticky"><img src="img/logo_sticky.png" width="135" height="45" alt=""></a>
                </div>
                <div class="col-6">
                    <nav>
                        <ul>
                            <li><a href="rooms.php" class="btn_1 btn_scrollto">Book Now</a></li>
                            <li>
                               <div class="hamburger_2 open_close_nav_panel">
                                    <div class="hamburger__box">
                                        <div class="hamburger__inner"></div>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div><!-- /container -->
    </header><!-- /Header -->

    <div class="nav_panel">
        <a href="#0" class="closebt open_close_nav_panel"><i class="bi bi-x"></i></a>
        <div class="logo_panel"><img src="img/logo_sticky.png" width="135" height="45" alt=""></div>
        <div class="sidebar-navigation">
            <nav>
                <ul class="level-1">
                    <li class=""><a href="index-3.php">Home</a>
                    </li>
                    <li class=""><a href="rooms.php">Rooms & Suites</a>
                    </li>
                   
                    
                    <li><a href="about.php">About</a></li>
                    <li><a href="contacts.php">Contact Us</a></li>
                    <li class="parent"><a href="#0">Explore</a>
                        <ul class="level-2">
                            <li class="back"><a href="#0">Back</a></li>
                    
                            <li><a href="gallery.php">Ebitare Gallery</a></li>
                           
                        </ul>
                    </li>
                </ul>
                <div class="panel_footer">
                    <div class="phone_element"><a href="tel://423424234"><i class="bi bi-telephone"></i><span><em>Info and bookings</em>+<?php echo $contact_r['pn1']?></span></a></div>
                </div>
                <!-- /panel_footer -->
            </nav>
        </div>
        <!-- /sidebar-navigation -->
    </div>

  <div class="container">
    <div class="row">
      <!-- Sidebar Filter -->
      <div class="col-lg-3 col-md-12 mb-4">
        <div class="filter-panel">
          <h4 class="filter-heading">FILTER</h4>
          
          <!-- Availability Section -->
          <div class="mb-4">
            <h5 class="d-flex align-items-center justify-content-between">
              <span>CHECK AVAILABILITY</span>
              <button id="chk_avail_btn" onclick="chk_avail_clear()" class="btn btn-sm shadow-none text-secondary d-none">Reset</button>
            </h5>

            <label class="form-label">Check-in</label>
            <input type="date" class="form-control shadow-none" id="checkin" min="<?php echo $today_date; ?>" value="<?php echo $checkin_default; ?>" onchange="validateDates()">
            <div id="checkin-error" class="text-danger small d-none"></div>

            <label class="form-label mt-2">Check-out</label>
            <input type="date" class="form-control shadow-none" id="checkout" min="<?php echo $today_date; ?>" value="<?php echo $checkout_default; ?>" onchange="validateDates()">
            <div id="checkout-error" class="text-danger small d-none"></div>
            
            <button onclick="applyDateFilter()" class="btn_4 w-100 mt-3">Apply Filter</button>
          </div>

          <!-- Guests Filter -->
          <div class="mb-4">
            <h5 class="d-flex align-items-center justify-content-between filter-heading">
              <span>GUESTS</span>
              <button id="guests_btn" onclick="guests_clear()" class="btn btn-sm shadow-none text-secondary d-none">Reset</button>
            </h5>
            <div class="row">
              <div class="col-6">
                <label class="form-label">Adults</label>
                <input type="number" min="1" id="adult" value="<?php echo $adult_default; ?>" class="form-control shadow-none">
              </div>
              <div class="col-6">
                <label class="form-label">Children</label>
                <input type="number" min="0" id="children" value="<?php echo $children_default; ?>" class="form-control shadow-none">
              </div>
            </div>
            <button onclick="applyGuestFilter()" class="btn_4 w-100 mt-3">Apply Filter</button>
          </div>
        </div>
      </div>

      <!-- Room Listings -->
      <div class="col-lg-9 col-md-12">
        <div id="status-message" class="alert d-none" role="alert"></div>
        <div id="rooms-data" class="row_list_version_2">
          <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading rooms...</span>
            </div>
            <p class="mt-3">Loading available rooms...</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="pinned-image pinned-image--medium">
                <div class="pinned-image__container" id="section_video">
                    <video loop="loop" muted="muted" id="video_home">
                        <source src="video/swimming_pool_2.mp4" type="video/mp4">
                        <source src="video/swimming_pool_2.webm" type="video/webm">
                        <source src="video/swimming_pool_2.ogv" type="video/ogg">
                    </video>
                    <div class="pinned-image__container-overlay"></div>
                </div>
                <div class="pinned_over_content">
                    <div class="title white">
                        <small data-cue="slideInUp" data-delay="200">Hospitality at its peak</small>
                        <h2 data-cue="slideInUp" data-delay="300">Immerse Yourself<br>in Pure Relaxation</h2>
                    </div>
                </div>
            </div>
</div>
 <!-- /footer -->
 <footer class=" mt-5">
    <style>
        
        /* White color for secondary phone number and social links */
        
        .footer_bg a[href^="tel:+"]:has(i.bi-telephone-fill),
        .footer_bg a[href^="tel:+"] i.bi-telephone-fill,
        .footer_bg .social a,
        .footer_bg .social a i {
            color: white !important;
        }
        

        /* Optional: hover effect for social links */
        .footer_bg .social a:hover,
        .footer_bg .social a:hover i {
            color: #ffd700 !important; /* gold on hover */
        }
    </style>

    <div class="footer_bg">
        <div class="gradient_over"></div>
        <div class="background-image" data-background="url(img/rooms/3.jpg)"></div>
    </div>
    <div class="container">
        <div class="row move_content">
            <!-- Contact Section -->
            <div class="col-lg-4 col-md-12">
                <h5>Contact Us</h5>
                <ul>
                    <li>Address: <?php echo $contact_r['address']; ?><br><br></li>
                    <li><i class="bi bi-envelope-fill">   : </i> <strong><a href="mailto:<?php echo $contact_r['email']; ?>"><?php echo $contact_r['email']; ?></a></strong></li>
                    <li><i class="bi bi-telephone-fill">  : </i> <strong><a href="tel:+<?php echo ltrim($contact_r['pn1'], '+'); ?>">+<?php echo $contact_r['pn1']; ?></a></strong></li>
                    <?php if (!empty($contact_r['pn2'])): ?>
                        <li>
                            <strong>
                                <a href="tel:+<?php echo ltrim($contact_r['pn2'], '+'); ?>" class="d-inline-block mb-2 text-decoration-none">
                                    <i class="bi bi-telephone-fill">  : </i> +<?php echo $contact_r['pn2']; ?>
                                </a>
                            </strong>
                        </li>
                    <?php endif; ?>
                </ul>

                <!-- Social Media Links -->
                <div class="social">
                    <ul>
                        <?php if (!empty($contact_r['insta'])): ?>
                            <li>
                                <a href="<?php echo $contact_r['insta']; ?>" class="d-inline-block mb-2 text-decoration-none" target="_blank">
                                    <i class="bi bi-instagram me-1"></i> 
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if (!empty($contact_r['fb'])): ?>
                            <li>
                                <a href="<?php echo $contact_r['fb']; ?>" class="d-inline-block mb-2 text-decoration-none" target="_blank">
                                    <i class="bi bi-facebook me-1"></i> 
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if (!empty($contact_r['tw'])): ?>
                            <li>
                                <a href="<?php echo $contact_r['tw']; ?>" class="d-inline-block mb-2 text-decoration-none" target="_blank">
                                    <i class="bi bi-twitter-x me-1"></i> 
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>

            <!-- Explore Section -->
            <div class="col-lg-3 col-md-6 ms-lg-auto">
                <h5>Explore</h5>
                <div class="footer_links">
                    <ul>
                        <li><a href="index.php">Home</a></li>
                        <li><a href="about.php">About Us</a></li>
                        <li><a href="rooms.php">Rooms &amp; Suites</a></li>
                        <li><a href="contacts.php">Contact Us</a></li>
                    </ul>
                </div>
            </div>

            <!-- Newsletter Section -->
            <div class="col-lg-3 col-md-6">
                <div id="newsletter">
                    <h5>Newsletter</h5>
                    <div id="message-newsletter"></div>
                    <form method="post" action="" name="newsletter_form" id="newsletter_form">
                        <div class="form-group">
                            <input type="email" name="email_newsletter" id="email_newsletter" class="form-control" placeholder="Your email" required>
                            <button type="submit" id="submit-newsletter"><i class="bi bi-send"></i></button>
                        </div>
                    </form>
                    <p>Receive latest offers and promos without spam. You can cancel anytime.</p>
                </div>
            </div>
        </div>
        <!--/row-->
    </div>
</footer>



    <div class="progress-wrap">
        <svg class="progress-circle svg-content" width="100%" height="100%" viewBox="-1 -1 102 102">
            <path d="M50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98"/>
        </svg>
    </div>
            

  <!-- JS Script -->
  <script>
    let rooms_data = document.getElementById('rooms-data');
    let statusMessage = document.getElementById('status-message');
    let checkin = document.getElementById('checkin');
    let checkout = document.getElementById('checkout');
    let checkinError = document.getElementById('checkin-error');
    let checkoutError = document.getElementById('checkout-error');
    let chk_avail_btn = document.getElementById('chk_avail_btn');

    let adult = document.getElementById('adult');
    let children = document.getElementById('children');
    let guests_btn = document.getElementById('guests_btn');
    
    // Set default date values if empty
    if(!checkin.value) {
      const today = new Date();
      checkin.value = today.toISOString().split('T')[0];
    }
    
    if(!checkout.value) {
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      checkout.value = tomorrow.toISOString().split('T')[0];
    }

    function validateDates() {
      // Reset error messages
      checkinError.classList.add('d-none');
      checkoutError.classList.add('d-none');
      
      const checkinDate = new Date(checkin.value);
      const checkoutDate = new Date(checkout.value);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      let isValid = true;
      
      // Validate check-in date
      if(checkinDate < today) {
        checkinError.textContent = "Check-in date cannot be in the past";
        checkinError.classList.remove('d-none');
        isValid = false;
      }
      
      // Validate check-out date
      if(checkoutDate <= checkinDate) {
        checkoutError.textContent = "Check-out date must be after check-in date";
        checkoutError.classList.remove('d-none');
        isValid = false;
      }
      
      return isValid;
    }

    function applyDateFilter() {
      if(validateDates()) {
        fetch_rooms();
        chk_avail_btn.classList.remove('d-none');
      }
    }
    
    function applyGuestFilter() {
      if(adult.value > 0) {
        fetch_rooms();
        guests_btn.classList.remove('d-none');
      } else {
        alert("Please specify at least 1 adult");
      }
    }

    function fetch_rooms() {
      // Show loading state
      rooms_data.innerHTML = `
        <div class="text-center py-5">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading rooms...</span>
          </div>
          <p class="mt-3">Loading available rooms...</p>
        </div>
      `;
      
      // Hide any previous status messages
      statusMessage.classList.add('d-none');
      
      // Prepare filters
      let chk_avail = JSON.stringify({
        checkin: checkin.value,
        checkout: checkout.value
      });

      let guests = JSON.stringify({
        adult: adult.value || 1,
        children: children.value || 0
      });

      let xhr = new XMLHttpRequest();
      xhr.open("GET", `ajax/rooms_crud.php?fetch_rooms&chk_avail=${chk_avail}&guests=${guests}`, true);
      
      xhr.onload = function () {
        if(this.status == 200) {
          if(this.responseText.trim() === '') {
            rooms_data.innerHTML = `
              <div class="text-center py-5">
                <h4>No rooms found matching your criteria</h4>
                <p>Try changing your search filters or dates</p>
              </div>
            `;
          } else {
            rooms_data.innerHTML = this.responseText;
            
            // Initialize owl carousel for all room image galleries
            initializeOwlCarousels();
          }
        } else {
          // Show error message
          statusMessage.textContent = "Error loading rooms. Please try again.";
          statusMessage.classList.remove('d-none');
          statusMessage.classList.add('alert-danger');
          
          rooms_data.innerHTML = `
            <div class="text-center py-5">
              <h4>Couldn't load rooms</h4>
              <p>Please refresh the page or try again later</p>
            </div>
          `;
        }
      };
      
      xhr.onerror = function() {
        // Handle network errors
        statusMessage.textContent = "Network error. Please check your connection.";
        statusMessage.classList.remove('d-none');
        statusMessage.classList.add('alert-danger');
        
        rooms_data.innerHTML = `
          <div class="text-center py-5">
            <h4>Connection Error</h4>
            <p>Please check your internet connection and try again</p>
          </div>
        `;
      };

      xhr.send();
    }

    function initializeOwlCarousels() {
      if (typeof $.fn.owlCarousel !== 'undefined') {
        $('.carousel_item_1').each(function() {
          $(this).owlCarousel({
            items: 1,
            loop: true,
            margin: 0,
            dots: true,
            lazyLoad: true,
            nav: false,
            autoplay: true,
            autoplayTimeout: 5000,
            autoplayHoverPause: true
          });
        });
      }
      
      // Initialize lightbox if available
      if (typeof fslightbox !== 'undefined') {
        fslightbox.initialize();
      }
    }

    function chk_avail_clear() {
      // Reset to today and tomorrow
      const today = new Date();
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      
      checkin.value = today.toISOString().split('T')[0];
      checkout.value = tomorrow.toISOString().split('T')[0];
      
      fetch_rooms();
      chk_avail_btn.classList.add('d-none');
    }

    function guests_clear() {
      adult.value = '1';
      children.value = '0';
      fetch_rooms();
      guests_btn.classList.add('d-none');
    }

    // Check if user is logged in before booking
    function checkLoginToBook(isLoggedIn, roomId) {
      if(isLoggedIn) {
        window.location.href = `book.php?id=${roomId}&checkin=${checkin.value}&checkout=${checkout.value}&adult=${adult.value}&children=${children.value}`;
      } else {
        alert("Please login to book a room!");
        window.location.href = 'login.php';
      }
    }

    // Initial fetch on page load
    window.onload = function() {
      validateDates();
      fetch_rooms();
    }
  </script>

  <?php require('inc/footer.php'); ?>
  
  <!-- Include necessary JS libraries -->
  <script src="js/jquery-3.6.0.min.js"></script>
  <script src="js/owl.carousel.min.js"></script>
  <script src="js/fslightbox.js"></script>
  <script src="js/common_scripts.js"></script>
<script src="js/common_functions.js"></script>
<!-- <script src="js/datepicker_search.js"></script> -->
<script src="js/datepicker_inline.js"></script>
<script src="phpmailer/validate.js"></script>


<!-- SPECIFIC SCRIPTS -->
<script src="js/jquery.flexslider.min.js"></script>
<script src="js/slider_func.js"></script>
</body>
</html>