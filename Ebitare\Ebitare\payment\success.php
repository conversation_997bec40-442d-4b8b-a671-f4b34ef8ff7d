<?php

require('../admin/inc/db_config.php');
require('../admin/inc/essentials.php');

date_default_timezone_set("Africa/Lagos");
session_start();
unset($_SESSION['room']);

header("Pragma: no-cache");
header("Cache-Control: no-cache");
header("Expires:0");

// Debug mode - set to true to show detailed errors instead of redirecting
$debug_mode = false;

// For debugging purposes - log details of the verification process
function log_message($message) {
    error_log("[Payment Verification] " . $message);
}

log_message("Verification process started");

// Initialize variables
$paymentVerified = false;
$paymentMessage = "Processing payment...";
$paymentAmount = 0;
$paymentRef = "";
$bookingId = 0;
$debugInfo = [];

// Function to verify payment
function verifyPayment($reference, $con) {
    global $paymentVerified, $paymentMessage, $paymentAmount, $paymentRef, $bookingId, $debugInfo;
    
    $paymentRef = $reference;
    $debugInfo[] = "Reference received: " . $reference;
    log_message("Reference received: " . $reference);
    
    // Set Paystack verification endpoint
    $verificationEndpoint = "https://api.paystack.co/transaction/verify/" . urlencode($reference);
    
    // Get secret key
    $secretKey = PAYSTACK_KEY;
    $debugInfo[] = "Using Paystack Key: " . (empty($secretKey) ? "EMPTY KEY" : "Key exists");
    
    // Set headers for Paystack API request
    $headers = [
        'Authorization: Bearer ' . $secretKey,
    ];
    
    // Initialize cURL session
    $ch = curl_init();
    
    // Set cURL options
    curl_setopt_array($ch, [
        CURLOPT_URL => $verificationEndpoint,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_SSL_VERIFYPEER => true,
    ]);
    
    // Execute the cURL request and get the response
    $responseData = curl_exec($ch);
    
    // Check for cURL errors
    $curlError = curl_error($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $debugInfo[] = "HTTP Response Code: " . $httpCode;
    
    // Close the cURL session
    curl_close($ch);

    // Check if the cURL request was successful
    if ($responseData === false) {
        $paymentMessage = "Processing your payment...";
        $debugInfo[] = "cURL error: " . $curlError;
        log_message("cURL error: " . $curlError);
        return false;
    } else {
        // Decode the response data
        $decodedResponse = json_decode($responseData, true);
        $debugInfo[] = "Raw Response: " . substr($responseData, 0, 300) . "...";
        
        if (isset($decodedResponse['status']) && $decodedResponse['status'] == true) {
            $transactionData = $decodedResponse['data'];
            $debugInfo[] = "Paystack status: " . $transactionData['status'];
            log_message("Paystack response received, status: " . $transactionData['status']);
            
            // Check if the payment was successful according to Paystack
            if ($transactionData['status'] == 'success') {
                // Payment is successful according to Paystack
                $paymentAmount = $transactionData['amount'] / 100;
                $debugInfo[] = "Payment amount: " . $paymentAmount;
                log_message("Payment amount: " . $paymentAmount);
                
                // Prepare the reference for database query (prevent SQL injection)
                $safeReference = mysqli_real_escape_string($con, $reference);
                
                // Check database connection
                if (!$con) {
                    $debugInfo[] = "Database connection error: " . mysqli_connect_error();
                    $paymentMessage = "Processing your payment...";
                    return false;
                } else {
                    $debugInfo[] = "Database connected successfully";
                    
                    // First check if this transaction exists in our database
                    $query = "SELECT * FROM booking_order WHERE order_id = '$safeReference'";
                    $debugInfo[] = "Query: " . $query;
                    log_message("Executing query: " . $query);
                    $result = mysqli_query($con, $query);
                    
                    if (!$result) {
                        $debugInfo[] = "Query error: " . mysqli_error($con);
                        $paymentMessage = "Processing your payment...";
                        return false;
                    } else if (mysqli_num_rows($result) == 0) {
                        $debugInfo[] = "No record found for reference: " . $safeReference;
                        $paymentMessage = "Processing your payment...";
                        return false;
                    } else {
                        $bookingData = mysqli_fetch_assoc($result);
                        $bookingId = $bookingData['booking_id'];
                        $debugInfo[] = "Booking found with ID: " . $bookingId;
                        $debugInfo[] = "Current booking_status: " . $bookingData['booking_status'];
                        $debugInfo[] = "Current payment_status: " . $bookingData['payment_status'];
                        
                        // Check if payment status is already 'success'
                        if ($bookingData['payment_status'] == 'success' && $bookingData['booking_status'] == 'booked') {
                            // Payment already verified
                            $paymentVerified = true;
                            $paymentMessage = "Payment was already verified successfully.";
                            $debugInfo[] = "Payment already verified";
                            log_message("Payment already verified");
                            return true;
                        } else {
                            // Simple update - just set the payment status to success and booking status to booked
                            $upd_query = "UPDATE booking_order SET 
                                payment_status = 'success',
                                booking_status = 'booked' 
                                WHERE booking_id = $bookingId";
                            
                            $debugInfo[] = "Update query: " . $upd_query;
                            log_message("Executing update query");
                            
                            $update_result = mysqli_query($con, $upd_query);
                            if ($update_result) {
                                $debugInfo[] = "Update successful. Affected rows: " . mysqli_affected_rows($con);
                                $paymentVerified = true;
                                $paymentMessage = "Payment verified successfully.";
                                log_message("Database updated successfully");
                                return true;
                            } else {
                                $debugInfo[] = "Update failed: " . mysqli_error($con);
                                $paymentMessage = "Processing your payment...";
                                log_message("Database update failed: " . mysqli_error($con));
                                return false;
                            }
                        }
                    }
                }
            } else {
                $paymentMessage = "Processing your payment...";
                $debugInfo[] = "Payment failed: " . $transactionData['gateway_response'];
                log_message("Payment failed: " . $transactionData['gateway_response']);
                return false;
            }
        } else {
            $errorMsg = $decodedResponse['message'] ?? "Unknown error";
            $paymentMessage = "Processing your payment...";
            $debugInfo[] = "Error verifying payment: " . $errorMsg;
            log_message("Error verifying payment: " . $errorMsg);
            return false;
        }
    }
    return false;
}

// Process request if we have a reference
if (isset($_GET['reference'])) {
    $reference = $_GET['reference'];
    verifyPayment($reference, $con);
} else {
    $paymentMessage = "Processing your payment...";
    $debugInfo[] = "No reference parameter found in request";
    log_message("No reference parameter found in request");
    // Only redirect in non-debug mode
    if (!$debug_mode) {
        redirect('../index.php');
    }
}

// In debug mode, don't redirect - show the debug info
if (!$debug_mode && !$paymentVerified && $paymentMessage !== "Processing payment...") {
    log_message("Payment verification in process. Showing processing page.");
    // No redirection - show the processing page
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $paymentVerified ? 'Payment Success' : 'Processing Payment'; ?> - Ebitare Hotel</title>
    <style>
        /* Basic Reset */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
            color: #333;
            line-height: 1.6;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }

        .container {
            background-color: #fff;
            padding: 30px;
            text-align: center;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 800px;
        }

        h1 {
            color: <?php echo $paymentVerified ? '#28a745' : '#007bff'; ?>;
            font-size: 2rem;
            margin-bottom: 20px;
        }

        .info {
            margin: 20px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            text-align: left;
        }

        .info p {
            margin-bottom: 10px;
            font-size: 1rem;
        }

        p {
            font-size: 1.2rem;
            margin-bottom: 30px;
        }

        .btn {
            padding: 12px 30px;
            font-size: 1rem;
            background-color: #007bff;
            color: #fff;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 20px;
            margin-right: 10px;
        }

        .btn-refresh {
            background-color: #28a745;
        }

        .btn:hover {
            background-color: #0056b3;
        }

        .btn-refresh:hover {
            background-color: #218838;
        }

        .spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            width: 36px;
            height: 36px;
            border-radius: 50%;
            border-left-color: #007bff;
            display: inline-block;
            animation: spin 1s linear infinite;
            margin-right: 10px;
            vertical-align: middle;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .processing-message {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }

        .debug-info {
            text-align: left;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 30px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
        }

        /* Responsive design */
        @media (max-width: 600px) {
            h1 {
                font-size: 1.5rem;
            }
            .container {
                padding: 15px;
                width: 95%;
            }
        }
    </style>
    <?php if (!$paymentVerified): ?>
    <script>
        // Auto-refresh the page every 5 seconds to check for payment status updates
        setTimeout(function() {
            <?php if (isset($paymentRef)): ?>
            window.location.href = "?reference=<?php echo htmlspecialchars($paymentRef); ?>";
            <?php else: ?>
            window.location.reload();
            <?php endif; ?>
        }, 5000);
    </script>
    <?php endif; ?>
</head>
<body>

<div class="container">
    <?php if ($paymentVerified): ?>
        <h1>Payment Successful!</h1>
        <p>Thank you for your booking. Your transaction was successfully processed.</p>
        
        <div class="info">
            <p><strong>Payment Amount:</strong> <?php echo number_format($paymentAmount, 2); ?> NGN</p>
            <p><strong>Reference Number:</strong> <?php echo htmlspecialchars($paymentRef); ?></p>
            <p><strong>Status:</strong> Confirmed</p>
            <p><strong>Booking Status:</strong> Booked</p>
            <?php if($bookingId): ?>
            <p><strong>Booking ID:</strong> <?php echo $bookingId; ?></p>
            <?php endif; ?>
        </div>
        
        <p>For a smooth check-in, please visit your booking page to download your receipt and show it at the front desk.</p>
        <a href="../bookings.php" class="btn">Return to Home</a>
    <?php else: ?>
        <h1>Processing Payment</h1>
        
        <div class="processing-message">
            <div class="spinner"></div>
            <p>Please wait while we process your payment...</p>
        </div>
        
        <?php if (isset($paymentRef)): ?>
        <div class="info">
            <p><strong>Reference Number:</strong> <?php echo htmlspecialchars($paymentRef); ?></p>
            <?php if($bookingId): ?>
            <p><strong>Booking ID:</strong> <?php echo $bookingId; ?></p>
            <?php endif; ?>
            <p><strong>Status:</strong> Processing</p>
        </div>
        
        <p>Your payment is being processed. This page will automatically refresh to check for updates.</p>
        
        <!-- Continue to booking button -->
        <a href="../bookings.php" class="btn btn-refresh">Continue to Booking</a>
        <?php endif; ?>
        
        <a href="../bookings.php" class="btn">Return to Home</a>
    <?php endif; ?>
    
    <?php if ($debug_mode): ?>
    <div class="debug-info">
        <h3>Debug Information:</h3>
        <pre><?php 
            echo "Payment Verified: " . ($paymentVerified ? "Yes" : "No") . "\n";
            echo "Payment Message: " . $paymentMessage . "\n\n";
            echo "Debug Log:\n";
            echo implode("\n", $debugInfo);
        ?></pre>
    </div>
    <?php endif; ?>
</div>

</body>
</html>