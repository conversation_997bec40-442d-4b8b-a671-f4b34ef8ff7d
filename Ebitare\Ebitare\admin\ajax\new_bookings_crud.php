<?php
require('../inc/db_config.php');
require ('../inc/essentials.php');
adminLogin();

if (isset($_POST['get_bookings'])) {

    $frm_data  = filteration($_POST);

    $query = "SELECT bo.*, bd.* FROM `booking_order` bo
              INNER JOIN `booking_details` bd ON bo.booking_id = bd.booking_id
              WHERE 
                (bo.order_id LIKE ? OR bd.customer_phone LIKE ? OR bd.customer_name LIKE ? OR bd.customer_email LIKE ?)
                AND bo.booking_status = ? AND bo.arrival = ?
              ORDER BY bo.booking_id ASC";

    $values = [
        "%{$frm_data['search']}%",
        "%{$frm_data['search']}%",
        "%{$frm_data['search']}%",
        "%{$frm_data['search']}%",
        "booked",
        0
    ];

    $res = select($query, $values, 'sssssi');

    $i = 1;
    $table_data = "";

    if (mysqli_num_rows($res) == 0) {
        echo "<br> No Data Found</br>";
        exit();
    }

    while ($data = mysqli_fetch_assoc($res)) {
        $date = date("d-m-Y", strtotime($data['datentime']));
        $checkin = date("d-m-Y", strtotime($data['check_in']));
        $checkout = date("d-m-Y", strtotime($data['check_out']));

        $table_data .= "
            <tr>
                <td>$i</td>
                <td>
                    <span class='badge bg-primary'>
                        Order ID : {$data['order_id']}
                    </span>
                    <br>
                    <b>Name: </b> {$data['customer_name']}
                    <br>
                    <b>Email: </b> {$data['customer_email']}
                    <br>
                    <b>Phone No: </b> {$data['customer_phone']}
                </td>
                <td>
                    <b>Room: </b> {$data['room_name']}
                    <br>
                </td>
                <td>
                    <b>Check-in: </b> $checkin
                    <br>
                    <b>Check-out: </b> $checkout
                    <br>
                    <b>Paid: ₦</b>{$data['trans_amt']}
                    <br>
                    <b>Date: </b> $date
                    <br>
                </td>
                <td>
                    <button type='button' onclick='assign_room({$data['booking_id']})' class='btn text-white btn-sm-bold fw-bold custom-bg shadow-none' data-bs-toggle='modal' data-bs-target='#assign-room'>
                        <i class='bi bi-check2-square'></i> Assign Room
                    </button>
                    <br>
                    <button type='button' onclick='cancel_booking({$data['booking_id']})' class='btn mt-2 btn-outline-danger btn-sm-bold fw-bold shadow-none'>
                        <i class='bi bi-trash'></i> Cancel Booking
                    </button>
                </td>
            </tr>
        ";

        $i++;
    }

    echo $table_data;
}


if(isset($_POST['assign_room']))
{
    $frm_data = filteration($_POST);

    $query = "UPDATE `booking_order` bo INNER JOIN `booking_details` bd
    ON bo.booking_id  = bd.booking_id
    SET bo.arrival = ?, bo.rate_review=? ,bd.room_no = ?
    WHERE bo.booking_id=?";

    $values = [1,0, $frm_data['room_no'], $frm_data['booking_id']];

    $res = update($query,$values, 'iisi');

    echo ($res==2) ? 1:0;
}









if (isset($_POST['cancel_booking'])) {
    $frm_data = filteration($_POST);


    $query = "UPDATE `booking_order` SET `booking_status` = ?, `refund`=? WHERE `booking_id`=?";

    $values = ['cancelled', 0, $frm_data['booking_id']];
    $res = update($query,$values,'ssi');

    echo ($res > 0) ? 1 : 0;
}




?>
