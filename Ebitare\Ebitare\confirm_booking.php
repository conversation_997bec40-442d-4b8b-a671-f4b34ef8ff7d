<?php
// Check if the HTTP_REFERER is set and matches your domain
// $referer = $_SERVER['HTTP_REFERER'] ?? '';  // Get the referer

// // Define your site's base URL
// $base_url = 'https://127.0.0.1/h';  // Replace with your actual domain

// // If the referer is empty or doesn't match your base URL, stop page loading
// if (empty($referer) || strpos($referer, $base_url) === false) {
//     // Redirect or display an error message
//     header("Location: $base_url/"); // Redirect to the homepage or another page
//     exit; // Stop further execution of the script
// }
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="Ebitare">
    <title>EBITARE - Confirm Booking</title>
    
    <?php require('inc/links.php'); ?>
    
    <!-- Favicons-->
    

    <!-- GOOGLE WEB FONT-->
    <link rel="preconnect" href="https://fonts.googleapis.com/">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Caveat:wght@400;500&amp;family=Montserrat:wght@300;400;500;600;700&amp;display=swap" rel="stylesheet">

    <!-- BASE CSS -->
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <link href="css/vendors.min.css" rel="stylesheet">

    <!-- YOUR CUSTOM CSS -->
    <link href="css/custom.css" rel="stylesheet">
</head>

<body>
<body> 

<div id="preloader">
    <div data-loader="circle-side"></div>
</div><!-- /Page Preload -->

<div class="layer"></div><!-- Opacity Mask -->

<header class="reveal_header">
    <div class="container-fluid">
        <div class="row align-items-center">
             <div class="col-6">
                    <a href="#" class="logo_normal"><img src="img/logo.png" width="135" height="45" alt=""></a>
                    <a href="index-3.php" class="logo_sticky"><img src="img/logo_sticky.png" width="135" height="45" alt=""></a>
            </div>
            <div class="col-6">
                <nav>
                    <ul>
                                                   <div class="hamburger_2 open_close_nav_panel">
                                <div class="hamburger__box">
                                    <div class="hamburger__inner"></div>
                                </div>
                            </div>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div><!-- /container -->
</header><!-- /Header -->

<div class="nav_panel">
    <a href="#0" class="closebt open_close_nav_panel"><i class="bi bi-x"></i></a>
    <div class="logo_panel"><img src="img/logo_sticky.png" width="135" height="45" alt=""></div>
    <div class="sidebar-navigation">
        <nav>
            <ul class="level-1">
                <li class=""><a href="index-3.php">Home</a>
                </li>
                <li class=""><a href="rooms.php">Rooms & Suites</a>
                </li>
               
                
                <li><a href="about.php">About</a></li>
                <li><a href="contacts.php">Contact Us</a></li>
                <li class="parent"><a href="#0">Explore</a>
                    <ul class="level-2">
                        <li class="back"><a href="#0">Back</a></li>
                
                        <li><a href="gallery.php">Ebitare Gallery</a></li>
                       
                    </ul>
                </li>
            </ul>
            <div class="panel_footer">
                <div class="phone_element"><a href="tel://423424234"><i class="bi bi-telephone"></i><span><em>Info and bookings</em>+<?php echo $contact_r['pn1']?></span></a></div>
            </div>
            <!-- /panel_footer -->
        </nav>
    </div>
    <!-- /sidebar-navigation -->
</div>
<!-- /nav_panel -->

   

    <?php
    /* check room id is present or not
    shutdown mode is active or not
    User is logged in or not*/

    if (!isset($_GET['id']) || $settings_r['shutdown'] == true) {
        redirect('rooms.php');  // Redirect if 'id' is not set in the URL
    }

    // ALSO TO ALLOW ONLY LOGGED IN USERS TO BOOK ROOMS UNCOMMENT THIS

    // if(!isset($_SESSION['login']) && $_SESSION['login'] === true){
    //     redirect('rooms.php');
    //     echo 'You are not logged so you cannot book room';
    // }
   
    // filter and get and filter data
    $data = filteration($_GET);  // Sanitize or validate the input using 'filteration()'

    // Correct SQL query, assuming you want to check for rooms where 'removed' = 0 (active rooms)
    $room_res = select("SELECT * FROM `rooms` WHERE `id`=? AND `removed`=?", [$data['id'], 0], 'ii');

    // If no room is found, redirect to rooms.php
    if (mysqli_num_rows($room_res) == 0) {
        redirect('rooms.php');
    }

    // Fetch the room data
    $room_data = mysqli_fetch_assoc($room_res);

    $_SESSION['room'] = [
        "id" => $room_data['id'],          // Room ID
        "name" => $room_data['name'],      // Room name
        "price" => $room_data['price'],    // Room price
        "payment" => null,                 // Initialize payment as null
        "available" => false,              // Initialize availability as false
    ];

    // Execute the query and bind the parameter
    if (isset($_SESSION['uId'])) {
        // Fetch the user data from the database if 'uId' exists in the session
        $user_res = select("SELECT * FROM `user_cred` WHERE `id`= ? LIMIT 1", [$_SESSION['uId']], "i");
    
        // Check if the query returned any results
        if ($user_res && mysqli_num_rows($user_res) > 0) {
            // Fetch the user data if it exists
            $user_data = mysqli_fetch_assoc($user_res);
            // Now you can use $user_data for further logic (e.g., display user info)
        } else {
            // If no user found, you can set $user_data as empty or handle it in a way that suits your app
            $user_data = [];    
        }
    } else {
        $user_data = [];
    } 
    ?>

    <main>
        <div class="hero full-height jarallax" data-jarallax data-speed="0.2">
            <?php
            $room_img = ROOMS_IMG_PATH . "thumbnail.jpg";
            $thumb_q = mysqli_query($con, "SELECT * FROM `room_images` WHERE `room_id` = {$room_data['id']} LIMIT 1");
            
            if (mysqli_num_rows($thumb_q) > 0) {
                $thumb_res = mysqli_fetch_assoc($thumb_q);
                $room_img = ROOMS_IMG_PATH . $thumb_res['image'];
            }
            ?>
            
            <img class="jarallax-img kenburns" src="<?php echo $room_img; ?>" alt="<?php echo $room_data['name']; ?>">
            <div class="wrapper opacity-mask d-flex align-items-center text-center animate_hero" data-opacity-mask="rgba(0, 0, 0, 0.5)">
                <div class="container">
                    <div class="row justify-content-center">
                        <div class="col-lg-8">
                            <small class="slide-animated one">Hospitality at its peak</small>
                            <h1 class="slide-animated two">Confirm Booking</h1>
                            <p class="slide-animated three"><?php echo $room_data['name']; ?></p>
                        </div>
                    </div>
                </div>
                <div class="mouse_wp slide-animated four">
                    <a href="#first_section" class="btn_explore">
                        <div class="mouse"></div>
                    </a>
                </div>
                <!-- / mouse -->
            </div>
        </div>
        <!-- /Background Img Parallax -->

        <div class="bg_white" id="first_section">
            <div class="container margin_120_95">
                <div class="row justify-content-between">
                    <div class="col-lg-6">
                        <div class="title">
                            <small>Booking Details</small>
                            <h2>Confirm Your Stay</h2>
                        </div>
                        <?php 
                        $room_thumb = ROOMS_IMG_PATH . "thumbnail.jpg";
                        $thumb_q = mysqli_query($con, "SELECT * FROM `room_images`
                        WHERE `room_id` = {$room_data['id']}
                        AND `thumb` = '1'");

                        if(mysqli_num_rows($thumb_q) > 0){
                            $thumb_res = mysqli_fetch_assoc($thumb_q);
                            $room_thumb = ROOMS_IMG_PATH . $thumb_res['image'];
                        }

                        echo<<<data
                            <div class="room_facilities_list">
                                <img src="$room_thumb" class="img-fluid rounded mb-4">
                                <h4>$room_data[name]</h4>
                                <h6 class="room-price mt-3 mb-4">₦$room_data[price] per night</h6>
                            </div>
                        data;             
                        ?>
                    </div>
                    <div class="col-lg-5">
                        <form action="pay_now.php" method="POST" id="booking_form" class="box_style_1">
                            <div class="title mb-4">
                                <h3>Guest Information</h3>
                            </div>
                            <input type="hidden" name="user_id" value="<?php echo isset($_SESSION['uId']) ? $_SESSION['uId'] : ''; ?>">

                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label class="form-label">Full name</label>
                                    <input type="text" 
                                        value="<?php echo isset($user_data['name']) ? $user_data['name'] : ''; ?>" 
                                        class="form-control shadow-none" 
                                        name="name" 
                                        required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Email</label>
                                    <input type="email" 
                                        value="<?php echo isset($user_data['email']) ? $user_data['email'] : ''; ?>" 
                                        class="form-control shadow-none" 
                                        name="email" 
                                        required>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Phone No.</label>
                                    <input type="number" 
                                        value="<?php echo isset($user_data['phone']) ? $user_data['phone'] : ''; ?>" 
                                        class="form-control shadow-none" 
                                        name="phone" 
                                        required>
                                </div>

                                <div class="col-md-12 mb-3">
                                    <label class="form-label">Address</label>
                                    <textarea name="address" 
                                            class="form-control shadow-none" 
                                            rows="1" 
                                            required><?php echo isset($user_data['address']) ? $user_data['address'] : ''; ?></textarea>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Check-in</label>
                                    <input type="date" 
                                        class="form-control shadow-none" 
                                        name="checkin"
                                        id="checkin"
                                        onchange="check_availability()" 
                                        required>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Check-out</label>
                                    <input type="date"  
                                        class="form-control shadow-none" 
                                        name="checkout"
                                        id="checkout" 
                                        onchange="check_availability()" 
                                        required>
                                </div>

                                <div class="col-md-12 mb-3">
                                    <div class="spinner-border text-info mb-3 d-none" id="info_loader" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <h6 class="mb-4 text-danger" id="pay_info">Please select your check-in and check-out date!</h6>
                                    <button name="pay_now" class="btn_1 w-100" disabled>Pay Now</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <!-- /row -->
            </div>
            <!-- /container -->
        </div>
        <!-- /bg_white -->
    </main>

    <footer class="revealed">
        <div class="footer_bg">
            <div class="gradient_over"></div>
            <div class="background-image" data-background="url(img/rooms/3.jpg)"></div>
        </div>
        <div class="container">
            <div class="row move_content">
                <div class="col-lg-4 col-md-12">
                    <h5>Contacts</h5>
                    <ul>
                        <li><?php echo $contact_r['address'] ?? 'Hotel Address' ?><br><br></li>
                        <li><strong><a href="mailto:<?php echo $contact_r['email'] ?? '<EMAIL>' ?>"><?php echo $contact_r['email'] ?? '<EMAIL>' ?></a></strong></li>
                        <li><strong><a href="tel:<?php echo $contact_r['pn1'] ?? '+1234567890' ?>">+<?php echo $contact_r['pn1'] ?? '1234567890' ?></a></strong></li>
                    </ul>
                    <div class="social">
                        <ul>
                            <li><a href="<?php echo $contact_r['insta'] ?? '#' ?>"><i class="bi bi-instagram"></i></a></li>
                            <li><a href="<?php echo $contact_r['tw'] ?? '#' ?>"><i class="bi bi-twitter-x"></i></a></li>
                            <li><a href="<?php echo $contact_r['fb'] ?? '#' ?>"><i class="bi bi-facebook"></i></a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 ms-lg-auto">
                    <h5>Explore</h5>
                    <div class="footer_links">
                        <ul>
                            <li><a href="index.php">Home</a></li>
                            <li><a href="about.php">About Us</a></li>
                            <li><a href="rooms.php">Rooms &amp; Suites</a></li>
                            <li><a href="contacts.php">Contacts</a></li>
                            <li><a href="terms.php">Terms and Conditions</a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div id="newsletter">
                        <h5>Newsletter</h5>
                        <div id="message-newsletter"></div>
                        <form method="post" action="assets/newsletter.php" name="newsletter_form" id="newsletter_form">
                            <div class="form-group">
                                <input type="email" name="email_newsletter" id="email_newsletter" class="form-control" placeholder="Your email">
                                <button type="submit" id="submit-newsletter"><i class="bi bi-send"></i></button>
                            </div>
                        </form>
                        <p>Receive latest offers and promos without spam. You can cancel anytime.</p>
                    </div>
                </div>
            </div>
            <!--/row-->
        </div>
        <!--/container-->
        <div class="copy">
            <div class="container">
                © Ebitare Hotel - <?php echo date('Y'); ?>
            </div>
        </div>
    </footer>
    <!-- /footer -->
   
    <div class="progress-wrap">
        <svg class="progress-circle svg-content" width="100%" height="100%" viewBox="-1 -1 102 102">
            <path d="M50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98"/>
        </svg>
    </div>
    <!-- /back to top -->

    <!-- COMMON SCRIPTS -->
    <script src="js/common_scripts.js"></script>
    <script src="js/common_functions.js"></script>
    <script src="js/datepicker_inline.js"></script>
    <script src="phpmailer/validate.js"></script>
    
    <script>
        let booking_form = document.getElementById('booking_form');
        let info_loader = document.getElementById('info_loader');
        let pay_info = document.getElementById('pay_info');

        function check_availability()
        {
            let checkin_val = booking_form.elements['checkin'].value;
            let checkout_val = booking_form.elements['checkout'].value;
        
            booking_form.elements['pay_now'].setAttribute('disabled', true);

            if(checkin_val!='' && checkout_val!=''){
                pay_info.classList.add('d-none');
                pay_info.classList.replace('text-dark','text-danger');
                info_loader.classList.remove('d-none');

                let data = new FormData();

                data.append('check_availability', '');
                data.append('check_in', checkin_val);
                data.append('check_out', checkout_val);

                let xhr = new XMLHttpRequest();
                xhr.open("POST", "ajax/confirm_booking_crud.php?t=" + new Date().getTime(), true);

                xhr.onload = function() {
                   let data = JSON.parse(this.responseText);

                    // Trim whitespace from the response text (if any)
                    var response = this.responseText.trim();

                    if(data.status == 'check_in_out_equal'){
                        pay_info.innerText = "You cannot check-out on the same day!";
                    }
                    else if(data.status == 'check_out_earlier'){
                        pay_info.innerText = "Check-out date is earlier than Check-in date!";
                    }
                    else if(data.status == 'check_in_earlier'){
                        pay_info.innerText = "Check-in date is earlier than today's date!";
                    }
                    else if(data.status == 'unavailable'){
                        pay_info.innerText = "Room not available for this check-in date!";
                    }
                    else{
                        pay_info.innerHTML = "<br>No. of Days: " + data.days + "<br><br> Total Amount to Pay: ₦" + data.payment;
                        pay_info.classList.replace('text-danger','text-dark');
                        booking_form.elements['pay_now'].removeAttribute('disabled');
                        
                        // Add hidden input fields for the check-in and check-out dates
                        if(!document.getElementById('hidden_checkin')){
                            let hidden_checkin = document.createElement('input');
                            hidden_checkin.type = 'hidden';
                            hidden_checkin.name = 'checkin';
                            hidden_checkin.id = 'hidden_checkin';
                            hidden_checkin.value = checkin_val;
                            booking_form.appendChild(hidden_checkin);
                            
                            let hidden_checkout = document.createElement('input');
                            hidden_checkout.type = 'hidden';
                            hidden_checkout.name = 'checkout';
                            hidden_checkout.id = 'hidden_checkout';
                            hidden_checkout.value = checkout_val;
                            booking_form.appendChild(hidden_checkout);
                            
                            // Also store the payment amount for the session
                            let hidden_payment = document.createElement('input');
                            hidden_payment.type = 'hidden';
                            hidden_payment.name = 'payment';
                            hidden_payment.id = 'hidden_payment';
                            hidden_payment.value = data.payment;
                            booking_form.appendChild(hidden_payment);
                        } else {
                            document.getElementById('hidden_checkin').value = checkin_val;
                            document.getElementById('hidden_checkout').value = checkout_val;
                            document.getElementById('hidden_payment').value = data.payment;
                        }
                    }

                    pay_info.classList.remove('d-none');
                    info_loader.classList.add('d-none');                    
                }

                xhr.send(data);
            }
        }
    </script>
</body>
</html>