<?php
   require ('inc/db_config.php');
   require ('inc/essentials.php');


   require('inc/links.php');
   
   session_start();
   session_regenerate_id(true);
   if((isset($_SESSION['adminLogin']) && $_SESSION['adminLogin']==true)){
     redirect('dashboard.php');
   }

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ADMIN-LOGIN</title>
    <?php require('inc/links.php');?>

    <style>
      div.login-form{
        position:absolute;
        top:50%;
        left: 50%;
        transform:translate(-50%, -50%);
        width:400px;

      }
      </style>

</head>
<body class="bg-light">
   <div class="login-form text-center rounded bg-white shadow overflow-hidden">
    <form method="POST">
        <h4 class="bg-dark text-white py-3"> ADMIN LOGIN PANEL </h4>
        <div class="p-4">
          <div class="mb-3">
              <input name="admin_name"  required type="text" class="form-control shadow-none text-center" placeholder="Admin Name" id="exampleInputEmail1" aria-describedby="emailHelp">
            </div>
            <div class="mb-3">
              <input name="admin_pass" required  type="password" class="form-control shadow-none text-center" placeholder="Password" id="exampleInputPassword1" aria-describedby="passwordHelp">
            </div>
            <button name="login"  type="sumbit" class="btn text-white custom-bg shadow-none">LOGIN</button>
    </div>
    </form>
  </div>


  <?php    
    if (isset($_POST['login'])) 
    {
      $frm_data = filteration($_POST);  // Sanitize input

      // Correct SQL query with placeholders
      $query = "SELECT * FROM `admin_cred` WHERE `admin_name` = ? AND `admin_pass` = ?";

      // Prepare the values to be bound to the query
      $values = [$frm_data['admin_name'],$frm_data['admin_pass']];

      // Assuming the 'select' function returns results
      $res = select($query, $values, "ss");
      if($res->num_rows==1){
        $row = mysqli_fetch_assoc($res);
        $_SESSION['adminLogin'] = true;
        $_SESSION['adminId'] = $row['sr_no'];
        // Redirect to another page after successful login
        redirect('dashboard.php');
      }  
      else {
        alert('error','Incorrect Name or Password');
      }
      
    }
  ?>




  <?php require('inc/script.php');?>
</body>
</html>