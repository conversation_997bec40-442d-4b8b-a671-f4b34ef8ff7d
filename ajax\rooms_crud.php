<?php
require('../admin/inc/db_config.php');
require('../admin/inc/essentials.php');
date_default_timezone_set("Africa/Lagos");

session_start();

if (isset($_GET['fetch_rooms'])) {

    $chk_avail = json_decode($_GET['chk_avail'], true);

    if (!empty($chk_avail['checkin']) && !empty($chk_avail['checkout'])) {
        $today_date = new DateTime(date("Y-m-d"));
        $checkin_date = new DateTime($chk_avail['checkin']);
        $checkout_date = new DateTime($chk_avail['checkout']);

        if ($checkin_date == $checkout_date) {
            echo "<h3 class='text-center text-danger'>Check-in and Check-out are the same date</h3>";
            exit;
        } elseif ($checkout_date < $checkin_date) {
            echo "<h3 class='text-center text-danger'>Check-out date is earlier than Check-in date!</h3>";
            exit;
        } elseif ($checkin_date < $today_date) {
            echo "<h3 class='text-center text-danger'>Check-in date is earlier than today!</h3>";
            exit;
        }
    }

    $guests = json_decode($_GET['guests'], true);

    $adult = ($guests['adult'] != '') ? $guests['adult'] : 0;
    $children = ($guests['children'] != '') ? $guests['children'] : 0;
    
    $count_rooms = 0;
    $output = "";

    $settings_q = "SELECT * FROM `settings` WHERE `sr_no`=1";
    $settings_r = mysqli_fetch_assoc(mysqli_query($con, $settings_q));

    $room_res = select("SELECT * FROM `rooms` WHERE `adult`>=? AND `children`>=? AND`status`=? AND `removed`=?", [$adult,$children, 1, 0], 'iiii');

    while ($room_data = mysqli_fetch_assoc($room_res)) {

        // Check availability
        if (!empty($chk_avail['checkin']) && !empty($chk_avail['checkout'])) {

            $tb_query = "SELECT COUNT(*) AS `total_bookings` FROM `booking_order`
                         WHERE booking_status=? AND room_id=? 
                         AND check_out > ? AND check_in < ?";
            $values = ['booked', $room_data['id'], $chk_avail['checkin'], $chk_avail['checkout']];
            $tb_result = select($tb_query, $values, 'siss');

            if (!$tb_result) {
                echo json_encode(["status" => "query_error"]);
                exit;
            }

            $tb_fetch = mysqli_fetch_assoc($tb_result);

            if (($room_data['quantity'] - $tb_fetch['total_bookings']) <= 0) {
                continue; // Skip if fully booked
            }
        }

        // Get features
        $fea_q = mysqli_query($con, "SELECT f.name FROM `features` f 
            INNER JOIN `room_features` rfea ON f.id = rfea.features_id 
            WHERE rfea.room_id = '{$room_data['id']}'");
        $features_data = "";
        while ($fea_row = mysqli_fetch_assoc($fea_q)) {
            $features_data .= "<span class='badge rounded-pill text-dark mb-2 text-wrap lh-base small'>{$fea_row['name']}</span> ";
        }

        // Get facilities
        $fac_q = mysqli_query($con, "SELECT f.name FROM `facilities` f 
            INNER JOIN `room_facilities` rfac ON f.id = rfac.facilities_id 
            WHERE rfac.room_id = '{$room_data['id']}'");
        $facilities_data = "";
        while ($fac_row = mysqli_fetch_assoc($fac_q)) {
            $facilities_data .= "<span class='badge rounded-pill text-dark mb-2 text-wrap lh-base small'>{$fac_row['name']}</span> ";
        }

        // Get room images for carousel
        $room_img_q = mysqli_query($con, "SELECT * FROM `room_images` 
            WHERE `room_id` = {$room_data['id']} ORDER BY `sr_no` ASC");
        
        $carousel_items = "";
        $room_thumb = ROOMS_IMG_PATH . "thumbnail.jpg"; // Default thumbnail
        
        while ($img_row = mysqli_fetch_assoc($room_img_q)) {
            $img_path = ROOMS_IMG_PATH . $img_row['image'];
            $carousel_items .= "<div class='item'>
                <a data-fslightbox='gallery_{$room_data['id']}' data-type='image' href='{$img_path}'>
                    <img src='{$img_path}' alt='' style='height: 500px; object-fit: cover;'>
                </a>
            </div>";
            
            // Set thumbnail
            if ($img_row['thumb'] == '1') {
                $room_thumb = $img_path;
            }
        }
        
        // If no images found, just use the thumbnail
        if (empty($carousel_items)) {
            $carousel_items = "<div class='item'>
                <a data-fslightbox='gallery_{$room_data['id']}' data-type='image' href='{$room_thumb}'>
                    <img src='{$room_thumb}' alt='' style='height: 650px; object-fit: cover;'>
                </a>
            </div>";
        }

        // Book button logic
        $book_btn = "";
          if (!isset($settings_r['shutdown']) || !$settings_r['shutdown']) {
              $book_btn = "<a href='confirm_booking.php?id={$room_data['id']}&checkin={$chk_avail['checkin']}&checkout={$chk_avail['checkout']}&adult={$adult}&children={$children}' class='btn_4 learn-more'>
                  <span class='circle'>
                      <span class='d-flex justify-item-end align-item-end icon arrow px-3 mb-3'></span>
                  </span>
                  <span class='button-text'>Book Now</span>
              </a>";
          }

        // Build output with new styling - changing the proportion to make images bigger and details smaller
        $output .= "
        <div class='row_list_version_2" . ($count_rooms % 2 == 1 ? " inverted" : "") . "'>
            <div class='row g-0 align-items-center'>
                <div class='col-xl-9" . ($count_rooms % 2 == 1 ? " order-xl-2" : "") . "'>
                    <div class='owl-carousel owl-theme carousel_item_1 kenburns rounded-img'>
                        {$carousel_items}
                    </div>
                </div>
                <div class='col-xl-3" . ($count_rooms % 2 == 1 ? " order-xl-1" : "") . "'>
                    <div class='box_item_info py-3 px-3' data-jarallax-element='-25'>
                        <small>₦{$room_data['price']}/night</small>
                        <h3 class='fs-5'>{$room_data['name']}</h3>
                        <div class='facilities clearfix'>
                            <h6 class='fs-6 mb-1'>Features</h6>
                            <div class='small'>{$features_data}</div>
                            <h6 class='fs-6 mb-1 mt-2'>Facilities</h6>
                            <div class='small'>{$facilities_data}</div>
                            <h6 class='fs-6 mb-1 mt-2'>Guests</h6>
                            <ul class='ps-3 mb-2 small'>
                                <li><i class='bi bi-person'></i> {$room_data['adult']} Adults</li>
                                <li><i class='bi bi-person-heart'></i> {$room_data['children']} Children</li>
                            </ul>
                        </div>
                        <div class='box_item_footer d-flex align-items-center justify-content-between mt-2'>
                            {$book_btn}
                            <a href='room_detail.php?id={$room_data['id']}' class='animated_link'>
                                <strong>Details</strong>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>";

        $count_rooms++;
    }

    echo $count_rooms > 0 ? $output : "<h3 class='text-center text-danger'>No rooms to show!</h3>";
}
?>