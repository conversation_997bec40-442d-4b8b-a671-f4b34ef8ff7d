<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="Ebitare">
    <title>EBITARE - Room Details</title>
    
    <?php require('inc/links.php'); ?>
    
    <!-- Favicons-->
    
    <!-- GOOGLE WEB FONT-->
    <link rel="preconnect" href="https://fonts.googleapis.com/">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Caveat:wght@400;500&amp;family=Montserrat:wght@300;400;500;600;700&amp;display=swap" rel="stylesheet">

    <!-- BASE CSS -->
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <link href="css/vendors.min.css" rel="stylesheet">

    <!-- YOUR CUSTOM CSS -->
    <link href="css/custom.css" rel="stylesheet">
</head>

<body>
    <div id="preloader">
        <div data-loader="circle-side"></div>
    </div><!-- /Page Preload -->

    <div class="layer"></div><!-- Opacity Mask -->

    <?php
    if (!isset($_GET['id'])) {
        redirect('rooms.php');  // Redirect if 'id' is not set in the URL
    }

    $data = filteration($_GET);  // Sanitize or validate the input

    // Correct SQL query, assuming you want to check for rooms where 'removed' = 0 (active rooms)
    $room_res = select("SELECT * FROM `rooms` WHERE `id`=? AND `removed`=?", [$data['id'], 0], 'ii');

    // If no room is found, redirect to rooms.php
    if (mysqli_num_rows($room_res) == 0) {
        redirect('rooms.php');
    }

    // Fetch the room data
    $room_data = mysqli_fetch_assoc($room_res);
    ?>

    <header class="reveal_header">
        <div class="container-fluid">
            <div class="row align-items-center">
                 <div class="col-6">
                        <a href="#" class="logo_normal"><img src="img/logo.png" width="135" height="45" alt=""></a>
                        <a href="index-3.php" class="logo_sticky"><img src="img/logo_sticky.png" width="135" height="45" alt=""></a>
                </div>
                <div class="col-6">
                    <nav>
                        <ul>
                        <a id="bookNowBtn" href="#" class="btn_1 btn_scrollto">Book Now</a>

<script>
  // Example static values – change these as needed or make dynamic
  const checkin = "2025-04-22";
  const checkout = "2025-04-24";
  const adult = 2;
  const children = 1;

  // Get room ID from URL (e.g., room_detail.html?id=12)
  const urlParams = new URLSearchParams(window.location.search);
  const roomId = urlParams.get('id');

  // Only set the link if we found an ID
  if (roomId) {
    const bookLink = `confirm_booking.php?id=${roomId}&checkin=${checkin}&checkout=${checkout}&adult=${adult}&children=${children}`;
    document.getElementById("bookNowBtn").setAttribute("href", bookLink);
  } else {
    console.warn("Room ID not found in URL.");
  }
</script>
                            <li>
                               <div class="hamburger_2 open_close_nav_panel">
                                    <div class="hamburger__box">
                                        <div class="hamburger__inner"></div>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div><!-- /container -->
    </header><!-- /Header -->

    <div class="nav_panel">
        <a href="#0" class="closebt open_close_nav_panel"><i class="bi bi-x"></i></a>
        <div class="logo_panel"><img src="img/logo_sticky.png" width="135" height="45" alt=""></div>
        <div class="sidebar-navigation">
            <nav>
                <ul class="level-1">
                    <li class=""><a href="index-3.php">Home</a>
                    </li>
                    <li class=""><a href="rooms.php">Rooms & Suites</a>
                    </li>
                    <li><a href="about.php">About</a></li>
                    <li><a href="contacts.php">Contact Us</a></li>
                    <li class="parent"><a href="#0">Explore</a>
                        <ul class="level-2">
                            <li class="back"><a href="#0">Back</a></li>
                            <li><a href="gallery.php">Ebitare Gallery</a></li>
                        </ul>
                    </li>
                </ul>
                <div class="panel_footer">
                    <div class="phone_element"><a href="tel://<?php echo $contact_r['pn1']?>"><i class="bi bi-telephone"></i><span><em>Info and bookings</em>+<?php echo $contact_r['pn1']?></span></a></div>
                </div>
                <!-- /panel_footer -->
            </nav>
        </div>
        <!-- /sidebar-navigation -->
    </div>
    <!-- /nav_panel -->

    <main>
        <div class="hero full-height jarallax" data-jarallax data-speed="0.2">
            <?php
            $room_img = ROOMS_IMG_PATH . "thumbnail.jpg";
            $thumb_q = mysqli_query($con, "SELECT * FROM `room_images` WHERE `room_id` = {$room_data['id']} LIMIT 1");
            
            if (mysqli_num_rows($thumb_q) > 0) {
                $thumb_res = mysqli_fetch_assoc($thumb_q);
                $room_img = ROOMS_IMG_PATH . $thumb_res['image'];
            }
            ?>
            <img class="jarallax-img kenburns" src="<?php echo $room_img; ?>" alt="<?php echo $room_data['name']; ?>">
            <div class="wrapper opacity-mask d-flex align-items-center text-center animate_hero" data-opacity-mask="rgba(0, 0, 0, 0.5)">
                <div class="container">
                    <div class="row justify-content-center">
                        <div class="col-lg-8">
                            <small class="slide-animated one">Hospitality at its peak</small>
                            <h1 class="slide-animated two"><?php echo $room_data['name']; ?></h1>
                            <p class="slide-animated three">Exquisite furnishings for a cosy ambience</p>
                        </div>
                    </div>
                </div>
                <div class="mouse_wp slide-animated four">
                    <a href="#first_section" class="btn_explore">
                        <div class="mouse"></div>
                    </a>
                </div>
                <!-- / mouse -->
            </div>
        </div>
        <!-- /Background Img Parallax -->

        <div class="bg_white" id="first_section">
            <div class="container margin_120_95">
                <div class="row justify-content-between">
                    <div class="col-lg-4">
                        <div class="title">
                            <small>Luxury Experience</small>
                            <h2><?php echo $room_data['name']; ?></h2>
                            <h6 class="room-price mt-3 mb-4">₦<?php echo $room_data['price']; ?> per night</h6>
                            <?php
                            $book_btn = "";
                            if(!$settings_r['shutdown']) {
                                $login = 0;
                                if (isset($_SESSION['login']) && $_SESSION['login'] === true) {
                                    $login = 0;
                                }
                                $book_btn = "<button onclick='checkLoginToBook($login, {$room_data['id']})' class='btn_1'>Book Now</button>";
                            }
                            echo $book_btn;
                            ?>
                        </div>
                        <p><?php echo $room_data['description']; ?></p>
                    </div>
                    <div class="col-lg-6">
                        <div class="room_facilities_list">
                            <ul data-cues="slideInLeft">
                                <?php
                                // Fetch features
                                $fea_q = mysqli_query($con, "SELECT f.name FROM `features` f 
                                INNER JOIN `room_features` rfea ON f.id = rfea.features_id 
                                WHERE rfea.room_id = '{$room_data['id']}'");
                                
                                // Display features as list items with icons
                                while($fea_row = mysqli_fetch_assoc($fea_q)){
                                    echo '<li><i class="icon-hotel-double_bed_2"></i> ' . $fea_row['name'] . '</li>';
                                }
                                
                                // Fetch facilities
                                $fac_q = mysqli_query($con, "SELECT f.name FROM `facilities` f 
                                INNER JOIN `room_facilities` rfac ON f.id = rfac.facilities_id 
                                WHERE rfac.room_id = '{$room_data['id']}'");
                                
                                // Display facilities as list items with icons
                                while($fac_row = mysqli_fetch_assoc($fac_q)){
                                    echo '<li><i class="icon-hotel-safety_box"></i> ' . $fac_row['name'] . '</li>';
                                }
                                ?>
                                <li><i class="icon-hotel-disable"></i> <?php echo $room_data['adult']; ?> Adults</li>
                                <li><i class="icon-hotel-dog"></i> <?php echo $room_data['children']; ?> Children</li>
                                <li><i class="icon-hotel-bottle"></i> <?php echo $room_data['area']; ?> sq.ft</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <!-- /row -->
            </div>
            <!-- /container -->
        </div>
        <!-- /bg_white -->

        <div class="bg_white add_bottom_120">
            <div class="container-fluid p-lg-0">
                <div data-cues="zoomIn">
                    <div class="owl-carousel owl-theme carousel_item_centered kenburns rounded-img">
                        <?php
                        $thumb_q = mysqli_query($con, "SELECT * FROM `room_images` WHERE `room_id` = {$room_data['id']}");
                        
                        if (mysqli_num_rows($thumb_q) > 0) {
                            while ($thumb_res = mysqli_fetch_assoc($thumb_q)) {
                                $room_thumb = ROOMS_IMG_PATH . $thumb_res['image'];
                                echo "<div class='item'>
                                        <img src='$room_thumb' alt='{$room_data['name']}'>
                                    </div>";
                            }
                        } else {
                            echo "<div class='item'>
                                    <img src='$room_img' alt='{$room_data['name']}'>
                                </div>";
                        }
                        ?>
                    </div>
                </div>
                <div class="text-center mt-5">
                    <a class="btn_1 outline" data-fslightbox="gallery_1" data-type="image" href="<?php echo $room_img; ?>">FullScreen Gallery</a>
                    <?php
                    // Add additional gallery links for lightbox
                    $thumb_q = mysqli_query($con, "SELECT * FROM `room_images` WHERE `room_id` = {$room_data['id']}");
                    while ($thumb_res = mysqli_fetch_assoc($thumb_q)) {
                        $room_thumb = ROOMS_IMG_PATH . $thumb_res['image'];
                        echo "<a data-fslightbox='gallery_1' data-type='image' href='$room_thumb'></a>";
                    }
                    ?>
                </div>
            </div>
        </div>
        <!-- /bg_white -->

        <div class="container margin_120_95" id="reviews">
    <div class="row justify-content-between">
        <div class="col-lg-12">
            <div class="title text-center">
                <small>Testimonials</small>
                <h2>What Clients Say</h2>
            </div>
            
            <div class="carousel_testimonials owl-carousel owl-theme nav-dots-orizontal">
                <?php 
                $review_q = "SELECT rr.*, uc.name AS uname, uc.profile, r.name AS rname 
                             FROM `rating_review` rr
                             INNER JOIN `user_cred` uc ON rr.user_id = uc.id
                             INNER JOIN `rooms` r ON rr.room_id = r.id
                             WHERE rr.room_id = {$room_data['id']}
                             ORDER BY `sr_no` DESC LIMIT 6";

                $review_res = mysqli_query($con, $review_q);
                $img_path = USERS_IMG_PATH;

                if (mysqli_num_rows($review_res) == 0) {
                    echo '<div>No reviews yet for this room!</div>';
                } else {
                    while ($row = mysqli_fetch_assoc($review_res)) {
                        // Create star rating
                        $stars = "";
                        for ($i = 0; $i < $row['rating']; $i++) {
                            $stars .= "<i class='bi bi-star-fill text-warning'></i>";
                        }

                        // Display each review in the carousel
                        echo <<<HTML
                        <div>
                            <div class="box_overlay">
                                <div class="pic">
                                    <figure><img src="$img_path$row[profile]" alt="" class="img-circle"></figure>
                                    <h4>$row[uname]<small>$row[rname]</small></h4>
                                </div>
                                <div class="comment">
                                    "$row[review]"
                                </div>
                                <div class="rating">
                                    $stars
                                </div>
                            </div>
                        </div>
                        HTML;
                    }
                }
                ?>
            </div>
            <!-- End carousel_testimonials -->
            
            <p class="text-end mt-4"><a href="#0" class="btn_1">Leave a review</a></p>
        </div>
    </div>
</div>
<!-- /reviews -->

        <div class="bg_white">
            <div class="container margin_120_95">
                <div data-cue="slideInUp">
                    <div class="title">
                        <small>Ebitare Hotel</small>
                        <h2>Similar Rooms</h2>
                    </div>
                    <div class="row" data-cues="slideInUp" data-delay="800">
                        <?php
                        // Query to get similar rooms (example: rooms with similar price range or category)
                        $similar_rooms = select("SELECT * FROM `rooms` WHERE `id` != ? AND `removed` = ? ORDER BY RAND() LIMIT 3", [$room_data['id'], 0], 'ii');
                        
                        if(mysqli_num_rows($similar_rooms) > 0) {
                            while($similar_room = mysqli_fetch_assoc($similar_rooms)) {
                                // Get thumbnail for each similar room
                                $room_thumb = ROOMS_IMG_PATH . "thumbnail.jpg";
                                $thumb_q = mysqli_query($con, "SELECT * FROM `room_images` WHERE `room_id` = {$similar_room['id']} LIMIT 1");
                                
                                if (mysqli_num_rows($thumb_q) > 0) {
                                    $thumb_res = mysqli_fetch_assoc($thumb_q);
                                    $room_thumb = ROOMS_IMG_PATH . $thumb_res['image'];
                                }
                                
                                echo <<<HTML
                                <div class="col-xl-4 col-lg-6 col-md-6 col-sm-6">
                                    <a href="room_detail.php?id={$similar_room['id']}" class="box_cat_rooms">
                                        <figure>
                                            <div class="background-image" data-background="url({$room_thumb})"></div>
                                            <div class="info">
                                                <small>From ₦{$similar_room['price']}/night</small>
                                                <h3>{$similar_room['name']}</h3>
                                                <span>Read more</span>
                                            </div>
                                        </figure>
                                    </a>
                                </div>
                                HTML;
                            }
                        } else {
                            echo '<div class="col-12 text-center"><p>No similar rooms available at this time.</p></div>';
                        }
                        ?>
                    </div>
                    <!-- /row-->
                </div>
            </div>
        </div>
        <!-- /bg_white -->
    </main>

    <footer class="revealed">
        <div class="footer_bg">
            <div class="gradient_over"></div>
            <div class="background-image" data-background="url(img/rooms/3.jpg)"></div>
        </div>
        <div class="container">
            <div class="row move_content">
                <div class="col-lg-4 col-md-12">
                    <h5>Contacts</h5>
                    <ul>
                        <li><?php echo $contact_r['address'] ?><br><br></li>
                        <li><strong><a href="mailto:<?php echo $contact_r['email'] ?>"><?php echo $contact_r['email'] ?></a></strong></li>
                        <li><strong><a href="tel:<?php echo $contact_r['pn1'] ?>">+<?php echo $contact_r['pn1'] ?></a></strong></li>
                    </ul>
                    <div class="social">
                        <ul>
                            <li><a href="<?php echo $contact_r['insta'] ?>"><i class="bi bi-instagram"></i></a></li>
                            <li><a href="<?php echo $contact_r['tw'] ?>"><i class="bi bi-twitter-x"></i></a></li>
                            <li><a href="<?php echo $contact_r['fb'] ?>"><i class="bi bi-facebook"></i></a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 ms-lg-auto">
                    <h5>Explore</h5>
                    <div class="footer_links">
                        <ul>
                            <li><a href="index.php">Home</a></li>
                            <li><a href="about.php">About Us</a></li>
                            <li><a href="rooms.php">Rooms &amp; Suites</a></li>
                            <li><a href="contacts.php">Contacts</a></li>
                            <li><a href="terms.php">Terms and Conditions</a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div id="newsletter">
                        <h5>Newsletter</h5>
                        <div id="message-newsletter"></div>
                        <form method="post" action="assets/newsletter.php" name="newsletter_form" id="newsletter_form">
                            <div class="form-group">
                                <input type="email" name="email_newsletter" id="email_newsletter" class="form-control" placeholder="Your email">
                                <button type="submit" id="submit-newsletter"><i class="bi bi-send"></i></button>
                            </div>
                        </form>
                        <p>Receive latest offers and promos without spam. You can cancel anytime.</p>
                    </div>
                </div>
            </div>
            <!--/row-->
        </div>
        <!--/container-->
        <div class="copy">
            <div class="container">
                © Ebitare Hotel - <?php echo date('Y'); ?>
            </div>
        </div>
    </footer>
    <!-- /footer -->
   
    <div class="progress-wrap">
        <svg class="progress-circle svg-content" width="100%" height="100%" viewBox="-1 -1 102 102">
            <path d="M50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98"/>
        </svg>
    </div>
    <!-- /back to top -->

    <!-- COMMON SCRIPTS -->
    <script src="js/common_scripts.js"></script>
    <script src="js/common_functions.js"></script>
    <script src="js/datepicker_inline.js"></script>
    <script src="phpmailer/validate.js"></script>
    <script>
        // Progress bars animation
        $(function() {
            "use strict";
            var $section = $('#reviews');
            $(window).on('scroll', function(ev) {
                var scrollOffset = $(window).scrollTop();
                var containerOffset = $section.offset().top - window.innerHeight;
                if (scrollOffset > containerOffset) {
                    $(".progress-bar").each(function() {
                        var each_bar_width = $(this).attr('aria-valuenow');
                        $(this).width(each_bar_width + '%');
                    });
                }
            });
        });
        
        // Login check function from first template
        function checkLoginToBook(isLoggedIn, roomId) {
            if (isLoggedIn) {
                window.location.href = 'confirm_booking.php?id=' + roomId;
            } else {
                window.location.href = 'login.php';
            }
        }
    </script>
</body>
</html>