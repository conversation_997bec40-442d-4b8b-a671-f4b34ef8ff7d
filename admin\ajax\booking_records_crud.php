<?php
require('../inc/db_config.php');
require ('../inc/essentials.php');

adminLogin();

if (isset($_POST['get_bookings'])) {

 
    $frm_data  = filteration($_POST);

    $limit = 10;
    $page = isset($frm_data['page']) ? (int)$frm_data['page'] : 1;
    $start = ($page - 1) * $limit;
    $search = "%{$frm_data['search']}%";

    $query = "SELECT bo.*, bd.* FROM `booking_order` bo
              INNER JOIN `booking_details` bd ON bo.booking_id = bd.booking_id
              WHERE (
                    (bo.booking_status = 'booked' AND bo.arrival = 1)
                    OR (bo.booking_status = 'cancelled' AND bo.refund = 1)
                    OR (bo.booking_status = 'payment failed')
              )
              AND (
                    bo.order_id LIKE ? OR
                    bd.customer_phone LIKE ? OR
                    bd.customer_name LIKE ? OR
                    bd.customer_email LIKE ?
              )
              ORDER BY bo.booking_id DESC";

    $values = [$search, $search, $search, $search];

    // Count total matching rows
    $res = select($query, $values, 'ssss');
    $total_rows = mysqli_num_rows($res);

    if ($total_rows == 0) {
        $output = json_encode(['table_data' => "<tr><td colspan='5'><b>No Data Found</b></td></tr>", "pagination" => '']);
        echo $output;
        exit();
    }

    // Paginated result
    $query .= " LIMIT $start, $limit";
    $limit_res = select($query, $values, 'ssss');

    $i = $start + 1;
    $table_data = "";

    while ($data = mysqli_fetch_assoc($limit_res)) {
        $date = date("d-m-Y", strtotime($data['datentime']));
        $checkin = date("d-m-Y", strtotime($data['check_in']));
        $checkout = date("d-m-Y", strtotime($data['check_out']));

        $status_bg = match($data['booking_status']) {
            'booked' => 'bg-success',
            'cancelled' => 'bg-danger',
            default => 'bg-warning text-dark'
        };

        $table_data .= "
            <tr>
                <td>$i</td>
                <td>
                    <span class='badge bg-primary'>Order ID: {$data['order_id']}</span><br>
                    <b>Name:</b> {$data['customer_name']}<br>
                    <b>Email:</b> {$data['customer_email']}<br>
                    <b>Phone No:</b> {$data['customer_phone']}
                </td>
                <td><b>Room:</b> {$data['room_name']}</td>
                <td>
                    <b>Check-in:</b> $checkin<br>
                    <b>Check-out:</b> $checkout<br>
                    <b>Amount: ₦</b>{$data['trans_amt']}<br>
                    <b>Date:</b> $date
                </td>
                <td>
                <span class='badge $status_bg'>{$data['booking_status']}</span>
                </td>
                <td>
                    <button type='button' onclick='download({$data['booking_id']})' class='btn btn-outline-success btn-sm-bold fw-bold shadow-none'>
                        <i class='bi bi-file-earmark-arrow-down-fill'></i>
                    </button>
                </td>
            </tr>
        ";

        $i++;
    }

    // Pagination buttons (simple logic)
    $pagination = "";
    $total_pages = ceil($total_rows / $limit);
    for ($j = 1; $j <= $total_pages; $j++) {
        $active = ($j == $page) ? "active" : "";
        $pagination .= "<button onclick='change_page($j)' class='btn btn-sm btn-outline-dark $active'>$j</button> ";
    }

    // Output both table data and pagination as JSON
    echo json_encode(['table_data' => $table_data, 'pagination' => $pagination]);
}



if(isset($_POST['assign_room']))
{
    $frm_data = filteration($_POST);

    $query = "UPDATE `booking_order` bo INNER JOIN `booking_details` bd
    ON bo.booking_id  = bd.booking_id
    SET bo.arrival = ?, bd.room_no = ?
    WHERE bo.booking_id=?";

    $values = [1, $frm_data['room_no'], $frm_data['booking_id']];

    $res = update($query,$values, 'isi');

    echo ($res==2) ? 1:0;
}









if (isset($_POST['cancel_booking'])) {
    $frm_data = filteration($_POST);


    $query = "UPDATE `booking_order` SET `booking_status` = ?, `refund`=? WHERE `booking_id`=?";

    $values = ['cancelled', 0, $frm_data['booking_id']];
    $res = update($query,$values,'ssi');

    echo ($res > 0) ? 1 : 0;
}

if(isset($_POST['download'])) {
    $booking_id = $_POST['booking_id'];
    
    // Include TCPDF library
    require_once('../vendor/tecnickcom/tcpdf/tcpdf.php'); // Update this path if needed
    
    // Get booking data
    $query = "SELECT bo.*, bd.* FROM `booking_order` bo
              INNER JOIN `booking_details` bd ON bo.booking_id = bd.booking_id
              WHERE bo.booking_id = ?";
    
    $values = [$booking_id];
    $res = select($query, $values, 'i');
    
    if(mysqli_num_rows($res) == 0) {
        echo "Booking not found!";
        exit;
    }
    
    $data = mysqli_fetch_assoc($res);
    
    // Create new PDF document
    $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
    
    // Set document information
    $pdf->SetCreator('Hotel Booking System');
    $pdf->SetTitle('Booking Receipt - ' . $data['order_id']);
    
    // Remove default header/footer
    $pdf->setPrintHeader(false);
    $pdf->setPrintFooter(false);
    
    // Add a page
    $pdf->AddPage();
    
    // Set font
    $pdf->SetFont('helvetica', '', 12);
    
    // Booking details content
    $html = '
    <h1>Booking Receipt</h1>
    <hr>
    <h3>Booking Details</h3>
    <table cellspacing="4" cellpadding="6">
        <tr>
            <td><b>Order ID:</b></td>
            <td>'.$data['order_id'].'</td>
        </tr>
        <tr>
            <td><b>Name:</b></td>
            <td>'.$data['customer_name'].'</td>
        </tr>
        <tr>
            <td><b>Email:</b></td>
            <td>'.$data['customer_email'].'</td>
        </tr>
        <tr>
            <td><b>Phone:</b></td>
            <td>'.$data['customer_phone'].'</td>
        </tr>
        <tr>
            <td><b>Room:</b></td>
            <td>'.$data['room_name'].'</td>
        </tr>
        <tr>
            <td><b>Check-in Date:</b></td>
            <td>'.date("d-m-Y", strtotime($data['check_in'])).'</td>
        </tr>
        <tr>
            <td><b>Check-out Date:</b></td>
            <td>'.date("d-m-Y", strtotime($data['check_out'])).'</td>
        </tr>
        <tr>
            <td><b>Amount:</b></td>
            <td>₦'.$data['trans_amt'].'</td>
        </tr>
        <tr>
            <td><b>Status:</b></td>
            <td>'.$data['booking_status'].'</td>
        </tr>
    </table>
    <br><br>
    <p>Thank you for choosing our hotel!</p>
    ';
    
    // Output the HTML content
    $pdf->writeHTML($html, true, false, true, false, '');
    
    // Close and output PDF document
    $pdf->Output('booking_'.$data['order_id'].'.pdf', 'D');
    exit();
}



?>
